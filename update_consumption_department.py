#!/usr/bin/env python3
"""
Script to add department_id field to existing consumption records
"""

import json
import os
from datetime import datetime

def update_consumption_records():
    """Add department_id field to all existing consumption records"""
    
    # Load consumption data
    consumption_file = 'data/consumption.json'
    if not os.path.exists(consumption_file):
        print(f"Error: {consumption_file} not found")
        return
    
    with open(consumption_file, 'r') as f:
        consumption_data = json.load(f)
    
    # Update each consumption record to include department_id
    updated_count = 0
    for record in consumption_data:
        if 'department_id' not in record:
            # Default to Main Pharmacy (department_id: "01") for existing records
            record['department_id'] = "01"
            record['updated_at'] = datetime.now().isoformat()
            updated_count += 1
    
    # Save updated data
    with open(consumption_file, 'w') as f:
        json.dump(consumption_data, f, indent=2)
    
    print(f"Successfully updated {updated_count} consumption records with department_id field")
    print(f"All records now have department_id field (defaulted to '01' - Main Pharmacy)")

if __name__ == "__main__":
    update_consumption_records()
