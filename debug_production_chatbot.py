#!/usr/bin/env python3
"""
Production Chatbot Debugging Script
Comprehensive error analysis for https://alorfmedz.com/chatbot/ 500 error
"""

import asyncio
import json
from datetime import datetime
from playwright.async_api import async_playwright, <PERSON>, <PERSON><PERSON><PERSON>, BrowserContext

class ProductionChatbotDebugger:
    def __init__(self):
        self.production_url = "https://alorfmedz.com"
        self.chatbot_url = "https://alorfmedz.com/chatbot/"
        self.admin_credentials = {"username": "admin", "password": "@Xx123456789xX@"}
        self.browser = None
        self.context = None
        self.page = None
        self.console_logs = []
        self.network_errors = []
        self.network_requests = []
        self.javascript_errors = []
        
    async def setup_browser(self):
        """Setup Playwright browser with comprehensive logging"""
        print("🚀 Setting up browser for production debugging...")
        playwright = await async_playwright().start()
        
        # Launch browser with debugging options
        self.browser = await playwright.chromium.launch(
            headless=False,
            slow_mo=1000,
            args=[
                '--start-maximized',
                '--disable-web-security',
                '--disable-features=VizDisplayCompositor'
            ]
        )
        
        # Create context with extended permissions
        self.context = await self.browser.new_context(
            viewport={'width': 1920, 'height': 1080},
            ignore_https_errors=True,
            record_video_dir="debug_videos/",
            record_video_size={"width": 1920, "height": 1080}
        )
        
        # Create page with event listeners
        self.page = await self.context.new_page()
        
        # Set up console logging
        self.page.on("console", self.handle_console_message)
        
        # Set up error logging
        self.page.on("pageerror", self.handle_page_error)
        
        # Set up network monitoring
        self.page.on("request", self.handle_request)
        self.page.on("response", self.handle_response)
        self.page.on("requestfailed", self.handle_request_failed)
        
        # Set timeout
        self.page.set_default_timeout(60000)  # 60 seconds
        
        print("✅ Browser setup complete with comprehensive monitoring")
        return True
        
    async def handle_console_message(self, msg):
        """Handle console messages"""
        timestamp = datetime.now().strftime('%H:%M:%S.%f')[:-3]
        log_entry = {
            "timestamp": timestamp,
            "type": msg.type,
            "text": msg.text,
            "location": msg.location
        }
        self.console_logs.append(log_entry)
        print(f"🔍 Console [{msg.type.upper()}]: {msg.text}")
        
    async def handle_page_error(self, error):
        """Handle JavaScript errors"""
        timestamp = datetime.now().strftime('%H:%M:%S.%f')[:-3]
        error_entry = {
            "timestamp": timestamp,
            "message": str(error),
            "stack": getattr(error, 'stack', 'No stack trace available')
        }
        self.javascript_errors.append(error_entry)
        print(f"❌ JavaScript Error: {error}")
        
    async def handle_request(self, request):
        """Handle network requests"""
        request_entry = {
            "timestamp": datetime.now().strftime('%H:%M:%S.%f')[:-3],
            "method": request.method,
            "url": request.url,
            "headers": dict(request.headers),
            "post_data": request.post_data
        }
        self.network_requests.append(request_entry)
        
    async def handle_response(self, response):
        """Handle network responses"""
        if response.status >= 400:
            error_entry = {
                "timestamp": datetime.now().strftime('%H:%M:%S.%f')[:-3],
                "status": response.status,
                "status_text": response.status_text,
                "url": response.url,
                "headers": dict(response.headers)
            }
            self.network_errors.append(error_entry)
            print(f"🚨 Network Error [{response.status}]: {response.url}")
            
    async def handle_request_failed(self, request):
        """Handle failed requests"""
        failure_entry = {
            "timestamp": datetime.now().strftime('%H:%M:%S.%f')[:-3],
            "method": request.method,
            "url": request.url,
            "failure": request.failure
        }
        self.network_errors.append(failure_entry)
        print(f"💥 Request Failed: {request.url} - {request.failure}")
        
    async def login_to_production(self):
        """Login to production environment"""
        print("🔐 Attempting login to production...")
        
        try:
            # Navigate to production login
            await self.page.goto(self.production_url)
            await self.page.wait_for_load_state('networkidle')
            
            # Check if already logged in
            if "/dashboard" in self.page.url:
                print("✅ Already logged in to production")
                return True
            
            # Fill login form
            await self.page.fill('input[name="username"]', self.admin_credentials["username"])
            await self.page.fill('input[name="password"]', self.admin_credentials["password"])
            
            # Submit form
            await self.page.click('button[type="submit"]')
            await self.page.wait_for_load_state('networkidle')
            
            # Verify login
            if "/dashboard" in self.page.url:
                print("✅ Successfully logged in to production")
                return True
            else:
                print("❌ Login failed - checking for error messages")
                error_msg = await self.page.query_selector('.alert-danger')
                if error_msg:
                    error_text = await error_msg.text_content()
                    print(f"❌ Login error: {error_text}")
                return False
                
        except Exception as e:
            print(f"❌ Login exception: {str(e)}")
            return False
    
    async def debug_chatbot_page(self):
        """Debug the chatbot page specifically"""
        print("\n🤖 DEBUGGING CHATBOT PAGE")
        print("=" * 60)
        
        try:
            # Clear previous logs
            self.console_logs.clear()
            self.network_errors.clear()
            self.javascript_errors.clear()
            
            print(f"🌐 Navigating to: {self.chatbot_url}")
            
            # Navigate to chatbot page
            response = await self.page.goto(self.chatbot_url, wait_until='networkidle')
            
            print(f"📊 Initial response status: {response.status}")
            
            # Wait a bit for any async operations
            await self.page.wait_for_timeout(5000)
            
            # Take screenshot
            screenshot_path = f"chatbot_error_{datetime.now().strftime('%Y%m%d_%H%M%S')}.png"
            await self.page.screenshot(path=screenshot_path, full_page=True)
            print(f"📸 Screenshot saved: {screenshot_path}")
            
            # Check page content
            page_title = await self.page.title()
            page_url = self.page.url
            
            print(f"📄 Page title: {page_title}")
            print(f"🔗 Final URL: {page_url}")
            
            # Look for error messages
            error_elements = await self.page.query_selector_all('.alert-danger, .error, .alert-error')
            if error_elements:
                print("\n🚨 ERROR MESSAGES FOUND:")
                for i, element in enumerate(error_elements):
                    error_text = await element.text_content()
                    print(f"   Error {i+1}: {error_text}")
            
            # Check for 500 error page content
            body_text = await self.page.text_content('body')
            if "500" in body_text or "Internal Server Error" in body_text:
                print("🚨 500 Internal Server Error detected in page content")
            
            # Check for specific chatbot elements
            chatbot_container = await self.page.query_selector('#chatbot, .chatbot, .chat-container')
            if chatbot_container:
                print("✅ Chatbot container found")
            else:
                print("❌ Chatbot container not found")
            
            # Check for JavaScript files
            script_tags = await self.page.query_selector_all('script[src]')
            print(f"\n📜 Found {len(script_tags)} external scripts")
            for script in script_tags:
                src = await script.get_attribute('src')
                print(f"   Script: {src}")
            
            return True
            
        except Exception as e:
            print(f"❌ Chatbot debugging error: {str(e)}")
            return False
    
    async def analyze_network_traffic(self):
        """Analyze network traffic for issues"""
        print("\n🌐 NETWORK TRAFFIC ANALYSIS")
        print("=" * 60)
        
        # Analyze failed requests
        if self.network_errors:
            print(f"🚨 Found {len(self.network_errors)} network errors:")
            for i, error in enumerate(self.network_errors):
                print(f"\n   Error {i+1}:")
                print(f"   ⏰ Time: {error['timestamp']}")
                print(f"   🔗 URL: {error.get('url', 'Unknown')}")
                print(f"   📊 Status: {error.get('status', 'Failed')}")
                if 'failure' in error:
                    print(f"   💥 Failure: {error['failure']}")
        else:
            print("✅ No network errors detected")
        
        # Analyze chatbot-specific requests
        chatbot_requests = [req for req in self.network_requests if 'chatbot' in req['url'].lower()]
        if chatbot_requests:
            print(f"\n🤖 Found {len(chatbot_requests)} chatbot-related requests:")
            for req in chatbot_requests:
                print(f"   {req['method']} {req['url']}")
        else:
            print("\n⚠️ No chatbot-specific requests found")
    
    async def analyze_console_logs(self):
        """Analyze console logs for errors"""
        print("\n📋 CONSOLE LOG ANALYSIS")
        print("=" * 60)
        
        if self.console_logs:
            error_logs = [log for log in self.console_logs if log['type'] in ['error', 'warning']]
            if error_logs:
                print(f"🚨 Found {len(error_logs)} console errors/warnings:")
                for log in error_logs:
                    print(f"\n   [{log['type'].upper()}] {log['timestamp']}")
                    print(f"   📝 Message: {log['text']}")
                    if log['location']:
                        print(f"   📍 Location: {log['location']}")
            else:
                print("✅ No console errors found")
                
            # Show all logs for debugging
            print(f"\n📊 All console messages ({len(self.console_logs)} total):")
            for log in self.console_logs[-10:]:  # Show last 10
                print(f"   [{log['type']}] {log['text']}")
        else:
            print("⚠️ No console logs captured")
    
    async def analyze_javascript_errors(self):
        """Analyze JavaScript errors"""
        print("\n⚡ JAVASCRIPT ERROR ANALYSIS")
        print("=" * 60)
        
        if self.javascript_errors:
            print(f"🚨 Found {len(self.javascript_errors)} JavaScript errors:")
            for i, error in enumerate(self.javascript_errors):
                print(f"\n   Error {i+1} at {error['timestamp']}:")
                print(f"   📝 Message: {error['message']}")
                print(f"   📚 Stack: {error['stack']}")
        else:
            print("✅ No JavaScript errors detected")
    
    async def generate_debug_report(self):
        """Generate comprehensive debug report"""
        print("\n📊 GENERATING COMPREHENSIVE DEBUG REPORT")
        print("=" * 80)
        
        report = {
            "timestamp": datetime.now().isoformat(),
            "url_tested": self.chatbot_url,
            "console_logs": self.console_logs,
            "network_errors": self.network_errors,
            "javascript_errors": self.javascript_errors,
            "network_requests": [req for req in self.network_requests if 'chatbot' in req['url'].lower()]
        }
        
        # Save detailed report
        report_filename = f"chatbot_debug_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(report_filename, 'w') as f:
            json.dump(report, f, indent=2)
        
        print(f"💾 Detailed report saved: {report_filename}")
        
        # Print summary
        print("\n🎯 DEBUG SUMMARY:")
        print(f"   Console Logs: {len(self.console_logs)}")
        print(f"   Network Errors: {len(self.network_errors)}")
        print(f"   JavaScript Errors: {len(self.javascript_errors)}")
        print(f"   Chatbot Requests: {len([req for req in self.network_requests if 'chatbot' in req['url'].lower()])}")
        
        return report_filename
    
    async def cleanup(self):
        """Cleanup browser resources"""
        if self.context:
            await self.context.close()
        if self.browser:
            await self.browser.close()
        print("\n🧹 Browser cleanup complete")

async def main():
    """Main debugging execution"""
    print("🔍 PRODUCTION CHATBOT DEBUGGING SUITE")
    print("=" * 80)
    print(f"Target URL: https://alorfmedz.com/chatbot/")
    print(f"Debug started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 80)
    
    debugger = ProductionChatbotDebugger()
    
    try:
        # Setup browser
        await debugger.setup_browser()
        
        # Login to production
        login_success = await debugger.login_to_production()
        if not login_success:
            print("⚠️ Proceeding without login - testing public access")
        
        # Debug chatbot page
        await debugger.debug_chatbot_page()
        
        # Analyze results
        await debugger.analyze_network_traffic()
        await debugger.analyze_console_logs()
        await debugger.analyze_javascript_errors()
        
        # Generate report
        report_file = await debugger.generate_debug_report()
        
        print(f"\n✅ Debugging complete! Check {report_file} for detailed analysis.")
        
    except Exception as e:
        print(f"❌ Debug suite error: {str(e)}")
    
    finally:
        await debugger.cleanup()

if __name__ == "__main__":
    asyncio.run(main())
