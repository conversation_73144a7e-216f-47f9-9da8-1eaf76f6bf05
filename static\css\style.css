/* Hospital Pharmacy Management System - Custom Styles */

:root {
    --primary-color: #0d6efd;
    --secondary-color: #6c757d;
    --success-color: #198754;
    --danger-color: #dc3545;
    --warning-color: #ffc107;
    --info-color: #0dcaf0;
    --sidebar-width: 250px;
    --sidebar-collapsed-width: 60px;
}

/* Dark theme variables */
[data-bs-theme="dark"] {
    --bs-body-bg: #212529;
    --bs-body-color: #dee2e6;
    --sidebar-bg: #1a1d20;
    --sidebar-border: #495057;
}

/* Light theme sidebar variables */
[data-bs-theme="light"] {
    --sidebar-bg: #ffffff;
    --sidebar-border: #dee2e6;
    --sidebar-text: #495057;
    --sidebar-text-hover: #0d6efd;
    --sidebar-active-bg: #e7f1ff;
}

/* General Styles */
body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.6;
    margin: 0;
    padding: 0;
}

/* Sidebar Styles */
.sidebar {
    position: fixed;
    top: 0;
    left: 0;
    height: 100vh;
    width: var(--sidebar-width);
    background-color: var(--sidebar-bg);
    border-right: 1px solid var(--sidebar-border);
    z-index: 1000;
    transition: transform 0.3s ease-in-out;
    overflow-y: auto;
    box-shadow: 2px 0 5px rgba(0,0,0,0.1);
}

.sidebar-header {
    padding: 1rem;
    border-bottom: 1px solid var(--sidebar-border);
    background-color: var(--primary-color);
    color: white;
}

.sidebar-brand {
    font-weight: bold;
    font-size: 1.25rem;
    color: white;
    text-decoration: none;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.sidebar-brand:hover {
    color: white;
    text-decoration: none;
}

.sidebar-nav {
    padding: 0;
    margin: 0;
    list-style: none;
}

.sidebar-nav-item {
    margin: 0;
}

.sidebar-nav-link {
    display: flex;
    align-items: center;
    padding: 0.75rem 1rem;
    color: var(--sidebar-text);
    text-decoration: none;
    transition: all 0.2s ease;
    border-left: 3px solid transparent;
}

.sidebar-nav-link:hover {
    background-color: var(--sidebar-active-bg);
    color: var(--sidebar-text-hover);
    text-decoration: none;
    border-left-color: var(--primary-color);
}

.sidebar-nav-link.active {
    background-color: var(--sidebar-active-bg);
    color: var(--primary-color);
    border-left-color: var(--primary-color);
    font-weight: 600;
}

.sidebar-nav-link i {
    width: 20px;
    margin-right: 0.75rem;
    text-align: center;
}

.sidebar-footer {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    border-top: 1px solid var(--sidebar-border);
    background-color: var(--sidebar-bg);
}

.sidebar-user {
    padding: 1rem;
}

.sidebar-user-dropdown {
    width: 100%;
    background: none;
    border: none;
    color: var(--sidebar-text);
    text-align: left;
    padding: 0.5rem;
    border-radius: 0.375rem;
    transition: background-color 0.2s ease;
}

.sidebar-user-dropdown:hover {
    background-color: var(--sidebar-active-bg);
    color: var(--sidebar-text-hover);
}

/* Main Content Area */
.main-content {
    margin-left: var(--sidebar-width);
    min-height: 100vh;
    transition: margin-left 0.3s ease-in-out;
    width: calc(100% - var(--sidebar-width));
}

.content-wrapper {
    padding: 1.5rem;
    width: 100%;
    max-width: none;
}

/* Mobile Toggle Button */
.sidebar-toggle {
    display: none;
    position: fixed;
    top: 1rem;
    left: 1rem;
    z-index: 1001;
    background-color: var(--primary-color);
    color: white;
    border: none;
    border-radius: 0.375rem;
    padding: 0.5rem;
    box-shadow: 0 2px 5px rgba(0,0,0,0.2);
}

.sidebar-toggle:hover {
    background-color: var(--primary-color);
    opacity: 0.9;
}

/* Sidebar Overlay for Mobile */
.sidebar-overlay {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 999;
}

/* Cards */
.card {
    border: none;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    transition: box-shadow 0.15s ease-in-out;
    width: 100%;
}

.card:hover {
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
}

/* Tables */
.table {
    margin-bottom: 0;
}

.table th {
    border-top: none;
    font-weight: 600;
    background-color: var(--bs-light);
}

/* Stock status colors */
.stock-danger {
    background-color: var(--danger-color) !important;
    color: white;
}

.stock-warning {
    background-color: var(--warning-color) !important;
    color: black;
}

.stock-success {
    background-color: var(--success-color) !important;
    color: white;
}

/* Dashboard widgets */
.dashboard-widget {
    background: linear-gradient(135deg, var(--primary-color), var(--info-color));
    color: white;
    border-radius: 10px;
    padding: 1.5rem;
    margin-bottom: 1rem;
}

.dashboard-widget .widget-icon {
    font-size: 3rem;
    opacity: 0.8;
}

.dashboard-widget .widget-number {
    font-size: 2.5rem;
    font-weight: bold;
}

/* Forms */
.form-control:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
}

/* Buttons */
.btn {
    border-radius: 6px;
    font-weight: 500;
}

.btn-sm {
    padding: 0.25rem 0.5rem;
    font-size: 0.875rem;
}

/* Action buttons */
.action-buttons {
    white-space: nowrap;
}

.action-buttons .btn {
    margin-right: 0.25rem;
}

/* Login page */
.login-container {
    min-height: 100vh;
    display: flex;
    align-items: center;
    background: linear-gradient(135deg, var(--primary-color), var(--info-color));
}

/* Footer */
footer {
    margin-top: auto;
}

/* Responsive adjustments */
@media (max-width: 992px) {
    .sidebar {
        transform: translateX(-100%);
    }

    .sidebar.show {
        transform: translateX(0);
    }

    .sidebar-toggle {
        display: block;
    }

    .sidebar-overlay.show {
        display: block;
    }

    .main-content {
        margin-left: 0;
        width: 100%;
    }

    .content-wrapper {
        padding: 4rem 1rem 1rem 1rem;
    }
}

@media (max-width: 768px) {
    .table-responsive {
        font-size: 0.875rem;
    }

    .action-buttons .btn {
        margin-bottom: 0.25rem;
        font-size: 0.8rem;
        padding: 0.375rem 0.75rem;
    }

    .dashboard-widget {
        text-align: center;
        margin-bottom: 1rem;
    }

    .dashboard-widget .widget-number {
        font-size: 2rem;
    }

    .content-wrapper {
        padding: 4rem 0.75rem 1rem 0.75rem;
    }

    .card {
        margin-bottom: 1rem;
    }

    .sidebar {
        width: 280px;
    }
}

@media (max-width: 576px) {
    .content-wrapper {
        padding: 4rem 0.5rem 1rem 0.5rem;
    }

    .dashboard-widget .widget-number {
        font-size: 1.75rem;
    }

    .sidebar-nav-link {
        padding: 1rem;
        font-size: 0.95rem;
    }

    .table-responsive {
        font-size: 0.8rem;
    }
}

/* Loading spinner */
.spinner-border-sm {
    width: 1rem;
    height: 1rem;
}

/* Custom scrollbar */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
    background: #888;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: #555;
}

/* Full-width layout overrides */
.container,
.container-fluid,
.container-sm,
.container-md,
.container-lg,
.container-xl,
.container-xxl {
    max-width: none !important;
    width: 100% !important;
    padding-left: 0 !important;
    padding-right: 0 !important;
}

/* Table improvements for full-width */
.table-responsive {
    width: 100%;
    overflow-x: auto;
}

.table {
    width: 100%;
    margin-bottom: 1rem;
}

/* Print styles */
@media print {
    .sidebar,
    .sidebar-toggle,
    .sidebar-overlay,
    .btn,
    .action-buttons,
    footer {
        display: none !important;
    }

    .main-content {
        margin-left: 0 !important;
        width: 100% !important;
    }

    .content-wrapper {
        padding: 0 !important;
        margin: 0 !important;
    }
}

/* Dark theme sidebar adjustments */
[data-bs-theme="dark"] .sidebar {
    --sidebar-bg: #1a1d20;
    --sidebar-border: #495057;
    --sidebar-text: #dee2e6;
    --sidebar-text-hover: #ffffff;
    --sidebar-active-bg: #495057;
}

[data-bs-theme="dark"] .sidebar-header {
    background-color: var(--primary-color);
}

/* Animation improvements */
.sidebar,
.main-content {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.sidebar-nav-link {
    transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Focus styles for accessibility */
.sidebar-nav-link:focus,
.sidebar-toggle:focus,
.sidebar-user-dropdown:focus {
    outline: 2px solid var(--primary-color);
    outline-offset: 2px;
}
