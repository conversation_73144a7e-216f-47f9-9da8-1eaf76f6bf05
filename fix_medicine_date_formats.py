#!/usr/bin/env python3
"""
Fix Medicine Date Format Issues:
1. Update frontend table view to display dates as dd-mm-yyyy
2. Update import template to use dd-mm-yyyy format
3. Update import function to accept both dd-mm-yyyy and yyyy-mm-dd formats
4. Add date format conversion utilities
"""

import os
import re

def fix_medicine_index_template():
    """Fix the medicine index template to display dates as dd-mm-yyyy"""
    template_path = 'templates/medicines/index.html'
    
    print("Fixing medicine index template date display...")
    
    with open(template_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Find the expiry date display section and add date formatting
    old_pattern = r'{{ medicine\.expiry_date }}'
    new_pattern = r'{{ medicine.expiry_date | format_date_display if medicine.expiry_date else "" }}'
    
    content = re.sub(old_pattern, new_pattern, content)
    
    with open(template_path, 'w', encoding='utf-8') as f:
        f.write(content)
    
    print("✅ Updated medicine index template")

def fix_medicine_blueprint():
    """Fix the medicine blueprint to handle both date formats and add template filter"""
    blueprint_path = 'blueprints/medicines.py'
    
    print("Fixing medicine blueprint date handling...")
    
    with open(blueprint_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Add date format conversion function after imports
    date_utils_code = '''
def convert_date_format(date_str):
    """Convert date from dd-mm-yyyy or dd/mm/yyyy to yyyy-mm-dd format"""
    if not date_str or not date_str.strip():
        return None
    
    date_str = date_str.strip()
    
    # If already in yyyy-mm-dd format, return as is
    if re.match(r'\\d{4}-\\d{2}-\\d{2}', date_str):
        return date_str
    
    # Convert dd-mm-yyyy or dd/mm/yyyy to yyyy-mm-dd
    if re.match(r'\\d{1,2}[-/]\\d{1,2}[-/]\\d{4}', date_str):
        parts = re.split(r'[-/]', date_str)
        if len(parts) == 3:
            day, month, year = parts
            return f"{year}-{month.zfill(2)}-{day.zfill(2)}"
    
    return date_str

def format_date_display(date_str):
    """Convert date from yyyy-mm-dd to dd-mm-yyyy for display"""
    if not date_str:
        return ""
    
    if re.match(r'\\d{4}-\\d{2}-\\d{2}', date_str):
        year, month, day = date_str.split('-')
        return f"{day}-{month}-{year}"
    
    return date_str

'''
    
    # Insert the date utility functions after the imports
    import_end = content.find('medicines_bp = Blueprint')
    if import_end != -1:
        content = content[:import_end] + date_utils_code + content[import_end:]
    
    # Update CSV import validation to handle both formats
    old_validation = '''                # Validate expiry date if provided
                expiry_date = row.get('expiry_date', '').strip()
                if expiry_date:
                    try:
                        datetime.strptime(expiry_date, '%Y-%m-%d')
                    except ValueError:
                        errors.append(f'Row {row_num}: Invalid expiry date format. Use YYYY-MM-DD')
                        continue'''
    
    new_validation = '''                # Validate and convert expiry date if provided
                expiry_date = row.get('expiry_date', '').strip()
                if expiry_date:
                    converted_date = convert_date_format(expiry_date)
                    if converted_date:
                        try:
                            datetime.strptime(converted_date, '%Y-%m-%d')
                            expiry_date = converted_date
                        except ValueError:
                            errors.append(f'Row {row_num}: Invalid expiry date format. Use DD-MM-YYYY or YYYY-MM-DD')
                            continue
                    else:
                        errors.append(f'Row {row_num}: Invalid expiry date format. Use DD-MM-YYYY or YYYY-MM-DD')
                        continue'''
    
    content = content.replace(old_validation, new_validation)
    
    # Add template filter registration
    filter_registration = '''
# Register template filter
@medicines_bp.app_template_filter('format_date_display')
def format_date_display_filter(date_str):
    return format_date_display(date_str)

'''
    
    # Add filter registration before the first route
    first_route = content.find('@medicines_bp.route')
    if first_route != -1:
        content = content[:first_route] + filter_registration + content[first_route:]
    
    with open(blueprint_path, 'w', encoding='utf-8') as f:
        f.write(content)
    
    print("✅ Updated medicine blueprint with date handling")

def fix_import_template():
    """Fix the CSV import template to use dd-mm-yyyy format"""
    blueprint_path = 'blueprints/medicines.py'
    
    print("Fixing CSV import template format...")
    
    with open(blueprint_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Update template comments and examples
    old_template = '''    csv_content += '# Expiry date format: YYYY-MM-DD (optional)\\n'
    csv_content += '# Example with supplier_id: Paracetamol,01,,Tablet 500mg,50,Pain relief medication,2025-12-31,BATCH001,1234567890123\\n'
    csv_content += '# Example with supplier_name: Ibuprofen,,PharmaCorp International,Syrup 200ml,30,Anti-inflammatory,2026-06-15,BATCH002,9876543210987\\n' '''
    
    new_template = '''    csv_content += '# Expiry date format: DD-MM-YYYY (optional)\\n'
    csv_content += '# Example with supplier_id: Paracetamol,01,,Tablet 500mg,50,Pain relief medication,31-12-2025,BATCH001,1234567890123\\n'
    csv_content += '# Example with supplier_name: Ibuprofen,,PharmaCorp International,Syrup 200ml,30,Anti-inflammatory,15-06-2026,BATCH002,9876543210987\\n' '''
    
    content = content.replace(old_template, new_template)
    
    with open(blueprint_path, 'w', encoding='utf-8') as f:
        f.write(content)
    
    print("✅ Updated CSV import template format")

def main():
    print("Fixing medicine date format issues...")
    
    # Fix frontend table display
    fix_medicine_index_template()
    
    # Fix backend date handling and add template filter
    fix_medicine_blueprint()
    
    # Fix import template format
    fix_import_template()
    
    print("\n✅ All medicine date format fixes completed!")
    print("\nChanges made:")
    print("1. Frontend table now displays dates as dd-mm-yyyy")
    print("2. CSV import template now uses dd-mm-yyyy format")
    print("3. Import function accepts both dd-mm-yyyy and yyyy-mm-dd formats")
    print("4. Added date format conversion utilities")
    print("5. Added template filter for date display formatting")

if __name__ == "__main__":
    main()
