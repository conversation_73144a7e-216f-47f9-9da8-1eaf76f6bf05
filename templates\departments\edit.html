{% extends "base.html" %}

{% block title %}Edit Department - Hospital Pharmacy Management System{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1><i class="bi bi-pencil"></i> Edit Department</h1>
    <a href="{{ url_for('departments.index') }}" class="btn btn-outline-secondary">
        <i class="bi bi-arrow-left"></i> Back to Departments
    </a>
</div>

<div class="row justify-content-center">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">Department Information - ID: {{ department.id }}</h5>
            </div>
            <div class="card-body">
                <form method="POST" novalidate>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="name" class="form-label">Department Name <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="name" name="name" 
                                       value="{{ department.name }}" required>
                                <div class="invalid-feedback">
                                    Please provide a department name.
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="responsible_person" class="form-label">Responsible Person <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="responsible_person" name="responsible_person" 
                                       value="{{ department.responsible_person }}" required>
                                <div class="invalid-feedback">
                                    Please provide a responsible person.
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="telephone" class="form-label">Telephone <span class="text-danger">*</span></label>
                                <input type="tel" class="form-control" id="telephone" name="telephone" 
                                       value="{{ department.telephone }}" placeholder="+1234567890" required>
                                <div class="invalid-feedback">
                                    Please provide a telephone number.
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="notes" class="form-label">Notes</label>
                        <textarea class="form-control" id="notes" name="notes" rows="3" 
                                  placeholder="Additional information about the department...">{{ department.notes }}</textarea>
                    </div>
                    
                    <div class="d-flex justify-content-end gap-2">
                        <a href="{{ url_for('departments.index') }}" class="btn btn-secondary">
                            <i class="bi bi-x-circle"></i> Cancel
                        </a>
                        <button type="submit" class="btn btn-primary">
                            <i class="bi bi-check-circle"></i> Update Department
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// Form validation
(function() {
    'use strict';
    window.addEventListener('load', function() {
        var forms = document.getElementsByTagName('form');
        var validation = Array.prototype.filter.call(forms, function(form) {
            form.addEventListener('submit', function(event) {
                if (form.checkValidity() === false) {
                    event.preventDefault();
                    event.stopPropagation();
                }
                form.classList.add('was-validated');
            }, false);
        });
    }, false);
})();
</script>
{% endblock %}
