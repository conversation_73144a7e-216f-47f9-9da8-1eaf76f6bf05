#!/usr/bin/env python3
"""
Manual Production Testing Script
Simple script to manually test production site and compare with local features
"""

import asyncio
import json
from datetime import datetime
from playwright.async_api import async_playwright

async def manual_production_test():
    """Manual production testing with step-by-step verification"""
    
    async with async_playwright() as p:
        browser = await p.chromium.launch(headless=False, slow_mo=2000)
        context = await browser.new_context(viewport={'width': 1920, 'height': 1080})
        page = await context.new_page()
        
        print("🔍 Manual Production Testing Started")
        print("📋 Please follow the prompts and verify each step manually")
        
        try:
            # Step 1: Navigate to production site
            print("\n📡 Step 1: Navigating to production site...")
            await page.goto("https://alorfmedz.com", wait_until='networkidle')
            await page.screenshot(path="manual_test_1_homepage.png", full_page=True)
            print("✅ Screenshot saved: manual_test_1_homepage.png")
            
            input("Press Enter after you've verified the homepage loads correctly...")
            
            # Step 2: Login attempt
            print("\n🔐 Step 2: Attempting login...")
            
            # Fill login form with correct credentials
            await page.fill('input[name="username"]', 'admin')
            await page.fill('input[name="password"]', '@Xx123456789xX@')
            await page.screenshot(path="manual_test_2_login_filled.png")
            print("✅ Screenshot saved: manual_test_2_login_filled.png")
            
            # Submit login
            await page.click('button[type="submit"]')
            await page.wait_for_timeout(3000)
            await page.screenshot(path="manual_test_3_after_login.png", full_page=True)
            print("✅ Screenshot saved: manual_test_3_after_login.png")
            
            current_url = page.url
            print(f"📍 Current URL after login: {current_url}")
            
            login_success = input("Did login succeed? (y/n): ").lower() == 'y'
            
            if login_success:
                print("\n✅ Login successful - proceeding with feature testing")
                
                # Step 3: Test Purchases page
                print("\n🛒 Step 3: Testing Purchases page...")
                try:
                    await page.click('a[href*="purchase"], a:has-text("Purchase")')
                    await page.wait_for_timeout(3000)
                    await page.screenshot(path="manual_test_4_purchases.png", full_page=True)
                    print("✅ Screenshot saved: manual_test_4_purchases.png")
                    
                    # Check for enhanced columns
                    print("\n🔍 Checking for enhanced purchase management features:")
                    
                    features_to_check = [
                        ("Status Column", 'th:has-text("Status")'),
                        ("Delivery Date Column", 'th:has-text("Delivery Date")'),
                        ("Received By Column", 'th:has-text("Received By")'),
                        ("Notes Column", 'th:has-text("Notes")'),
                        ("Status Filter", 'select option:has-text("Complete")'),
                        ("Purchaser Filter", 'select option:has-text("Waleed")')
                    ]
                    
                    for feature_name, selector in features_to_check:
                        try:
                            element = await page.query_selector(selector)
                            if element:
                                print(f"✅ {feature_name}: FOUND")
                            else:
                                print(f"❌ {feature_name}: MISSING")
                        except:
                            print(f"❌ {feature_name}: ERROR")
                    
                    input("Press Enter after you've verified the purchases page features...")
                    
                except Exception as e:
                    print(f"❌ Error accessing purchases page: {e}")
                
                # Step 4: Test Reports page
                print("\n📊 Step 4: Testing Reports page...")
                try:
                    await page.click('a[href*="report"], a:has-text("Report")')
                    await page.wait_for_timeout(3000)
                    await page.screenshot(path="manual_test_5_reports.png", full_page=True)
                    print("✅ Screenshot saved: manual_test_5_reports.png")
                    
                    # Check for report enhancements
                    print("\n🔍 Checking for enhanced report features:")
                    
                    report_features = [
                        ("Row Numbering", 'th:has-text("#")'),
                        ("Enhanced Filtering", 'select, input[type="date"]'),
                        ("Medicine Names", 'td:has-text("Paracetamol")'),
                        ("Consumption Totals", 'tr:has-text("Total")')
                    ]
                    
                    for feature_name, selector in report_features:
                        try:
                            element = await page.query_selector(selector)
                            if element:
                                print(f"✅ {feature_name}: FOUND")
                            else:
                                print(f"❌ {feature_name}: MISSING")
                        except:
                            print(f"❌ {feature_name}: ERROR")
                    
                    input("Press Enter after you've verified the reports page features...")
                    
                except Exception as e:
                    print(f"❌ Error accessing reports page: {e}")
                
                # Step 5: Test specific report
                print("\n📋 Step 5: Testing specific report (Purchase Report)...")
                try:
                    # Try to access purchase report directly
                    await page.goto("https://alorfmedz.com/reports/purchase", wait_until='networkidle')
                    await page.wait_for_timeout(3000)
                    await page.screenshot(path="manual_test_6_purchase_report.png", full_page=True)
                    print("✅ Screenshot saved: manual_test_6_purchase_report.png")
                    
                    input("Press Enter after you've verified the purchase report features...")
                    
                except Exception as e:
                    print(f"❌ Error accessing purchase report: {e}")
                
            else:
                print("\n❌ Login failed - cannot test authenticated features")
                print("🔍 This indicates a potential deployment issue")
            
            # Step 6: Generate summary
            print("\n📋 Step 6: Generating test summary...")
            
            summary = {
                "timestamp": datetime.now().isoformat(),
                "production_url": "https://alorfmedz.com",
                "login_successful": login_success,
                "screenshots_captured": [
                    "manual_test_1_homepage.png",
                    "manual_test_2_login_filled.png", 
                    "manual_test_3_after_login.png"
                ]
            }
            
            if login_success:
                summary["screenshots_captured"].extend([
                    "manual_test_4_purchases.png",
                    "manual_test_5_reports.png",
                    "manual_test_6_purchase_report.png"
                ])
            
            with open("manual_production_test_summary.json", "w") as f:
                json.dump(summary, f, indent=2)
            
            print("✅ Manual test summary saved to manual_production_test_summary.json")
            print("✅ All screenshots saved for analysis")
            
        except Exception as e:
            print(f"❌ Critical error during manual testing: {e}")
            
        finally:
            print("\n🎯 Manual testing completed!")
            print("📸 Review the screenshots to compare with local implementation")
            input("Press Enter to close browser...")
            await browser.close()

if __name__ == "__main__":
    asyncio.run(manual_production_test())
