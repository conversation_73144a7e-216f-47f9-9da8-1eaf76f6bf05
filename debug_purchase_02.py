#!/usr/bin/env python3
"""
Debug Purchase #02 Issue
Detailed debugging of purchase #02 editing problem
"""

import asyncio
from playwright.async_api import async_playwright

async def debug_purchase_02():
    """Debug purchase #02 editing issue"""
    
    async with async_playwright() as p:
        browser = await p.chromium.launch(headless=False, slow_mo=1000)
        context = await browser.new_context(viewport={'width': 1920, 'height': 1080})
        page = await context.new_page()
        
        # Capture console errors
        console_errors = []
        page.on('console', lambda msg: console_errors.append(msg.text) if msg.type == 'error' else None)
        
        try:
            print("🔍 Debugging Purchase #02 Issue...")
            
            # Login
            await page.goto("http://localhost:5000", wait_until='networkidle')
            await page.fill('input[name="username"]', 'admin')
            await page.fill('input[name="password"]', '@Xx123456789xX@')
            await page.click('button[type="submit"]')
            await page.wait_for_timeout(2000)
            
            # Navigate to purchase #02 edit directly
            print("\n📝 Navigating directly to purchase #02 edit...")
            await page.goto("http://localhost:5000/purchases/edit/02", wait_until='networkidle')
            await page.wait_for_timeout(2000)
            
            # Check if we're on the edit page
            current_url = page.url
            print(f"📍 Current URL: {current_url}")
            
            if 'edit/02' in current_url:
                print("✅ Successfully reached edit page")
                
                # Check the form content
                print("\n🔍 Analyzing form content...")
                
                # Check medicines section
                medicines_section = await page.query_selector('#medicines-container')
                if medicines_section:
                    medicines_html = await medicines_section.inner_html()
                    print("📋 Medicines section found")
                    
                    # Count medicine rows
                    medicine_rows = await page.query_selector_all('.medicine-row')
                    print(f"🧪 Medicine rows count: {len(medicine_rows)}")
                    
                    if len(medicine_rows) == 0:
                        print("⚠️ No medicine rows found - this might be the issue!")
                        
                        # Add a medicine row to test
                        print("➕ Adding a medicine row for testing...")
                        add_button = await page.query_selector('button[onclick="addMedicineRow()"]')
                        if add_button:
                            await add_button.click()
                            await page.wait_for_timeout(1000)
                            
                            # Fill in the new medicine row
                            medicine_select = await page.query_selector('select[name="medicine_id[]"]')
                            quantity_input = await page.query_selector('input[name="quantity[]"]')
                            
                            if medicine_select and quantity_input:
                                # Select first medicine
                                await medicine_select.select_option(index=1)
                                await quantity_input.fill('10')
                                print("✅ Added test medicine row")
                else:
                    print("❌ Medicines section not found")
                
                # Check other form fields
                status_field = await page.query_selector('select[name="status"]')
                if status_field:
                    status_value = await status_field.input_value()
                    print(f"📊 Current status: {status_value}")
                
                # Try to submit the form
                print("\n💾 Attempting form submission...")
                submit_button = await page.query_selector('button[type="submit"]')
                if submit_button:
                    await submit_button.click()
                    await page.wait_for_timeout(5000)
                    
                    # Check result
                    new_url = page.url
                    print(f"📍 URL after submit: {new_url}")
                    
                    if 'edit' not in new_url:
                        print("✅ Form submission successful")
                    else:
                        print("❌ Form submission failed")
                        
                        # Check for error messages
                        error_alerts = await page.query_selector_all('.alert-danger')
                        for alert in error_alerts:
                            error_text = await alert.text_content()
                            print(f"❌ Error: {error_text}")
                        
                        # Check page content for errors
                        page_content = await page.content()
                        if 'error' in page_content.lower():
                            print("⚠️ Error detected in page content")
                
            else:
                print("❌ Failed to reach edit page")
                page_content = await page.content()
                if '500' in page_content:
                    print("❌ 500 Internal Server Error")
                elif '404' in page_content:
                    print("❌ 404 Not Found")
            
            # Print console errors
            if console_errors:
                print(f"\n🚨 Console Errors ({len(console_errors)}):")
                for error in console_errors:
                    print(f"   - {error}")
            else:
                print("\n✅ No console errors detected")
                
        except Exception as e:
            print(f"❌ Critical error: {str(e)}")
            
        finally:
            await browser.close()

if __name__ == "__main__":
    asyncio.run(debug_purchase_02())
