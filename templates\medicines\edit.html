{% extends "base.html" %}

{% block title %}Edit Medicine - Hospital Pharmacy Management System{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1><i class="bi bi-pencil"></i> Edit Medicine</h1>
    <a href="{{ url_for('medicines.index') }}" class="btn btn-outline-secondary">
        <i class="bi bi-arrow-left"></i> Back to Medicines
    </a>
</div>

<div class="row justify-content-center">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">Medicine Information - ID: {{ medicine.id }}</h5>
            </div>
            <div class="card-body">
                <form method="POST" novalidate>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="name" class="form-label">Medicine Name <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="name" name="name" 
                                       value="{{ medicine.name }}" required>
                                <div class="invalid-feedback">
                                    Please provide a medicine name.
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="supplier_id" class="form-label">Supplier <span class="text-danger">*</span></label>
                                <select class="form-select" id="supplier_id" name="supplier_id" required>
                                    <option value="">Select Supplier</option>
                                    {% for supplier in suppliers %}
                                    <option value="{{ supplier.id }}" 
                                            {% if supplier.id == medicine.supplier_id %}selected{% endif %}>
                                        {{ supplier.name }}
                                    </option>
                                    {% endfor %}
                                </select>
                                <div class="invalid-feedback">
                                    Please select a supplier.
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="form_dosage" class="form-label">Form/Dosage</label>
                                <input type="text" class="form-control" id="form_dosage" name="form_dosage" 
                                       value="{{ medicine.form_dosage }}"
                                       placeholder="e.g., Tablet 500mg, Syrup 100ml">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="low_stock_limit" class="form-label">Low Stock Limit <span class="text-danger">*</span></label>
                                <input type="number" class="form-control" id="low_stock_limit" name="low_stock_limit" 
                                       min="0" value="{{ medicine.low_stock_limit }}" required>
                                <div class="form-text">
                                    Alert when stock reaches this level
                                </div>
                                <div class="invalid-feedback">
                                    Please provide a valid low stock limit.
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- New Fields Section -->
                    <div class="row">
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="expiry_date" class="form-label">Expiry Date</label>
                                <input type="date" class="form-control" id="expiry_date" name="expiry_date"
                                       value="{{ medicine.expiry_date or '' }}">
                                <div class="form-text">
                                    Medicine expiration date (optional)
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="batch_number" class="form-label">Batch Number</label>
                                <input type="text" class="form-control" id="batch_number" name="batch_number"
                                       value="{{ medicine.batch_number or '' }}"
                                       placeholder="e.g., BATCH001">
                                <div class="form-text">
                                    Manufacturing batch/lot number (optional)
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="barcode_number" class="form-label">Barcode Number</label>
                                <input type="text" class="form-control" id="barcode_number" name="barcode_number"
                                       value="{{ medicine.barcode_number or '' }}"
                                       placeholder="e.g., 1234567890123">
                                <div class="form-text">
                                    Product barcode/UPC (optional)
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="notes" class="form-label">Notes</label>
                        <textarea class="form-control" id="notes" name="notes" rows="3"
                                  placeholder="Additional information about the medicine...">{{ medicine.notes or medicine.description or '' }}</textarea>
                    </div>
                    
                    <div class="d-flex justify-content-end gap-2">
                        <a href="{{ url_for('medicines.index') }}" class="btn btn-secondary">
                            <i class="bi bi-x-circle"></i> Cancel
                        </a>
                        <button type="submit" class="btn btn-primary">
                            <i class="bi bi-check-circle"></i> Update Medicine
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// Form validation
(function() {
    'use strict';
    window.addEventListener('load', function() {
        var forms = document.getElementsByTagName('form');
        var validation = Array.prototype.filter.call(forms, function(form) {
            form.addEventListener('submit', function(event) {
                if (form.checkValidity() === false) {
                    event.preventDefault();
                    event.stopPropagation();
                }
                form.classList.add('was-validated');
            }, false);
        });
    }, false);
})();
</script>
{% endblock %}
