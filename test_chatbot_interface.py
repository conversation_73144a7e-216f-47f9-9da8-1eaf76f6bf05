#!/usr/bin/env python3
"""
Test the chatbot interface with the exact query that was problematic
"""

import requests
import json

def test_chatbot_interface():
    """Test the chatbot interface with the problematic query"""
    base_url = "http://127.0.0.1:5000"
    session = requests.Session()
    
    print("🎯 Testing Chatbot Interface with Fixed Query\n")
    
    # Login
    login_data = {'username': 'admin', 'password': '@Xx123456789xX@'}
    login_response = session.post(f"{base_url}/auth/login", data=login_data)
    print(f"Login: {'✅' if 'dashboard' in login_response.url else '❌'}")
    
    # Test the exact query that was reported as problematic
    query = "give me names of all the medicines in the database"
    print(f"\nTesting Query: '{query}'")
    
    query_data = {'query': query}
    response = session.post(f"{base_url}/chatbot/query", json=query_data, headers={'Content-Type': 'application/json'})
    
    if response.status_code == 200:
        data = response.json()
        agent_handled = data.get('agent_handled', False)
        llm_handled = data.get('llm_handled', False)
        response_text = data.get('response', '')
        
        print(f"Status: {'✅ Success' if response.status_code == 200 else '❌ Failed'}")
        print(f"Agent Handled: {agent_handled}")
        print(f"LLM Handled: {llm_handled}")
        print(f"Response Length: {len(response_text)} characters")
        
        # Analyze the response
        print("\n📊 Response Analysis:")
        
        # Check for correct format
        if 'ALL MEDICINES IN DATABASE' in response_text:
            print("✅ Correct response format detected")
        else:
            print("❌ Incorrect response format")
        
        # Count medicines
        medicine_count = response_text.count('. **')
        print(f"✅ Medicine Count: {medicine_count} (Expected: 50)")
        
        # Check for unwanted information
        unwanted_info = ['Patients', 'Suppliers', 'Departments', 'Stores']
        has_unwanted = any(info in response_text for info in unwanted_info)
        print(f"{'❌ Contains' if has_unwanted else '✅ No'} unwanted information about other entities")
        
        # Check if it's complete (not just 3 medicines)
        if medicine_count >= 50:
            print("✅ Complete medicine list provided")
        else:
            print(f"❌ Incomplete list - only {medicine_count} medicines shown")
        
        print("\n📝 Response Preview (First 10 lines):")
        lines = response_text.split('\n')
        for i, line in enumerate(lines[:10]):
            if line.strip():
                print(f"   {line}")
        
        print("\n📝 Response Preview (Last 5 lines):")
        for line in lines[-5:]:
            if line.strip():
                print(f"   {line}")
                
        # Test a few more variations to ensure robustness
        print("\n🔄 Testing Additional Query Variations:")
        
        variations = [
            "show me all medicine names",
            "list all medicines",
            "what medicines do we have"
        ]
        
        for variation in variations:
            var_response = session.post(f"{base_url}/chatbot/query", json={'query': variation}, headers={'Content-Type': 'application/json'})
            if var_response.status_code == 200:
                var_data = var_response.json()
                var_text = var_data.get('response', '')
                var_count = var_text.count('. **')
                print(f"   '{variation}': {var_count} medicines ({'✅' if var_count >= 50 else '❌'})")
            else:
                print(f"   '{variation}': ❌ Failed")
    
    else:
        print(f"❌ HTTP Error: {response.status_code}")
        print(f"Error Response: {response.text[:200]}...")
    
    print("\n🎉 Chatbot Interface Testing Complete!")

if __name__ == "__main__":
    test_chatbot_interface()
