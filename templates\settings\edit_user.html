{% extends "base.html" %}

{% block title %}Edit User - Hospital Pharmacy Management System{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1><i class="bi bi-person-gear"></i> Edit User</h1>
    <a href="{{ url_for('settings.users') }}" class="btn btn-outline-secondary">
        <i class="bi bi-arrow-left"></i> Back to Users
    </a>
</div>

<!-- Edit User Form -->
<div class="row justify-content-center">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0"><i class="bi bi-person-fill"></i> Edit User: {{ user.username }}</h5>
            </div>
            <div class="card-body">
                <form method="POST">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="username" class="form-label">Username <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="username" name="username" 
                                       value="{{ user.username }}" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="name" class="form-label">Full Name</label>
                                <input type="text" class="form-control" id="name" name="name" 
                                       value="{{ user.name or '' }}">
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="email" class="form-label">Email</label>
                                <input type="email" class="form-control" id="email" name="email" 
                                       value="{{ user.email or '' }}">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="role" class="form-label">Role <span class="text-danger">*</span></label>
                                <select class="form-select" id="role" name="role" required>
                                    <option value="admin" {% if user.role == 'admin' %}selected{% endif %}>Administrator</option>
                                    <option value="department_user" {% if user.role == 'department_user' %}selected{% endif %}>Department User</option>
                                </select>
                            </div>
                        </div>
                    </div>

                    <div class="mb-3" id="departmentField" {% if user.role == 'admin' %}style="display: none;"{% endif %}>
                        <label for="department_id" class="form-label">Department</label>
                        <select class="form-select" id="department_id" name="department_id">
                            <option value="">Select Department</option>
                            {% for department in departments %}
                            <option value="{{ department.id }}" 
                                    {% if user.department_id == department.id %}selected{% endif %}>
                                {{ department.name }}
                            </option>
                            {% endfor %}
                        </select>
                    </div>

                    <div class="mb-3">
                        <label for="password" class="form-label">New Password</label>
                        <input type="password" class="form-control" id="password" name="password" 
                               placeholder="Leave blank to keep current password">
                        <div class="form-text">Leave blank to keep the current password</div>
                    </div>

                    <div class="d-flex justify-content-between">
                        <div>
                            <button type="submit" class="btn btn-primary">
                                <i class="bi bi-check-circle"></i> Update User
                            </button>
                            <a href="{{ url_for('settings.users') }}" class="btn btn-secondary">
                                <i class="bi bi-x-circle"></i> Cancel
                            </a>
                        </div>
                        <div>
                            <form method="POST" action="{{ url_for('settings.reset_user_password', user_id=user.id) }}" 
                                  style="display: inline;" onsubmit="return confirm('Are you sure you want to reset this user\'s password?')">
                                <button type="submit" class="btn btn-warning">
                                    <i class="bi bi-key"></i> Reset Password
                                </button>
                            </form>
                        </div>
                    </div>
                </form>
            </div>
        </div>

        <!-- User Information Card -->
        <div class="card mt-4">
            <div class="card-header">
                <h5 class="card-title mb-0"><i class="bi bi-info-circle"></i> User Information</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <p><strong>User ID:</strong> <code>{{ user.id }}</code></p>
                        <p><strong>Created:</strong> {{ user.created_at[:19] if user.created_at else 'Unknown' }}</p>
                    </div>
                    <div class="col-md-6">
                        <p><strong>Last Updated:</strong> {{ user.updated_at[:19] if user.updated_at else 'Never' }}</p>
                        <p><strong>Current Role:</strong> 
                            <span class="badge {% if user.role == 'admin' %}bg-danger{% else %}bg-primary{% endif %}">
                                {{ user.role|title }}
                            </span>
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// Show/hide department field based on role
document.getElementById('role').addEventListener('change', function() {
    const departmentField = document.getElementById('departmentField');
    const departmentSelect = document.getElementById('department_id');
    
    if (this.value === 'department_user') {
        departmentField.style.display = 'block';
        departmentSelect.required = true;
    } else {
        departmentField.style.display = 'none';
        departmentSelect.required = false;
        departmentSelect.value = '';
    }
});

// Initialize department field visibility
document.addEventListener('DOMContentLoaded', function() {
    const roleSelect = document.getElementById('role');
    const departmentField = document.getElementById('departmentField');
    const departmentSelect = document.getElementById('department_id');
    
    if (roleSelect.value === 'department_user') {
        departmentField.style.display = 'block';
        departmentSelect.required = true;
    } else {
        departmentField.style.display = 'none';
        departmentSelect.required = false;
    }
});
</script>
{% endblock %}
