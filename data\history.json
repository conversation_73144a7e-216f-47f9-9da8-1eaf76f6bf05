[{"id": "01", "timestamp": "2025-07-25T03:14:45.063304", "user_id": "01", "username": "admin", "role": "admin", "department_id": null, "action": "LOGIN", "entity_type": "user", "entity_id": "01", "details": {"username": "admin", "role": "admin"}, "ip_address": "localhost", "user_agent": "Flask App"}, {"id": "02", "timestamp": "2025-07-25T04:41:00.275229", "user_id": "01", "username": "admin", "role": "admin", "department_id": null, "action": "LOGIN", "entity_type": "user", "entity_id": "01", "details": {"username": "admin", "role": "admin"}, "ip_address": "localhost", "user_agent": "Flask App"}, {"id": "03", "timestamp": "2025-07-25T10:11:17.762434", "user_id": "01", "username": "admin", "role": "admin", "department_id": null, "action": "LOGIN", "entity_type": "user", "entity_id": "01", "details": {"username": "admin", "role": "admin"}, "ip_address": "localhost", "user_agent": "Flask App"}, {"id": "04", "timestamp": "2025-07-25T10:11:51.015051", "user_id": "01", "username": "admin", "role": "admin", "department_id": null, "action": "VIEW", "entity_type": "profile", "entity_id": "01", "details": {}, "ip_address": "localhost", "user_agent": "Flask App"}, {"id": "05", "timestamp": "2025-07-25T10:11:51.437339", "user_id": "01", "username": "admin", "role": "admin", "department_id": null, "action": "VIEW", "entity_type": "profile", "entity_id": "01", "details": {}, "ip_address": "localhost", "user_agent": "Flask App"}, {"id": "06", "timestamp": "2025-07-25T10:11:51.470799", "user_id": "01", "username": "admin", "role": "admin", "department_id": null, "action": "VIEW", "entity_type": "profile", "entity_id": "01", "details": {}, "ip_address": "localhost", "user_agent": "Flask App"}, {"id": "07", "timestamp": "2025-07-25T10:11:51.516392", "user_id": "01", "username": "admin", "role": "admin", "department_id": null, "action": "VIEW", "entity_type": "profile", "entity_id": "01", "details": {}, "ip_address": "localhost", "user_agent": "Flask App"}, {"id": "08", "timestamp": "2025-07-25T11:58:00.868930", "user_id": "02", "username": "pharmacy", "role": "department_user", "department_id": "01", "action": "LOGIN", "entity_type": "user", "entity_id": "02", "details": {"username": "pharmacy", "role": "department_user"}, "ip_address": "localhost", "user_agent": "Flask App"}, {"id": "09", "timestamp": "2025-07-25T12:00:30.308262", "user_id": "02", "username": "pharmacy", "role": "department_user", "department_id": "01", "action": "LOGOUT", "entity_type": "user", "entity_id": "02", "details": {"username": "pharmacy"}, "ip_address": "localhost", "user_agent": "Flask App"}, {"id": "10", "timestamp": "2025-07-25T12:00:52.774321", "user_id": "03", "username": "emergency", "role": "department_user", "department_id": "02", "action": "LOGIN", "entity_type": "user", "entity_id": "03", "details": {"username": "emergency", "role": "department_user"}, "ip_address": "localhost", "user_agent": "Flask App"}, {"id": "11", "timestamp": "2025-07-25T13:01:36.287351", "user_id": "01", "username": "admin", "role": "admin", "department_id": null, "action": "LOGIN", "entity_type": "user", "entity_id": "01", "details": {"username": "admin", "role": "admin"}, "ip_address": "localhost", "user_agent": "Flask App"}, {"id": "12", "timestamp": "2025-07-25T19:12:10.914993", "user_id": "01", "username": "admin", "role": "admin", "department_id": null, "action": "LOGIN", "entity_type": "user", "entity_id": "01", "details": {"username": "admin", "role": "admin"}, "ip_address": "localhost", "user_agent": "Flask App"}, {"id": "13", "timestamp": "2025-07-25T19:27:32.318937", "user_id": "01", "username": "admin", "role": "admin", "department_id": null, "action": "VIEW", "entity_type": "transfer", "entity_id": "01", "details": {}, "ip_address": "localhost", "user_agent": "Flask App"}, {"id": "14", "timestamp": "2025-07-25T19:27:32.794520", "user_id": "01", "username": "admin", "role": "admin", "department_id": null, "action": "VIEW", "entity_type": "transfer", "entity_id": "01", "details": {}, "ip_address": "localhost", "user_agent": "Flask App"}, {"id": "15", "timestamp": "2025-07-25T19:27:32.825614", "user_id": "01", "username": "admin", "role": "admin", "department_id": null, "action": "VIEW", "entity_type": "transfer", "entity_id": "01", "details": {}, "ip_address": "localhost", "user_agent": "Flask App"}, {"id": "16", "timestamp": "2025-07-25T19:28:44.279389", "user_id": "01", "username": "admin", "role": "admin", "department_id": null, "action": "VIEW", "entity_type": "transfer", "entity_id": "11", "details": {}, "ip_address": "localhost", "user_agent": "Flask App"}, {"id": "17", "timestamp": "2025-07-25T19:28:44.726066", "user_id": "01", "username": "admin", "role": "admin", "department_id": null, "action": "VIEW", "entity_type": "transfer", "entity_id": "11", "details": {}, "ip_address": "localhost", "user_agent": "Flask App"}, {"id": "18", "timestamp": "2025-07-25T19:28:44.762478", "user_id": "01", "username": "admin", "role": "admin", "department_id": null, "action": "VIEW", "entity_type": "transfer", "entity_id": "11", "details": {}, "ip_address": "localhost", "user_agent": "Flask App"}, {"id": "19", "timestamp": "2025-07-25T21:03:19.986177", "user_id": "01", "username": "admin", "role": "admin", "department_id": null, "action": "LOGIN", "entity_type": "user", "entity_id": "01", "details": {"username": "admin", "role": "admin"}, "ip_address": "localhost", "user_agent": "Flask App"}, {"id": "20", "timestamp": "2025-07-25T21:11:30.437801", "user_id": "01", "username": "admin", "role": "admin", "department_id": null, "action": "LOGIN", "entity_type": "user", "entity_id": "01", "details": {"username": "admin", "role": "admin"}, "ip_address": "localhost", "user_agent": "Flask App"}, {"id": "21", "timestamp": "2025-07-25T22:17:25.820937", "user_id": "01", "username": "admin", "role": "admin", "department_id": null, "action": "LOGOUT", "entity_type": "user", "entity_id": "01", "details": {"username": "admin"}, "ip_address": "localhost", "user_agent": "Flask App"}, {"id": "22", "timestamp": "2025-07-26T00:23:33.798644", "user_id": "01", "username": "admin", "role": "admin", "department_id": null, "action": "LOGIN", "entity_type": "user", "entity_id": "01", "details": {"username": "admin", "role": "admin"}, "ip_address": "localhost", "user_agent": "Flask App"}, {"id": "23", "timestamp": "2025-07-26T03:55:55.793795", "user_id": "01", "username": "admin", "role": "admin", "department_id": null, "action": "LOGIN", "entity_type": "user", "entity_id": "01", "details": {"username": "admin", "role": "admin"}, "ip_address": "localhost", "user_agent": "Flask App"}, {"id": "24", "timestamp": "2025-07-26T03:57:51.966888", "user_id": "01", "username": "admin", "role": "admin", "department_id": null, "action": "QUERY", "entity_type": "chatbot", "entity_id": "01", "details": {"query": "hi"}, "ip_address": "localhost", "user_agent": "Flask App"}, {"id": "25", "timestamp": "2025-07-26T03:58:35.181190", "user_id": "01", "username": "admin", "role": "admin", "department_id": null, "action": "QUERY", "entity_type": "chatbot", "entity_id": "01", "details": {"query": "\"Add a new medicine called [waleed]"}, "ip_address": "localhost", "user_agent": "Flask App"}, {"id": "26", "timestamp": "2025-07-26T03:58:35.191621", "user_id": "01", "username": "admin", "role": "admin", "department_id": null, "action": "AI_COMMAND", "entity_type": "chatbot", "entity_id": "01", "details": {"command": "\"Add a new medicine called [waleed]", "command_type": "add_medicine"}, "ip_address": "localhost", "user_agent": "Flask App"}, {"id": "27", "timestamp": "2025-07-26T03:58:35.209800", "user_id": "01", "username": "admin", "role": "admin", "department_id": null, "action": "CREATE", "entity_type": "medicine", "entity_id": "01", "details": {"medicine_id": "62", "medicine_name": "[waleed]", "via": "AI_assistant"}, "ip_address": "localhost", "user_agent": "Flask App"}, {"id": "28", "timestamp": "2025-07-26T04:16:24.728981", "user_id": "01", "username": "admin", "role": "admin", "department_id": null, "action": "QUERY", "entity_type": "chatbot", "entity_id": "01", "details": {"query": "hi"}, "ip_address": "localhost", "user_agent": "Flask App"}, {"id": "29", "timestamp": "2025-07-26T04:16:36.708206", "user_id": "01", "username": "admin", "role": "admin", "department_id": null, "action": "QUERY", "entity_type": "chatbot", "entity_id": "01", "details": {"query": "Show me medicines expiring within 30 day"}, "ip_address": "localhost", "user_agent": "Flask App"}, {"id": "30", "timestamp": "2025-07-26T04:16:36.717797", "user_id": "01", "username": "admin", "role": "admin", "department_id": null, "action": "AI_COMMAND", "entity_type": "chatbot", "entity_id": "01", "details": {"command": "Show me medicines expiring within 30 day", "command_type": "expiry_analysis"}, "ip_address": "localhost", "user_agent": "Flask App"}, {"id": "31", "timestamp": "2025-07-26T04:16:50.087290", "user_id": "01", "username": "admin", "role": "admin", "department_id": null, "action": "QUERY", "entity_type": "chatbot", "entity_id": "01", "details": {"query": "Which department has the lowest average stock levels?"}, "ip_address": "localhost", "user_agent": "Flask App"}, {"id": "32", "timestamp": "2025-07-26T04:16:50.096886", "user_id": "01", "username": "admin", "role": "admin", "department_id": null, "action": "AI_COMMAND", "entity_type": "chatbot", "entity_id": "01", "details": {"command": "Which department has the lowest average stock levels?", "command_type": "department_analysis"}, "ip_address": "localhost", "user_agent": "Flask App"}, {"id": "33", "timestamp": "2025-07-26T04:23:23.759321", "user_id": "01", "username": "admin", "role": "admin", "department_id": null, "action": "QUERY", "entity_type": "chatbot", "entity_id": "01", "details": {"query": "What medicines are running low on stock?"}, "ip_address": "localhost", "user_agent": "Flask App"}, {"id": "34", "timestamp": "2025-07-26T04:23:27.667360", "user_id": "01", "username": "admin", "role": "admin", "department_id": null, "action": "QUERY", "entity_type": "chatbot", "entity_id": "01", "details": {"query": "What is the highest stock medicine and its current level?"}, "ip_address": "localhost", "user_agent": "Flask App"}, {"id": "35", "timestamp": "2025-07-26T04:23:27.677827", "user_id": "01", "username": "admin", "role": "admin", "department_id": null, "action": "AI_COMMAND", "entity_type": "chatbot", "entity_id": "01", "details": {"command": "What is the highest stock medicine and its current level?", "command_type": "highest_stock"}, "ip_address": "localhost", "user_agent": "Flask App"}, {"id": "36", "timestamp": "2025-07-26T04:23:32.158450", "user_id": "01", "username": "admin", "role": "admin", "department_id": null, "action": "QUERY", "entity_type": "chatbot", "entity_id": "01", "details": {"query": "Show me medicines expiring within 30 days"}, "ip_address": "localhost", "user_agent": "Flask App"}, {"id": "37", "timestamp": "2025-07-26T04:23:32.167884", "user_id": "01", "username": "admin", "role": "admin", "department_id": null, "action": "AI_COMMAND", "entity_type": "chatbot", "entity_id": "01", "details": {"command": "Show me medicines expiring within 30 days", "command_type": "expiry_analysis"}, "ip_address": "localhost", "user_agent": "Flask App"}, {"id": "38", "timestamp": "2025-07-26T04:23:37.191811", "user_id": "01", "username": "admin", "role": "admin", "department_id": null, "action": "QUERY", "entity_type": "chatbot", "entity_id": "01", "details": {"query": "Which department has the lowest average stock levels?"}, "ip_address": "localhost", "user_agent": "Flask App"}, {"id": "39", "timestamp": "2025-07-26T04:23:37.202355", "user_id": "01", "username": "admin", "role": "admin", "department_id": null, "action": "AI_COMMAND", "entity_type": "chatbot", "entity_id": "01", "details": {"command": "Which department has the lowest average stock levels?", "command_type": "department_analysis"}, "ip_address": "localhost", "user_agent": "Flask App"}, {"id": "40", "timestamp": "2025-07-26T04:51:24.127933", "user_id": "01", "username": "admin", "role": "admin", "department_id": null, "action": "LOGIN", "entity_type": "user", "entity_id": "01", "details": {"username": "admin", "role": "admin"}, "ip_address": "localhost", "user_agent": "Flask App"}, {"id": "41", "timestamp": "2025-07-26T04:51:35.124183", "user_id": "01", "username": "admin", "role": "admin", "department_id": null, "action": "QUERY", "entity_type": "chatbot", "entity_id": "01", "details": {"query": "hi"}, "ip_address": "localhost", "user_agent": "Flask App"}, {"id": "42", "timestamp": "2025-07-26T05:02:48.070270", "user_id": "01", "username": "admin", "role": "admin", "department_id": null, "action": "LOGIN", "entity_type": "user", "entity_id": "01", "details": {"username": "admin", "role": "admin"}, "ip_address": "localhost", "user_agent": "Flask App"}, {"id": "43", "timestamp": "2025-07-26T05:02:48.158390", "user_id": "01", "username": "admin", "role": "admin", "department_id": null, "action": "QUERY", "entity_type": "chatbot", "entity_id": "01", "details": {"query": "Show me complete overview of all database tables with detailed analysis"}, "ip_address": "localhost", "user_agent": "Flask App"}, {"id": "44", "timestamp": "2025-07-26T05:02:48.168912", "user_id": "01", "username": "admin", "role": "admin", "department_id": null, "action": "AI_COMMAND", "entity_type": "chatbot", "entity_id": "01", "details": {"command": "Show me complete overview of all database tables with detailed analysis", "command_type": "comprehensive_overview"}, "ip_address": "localhost", "user_agent": "Flask App"}, {"id": "45", "timestamp": "2025-07-26T05:04:59.505689", "user_id": "01", "username": "admin", "role": "admin", "department_id": null, "action": "QUERY", "entity_type": "chatbot", "entity_id": "01", "details": {"query": "hi"}, "ip_address": "localhost", "user_agent": "Flask App"}, {"id": "46", "timestamp": "2025-07-26T05:05:20.768408", "user_id": "01", "username": "admin", "role": "admin", "department_id": null, "action": "QUERY", "entity_type": "chatbot", "entity_id": "01", "details": {"query": "report for all the stores"}, "ip_address": "localhost", "user_agent": "Flask App"}, {"id": "47", "timestamp": "2025-07-26T05:05:50.490284", "user_id": "01", "username": "admin", "role": "admin", "department_id": null, "action": "QUERY", "entity_type": "chatbot", "entity_id": "01", "details": {"query": "Get real-time inventory status"}, "ip_address": "localhost", "user_agent": "Flask App"}, {"id": "48", "timestamp": "2025-07-26T05:06:09.557360", "user_id": "01", "username": "admin", "role": "admin", "department_id": null, "action": "QUERY", "entity_type": "chatbot", "entity_id": "01", "details": {"query": "• Analyze consumption patterns"}, "ip_address": "localhost", "user_agent": "Flask App"}, {"id": "49", "timestamp": "2025-07-26T05:06:22.541002", "user_id": "01", "username": "admin", "role": "admin", "department_id": null, "action": "QUERY", "entity_type": "chatbot", "entity_id": "01", "details": {"query": "What medicines are running low on stock?"}, "ip_address": "localhost", "user_agent": "Flask App"}, {"id": "50", "timestamp": "2025-07-26T05:06:24.645764", "user_id": "01", "username": "admin", "role": "admin", "department_id": null, "action": "QUERY", "entity_type": "chatbot", "entity_id": "01", "details": {"query": "Which patient has consumed the most medicines this month?"}, "ip_address": "localhost", "user_agent": "Flask App"}, {"id": "51", "timestamp": "2025-07-26T05:06:24.656681", "user_id": "01", "username": "admin", "role": "admin", "department_id": null, "action": "AI_COMMAND", "entity_type": "chatbot", "entity_id": "01", "details": {"command": "Which patient has consumed the most medicines this month?", "command_type": "top_patients"}, "ip_address": "localhost", "user_agent": "Flask App"}, {"id": "52", "timestamp": "2025-07-26T05:06:28.315609", "user_id": "01", "username": "admin", "role": "admin", "department_id": null, "action": "QUERY", "entity_type": "chatbot", "entity_id": "01", "details": {"query": "Which department has the lowest average stock levels?"}, "ip_address": "localhost", "user_agent": "Flask App"}, {"id": "53", "timestamp": "2025-07-26T05:06:28.327404", "user_id": "01", "username": "admin", "role": "admin", "department_id": null, "action": "AI_COMMAND", "entity_type": "chatbot", "entity_id": "01", "details": {"command": "Which department has the lowest average stock levels?", "command_type": "department_analysis"}, "ip_address": "localhost", "user_agent": "Flask App"}, {"id": "54", "timestamp": "2025-07-26T05:06:33.998486", "user_id": "01", "username": "admin", "role": "admin", "department_id": null, "action": "QUERY", "entity_type": "chatbot", "entity_id": "01", "details": {"query": "What is the highest stock medicine and its current level?"}, "ip_address": "localhost", "user_agent": "Flask App"}, {"id": "55", "timestamp": "2025-07-26T05:06:34.008936", "user_id": "01", "username": "admin", "role": "admin", "department_id": null, "action": "AI_COMMAND", "entity_type": "chatbot", "entity_id": "01", "details": {"command": "What is the highest stock medicine and its current level?", "command_type": "highest_stock"}, "ip_address": "localhost", "user_agent": "Flask App"}, {"id": "56", "timestamp": "2025-07-26T05:06:38.379126", "user_id": "01", "username": "admin", "role": "admin", "department_id": null, "action": "QUERY", "entity_type": "chatbot", "entity_id": "01", "details": {"query": "Show me the top 5 most expensive purchases this year"}, "ip_address": "localhost", "user_agent": "Flask App"}, {"id": "57", "timestamp": "2025-07-26T05:06:38.393444", "user_id": "01", "username": "admin", "role": "admin", "department_id": null, "action": "AI_COMMAND", "entity_type": "chatbot", "entity_id": "01", "details": {"command": "Show me the top 5 most expensive purchases this year", "command_type": "expensive_purchases"}, "ip_address": "localhost", "user_agent": "Flask App"}, {"id": "58", "timestamp": "2025-07-26T05:06:44.030172", "user_id": "01", "username": "admin", "role": "admin", "department_id": null, "action": "QUERY", "entity_type": "chatbot", "entity_id": "01", "details": {"query": "Show me medicines expiring within 30 days"}, "ip_address": "localhost", "user_agent": "Flask App"}, {"id": "59", "timestamp": "2025-07-26T05:06:44.042053", "user_id": "01", "username": "admin", "role": "admin", "department_id": null, "action": "AI_COMMAND", "entity_type": "chatbot", "entity_id": "01", "details": {"command": "Show me medicines expiring within 30 days", "command_type": "expiry_analysis"}, "ip_address": "localhost", "user_agent": "Flask App"}, {"id": "60", "timestamp": "2025-07-26T05:12:14.827871", "user_id": "01", "username": "admin", "role": "admin", "department_id": null, "action": "QUERY", "entity_type": "chatbot", "entity_id": "01", "details": {"query": "What medicines are running low on stock?"}, "ip_address": "localhost", "user_agent": "Flask App"}, {"id": "61", "timestamp": "2025-07-26T05:12:24.086714", "user_id": "01", "username": "admin", "role": "admin", "department_id": null, "action": "QUERY", "entity_type": "chatbot", "entity_id": "01", "details": {"query": "hi"}, "ip_address": "localhost", "user_agent": "Flask App"}, {"id": "62", "timestamp": "2025-07-26T05:13:09.084985", "user_id": "01", "username": "admin", "role": "admin", "department_id": null, "action": "LOGIN", "entity_type": "user", "entity_id": "01", "details": {"username": "admin", "role": "admin"}, "ip_address": "localhost", "user_agent": "Flask App"}, {"id": "63", "timestamp": "2025-07-26T05:13:09.189461", "user_id": "01", "username": "admin", "role": "admin", "department_id": null, "action": "QUERY", "entity_type": "chatbot", "entity_id": "01", "details": {"query": "Show me complete overview of all database tables with detailed analysis"}, "ip_address": "localhost", "user_agent": "Flask App"}, {"id": "64", "timestamp": "2025-07-26T05:13:09.203162", "user_id": "01", "username": "admin", "role": "admin", "department_id": null, "action": "AI_COMMAND", "entity_type": "chatbot", "entity_id": "01", "details": {"command": "Show me complete overview of all database tables with detailed analysis", "command_type": "comprehensive_overview"}, "ip_address": "localhost", "user_agent": "Flask App"}, {"id": "65", "timestamp": "2025-07-26T05:14:39.344008", "user_id": "01", "username": "admin", "role": "admin", "department_id": null, "action": "LOGIN", "entity_type": "user", "entity_id": "01", "details": {"username": "admin", "role": "admin"}, "ip_address": "localhost", "user_agent": "Flask App"}, {"id": "66", "timestamp": "2025-07-26T05:14:39.498124", "user_id": "01", "username": "admin", "role": "admin", "department_id": null, "action": "QUERY", "entity_type": "chatbot", "entity_id": "01", "details": {"query": "Hello, can you help me with pharmacy data?"}, "ip_address": "localhost", "user_agent": "Flask App"}, {"id": "67", "timestamp": "2025-07-26T05:14:39.617809", "user_id": "01", "username": "admin", "role": "admin", "department_id": null, "action": "QUERY", "entity_type": "chatbot", "entity_id": "01", "details": {"query": "Show me complete overview of all database tables with detailed analysis"}, "ip_address": "localhost", "user_agent": "Flask App"}, {"id": "68", "timestamp": "2025-07-26T05:14:39.631057", "user_id": "01", "username": "admin", "role": "admin", "department_id": null, "action": "AI_COMMAND", "entity_type": "chatbot", "entity_id": "01", "details": {"command": "Show me complete overview of all database tables with detailed analysis", "command_type": "comprehensive_overview"}, "ip_address": "localhost", "user_agent": "Flask App"}, {"id": "69", "timestamp": "2025-07-26T05:18:39.517239", "user_id": "01", "username": "admin", "role": "admin", "department_id": null, "action": "LOGIN", "entity_type": "user", "entity_id": "01", "details": {"username": "admin", "role": "admin"}, "ip_address": "localhost", "user_agent": "Flask App"}, {"id": "70", "timestamp": "2025-07-26T05:18:39.688966", "user_id": "01", "username": "admin", "role": "admin", "department_id": null, "action": "QUERY", "entity_type": "chatbot", "entity_id": "01", "details": {"query": "Hello, can you help me with pharmacy data?"}, "ip_address": "localhost", "user_agent": "Flask App"}, {"id": "71", "timestamp": "2025-07-26T05:18:39.805967", "user_id": "01", "username": "admin", "role": "admin", "department_id": null, "action": "QUERY", "entity_type": "chatbot", "entity_id": "01", "details": {"query": "Show me complete overview of all database tables with detailed analysis"}, "ip_address": "localhost", "user_agent": "Flask App"}, {"id": "72", "timestamp": "2025-07-26T05:18:39.819513", "user_id": "01", "username": "admin", "role": "admin", "department_id": null, "action": "AI_COMMAND", "entity_type": "chatbot", "entity_id": "01", "details": {"command": "Show me complete overview of all database tables with detailed analysis", "command_type": "comprehensive_overview"}, "ip_address": "localhost", "user_agent": "Flask App"}, {"id": "73", "timestamp": "2025-07-26T05:19:07.284488", "user_id": "01", "username": "admin", "role": "admin", "department_id": null, "action": "LOGIN", "entity_type": "user", "entity_id": "01", "details": {"username": "admin", "role": "admin"}, "ip_address": "localhost", "user_agent": "Flask App"}, {"id": "74", "timestamp": "2025-07-26T05:19:07.421363", "user_id": "01", "username": "admin", "role": "admin", "department_id": null, "action": "QUERY", "entity_type": "chatbot", "entity_id": "01", "details": {"query": "Show me medicine inventory overview"}, "ip_address": "localhost", "user_agent": "Flask App"}, {"id": "75", "timestamp": "2025-07-26T05:19:07.563406", "user_id": "01", "username": "admin", "role": "admin", "department_id": null, "action": "QUERY", "entity_type": "chatbot", "entity_id": "01", "details": {"query": "What is the current status of our pharmacy inventory?"}, "ip_address": "localhost", "user_agent": "Flask App"}, {"id": "76", "timestamp": "2025-07-26T05:19:48.006898", "user_id": "01", "username": "admin", "role": "admin", "department_id": null, "action": "LOGIN", "entity_type": "user", "entity_id": "01", "details": {"username": "admin", "role": "admin"}, "ip_address": "localhost", "user_agent": "Flask App"}, {"id": "77", "timestamp": "2025-07-26T05:19:48.160932", "user_id": "01", "username": "admin", "role": "admin", "department_id": null, "action": "QUERY", "entity_type": "chatbot", "entity_id": "01", "details": {"query": "Show me medicine inventory overview"}, "ip_address": "localhost", "user_agent": "Flask App"}, {"id": "78", "timestamp": "2025-07-26T05:19:48.307039", "user_id": "01", "username": "admin", "role": "admin", "department_id": null, "action": "QUERY", "entity_type": "chatbot", "entity_id": "01", "details": {"query": "What is the current status of our pharmacy inventory?"}, "ip_address": "localhost", "user_agent": "Flask App"}, {"id": "79", "timestamp": "2025-07-26T05:20:11.345920", "user_id": "01", "username": "admin", "role": "admin", "department_id": null, "action": "LOGIN", "entity_type": "user", "entity_id": "01", "details": {"username": "admin", "role": "admin"}, "ip_address": "localhost", "user_agent": "Flask App"}, {"id": "80", "timestamp": "2025-07-26T05:20:53.363069", "user_id": "01", "username": "admin", "role": "admin", "department_id": null, "action": "LOGIN", "entity_type": "user", "entity_id": "01", "details": {"username": "admin", "role": "admin"}, "ip_address": "localhost", "user_agent": "Flask App"}, {"id": "81", "timestamp": "2025-07-26T05:20:53.518351", "user_id": "01", "username": "admin", "role": "admin", "department_id": null, "action": "QUERY", "entity_type": "chatbot", "entity_id": "01", "details": {"query": "Show me complete overview of all database tables"}, "ip_address": "localhost", "user_agent": "Flask App"}, {"id": "82", "timestamp": "2025-07-26T05:20:53.530598", "user_id": "01", "username": "admin", "role": "admin", "department_id": null, "action": "AI_COMMAND", "entity_type": "chatbot", "entity_id": "01", "details": {"command": "Show me complete overview of all database tables", "command_type": "comprehensive_overview"}, "ip_address": "localhost", "user_agent": "Flask App"}, {"id": "83", "timestamp": "2025-07-26T05:20:53.682683", "user_id": "01", "username": "admin", "role": "admin", "department_id": null, "action": "QUERY", "entity_type": "chatbot", "entity_id": "01", "details": {"query": "What medicines are running low on stock?"}, "ip_address": "localhost", "user_agent": "Flask App"}, {"id": "84", "timestamp": "2025-07-26T05:20:53.829511", "user_id": "01", "username": "admin", "role": "admin", "department_id": null, "action": "QUERY", "entity_type": "chatbot", "entity_id": "01", "details": {"query": "Which patient has consumed the most medicines?"}, "ip_address": "localhost", "user_agent": "Flask App"}, {"id": "85", "timestamp": "2025-07-26T05:20:53.848488", "user_id": "01", "username": "admin", "role": "admin", "department_id": null, "action": "AI_COMMAND", "entity_type": "chatbot", "entity_id": "01", "details": {"command": "Which patient has consumed the most medicines?", "command_type": "top_patients"}, "ip_address": "localhost", "user_agent": "Flask App"}, {"id": "86", "timestamp": "2025-07-26T05:20:54.006974", "user_id": "01", "username": "admin", "role": "admin", "department_id": null, "action": "QUERY", "entity_type": "chatbot", "entity_id": "01", "details": {"query": "Show me supplier analysis with performance metrics"}, "ip_address": "localhost", "user_agent": "Flask App"}, {"id": "87", "timestamp": "2025-07-26T05:20:54.023789", "user_id": "01", "username": "admin", "role": "admin", "department_id": null, "action": "AI_COMMAND", "entity_type": "chatbot", "entity_id": "01", "details": {"command": "Show me supplier analysis with performance metrics", "command_type": "suppliers_analysis"}, "ip_address": "localhost", "user_agent": "Flask App"}, {"id": "88", "timestamp": "2025-07-26T05:20:54.167330", "user_id": "01", "username": "admin", "role": "admin", "department_id": null, "action": "QUERY", "entity_type": "chatbot", "entity_id": "01", "details": {"query": "What is the current status of our pharmacy inventory?"}, "ip_address": "localhost", "user_agent": "Flask App"}, {"id": "89", "timestamp": "2025-07-26T05:21:05.035154", "user_id": "01", "username": "admin", "role": "admin", "department_id": null, "action": "QUERY", "entity_type": "chatbot", "entity_id": "01", "details": {"query": "Provide recommendations for improving our medicine stock management"}, "ip_address": "localhost", "user_agent": "Flask App"}, {"id": "90", "timestamp": "2025-07-26T05:21:15.224106", "user_id": "01", "username": "admin", "role": "admin", "department_id": null, "action": "QUERY", "entity_type": "chatbot", "entity_id": "01", "details": {"query": "Test with invalid API key"}, "ip_address": "localhost", "user_agent": "Flask App"}, {"id": "91", "timestamp": "2025-07-26T11:01:12.204791", "user_id": "01", "username": "admin", "role": "admin", "department_id": null, "action": "QUERY", "entity_type": "chatbot", "entity_id": "01", "details": {"query": "hi"}, "ip_address": "localhost", "user_agent": "Flask App"}, {"id": "92", "timestamp": "2025-07-26T11:02:37.309340", "user_id": "01", "username": "admin", "role": "admin", "department_id": null, "action": "QUERY", "entity_type": "chatbot", "entity_id": "01", "details": {"query": "give me report about consumption of all the medicins"}, "ip_address": "localhost", "user_agent": "Flask App"}, {"id": "93", "timestamp": "2025-07-26T11:30:08.074897", "user_id": "01", "username": "admin", "role": "admin", "department_id": null, "action": "QUERY", "entity_type": "chatbot", "entity_id": "01", "details": {"query": "hi"}, "ip_address": "localhost", "user_agent": "Flask App"}, {"id": "94", "timestamp": "2025-07-26T11:30:34.551695", "user_id": "01", "username": "admin", "role": "admin", "department_id": null, "action": "QUERY", "entity_type": "chatbot", "entity_id": "01", "details": {"query": "how can u help me"}, "ip_address": "localhost", "user_agent": "Flask App"}, {"id": "95", "timestamp": "2025-07-26T11:32:17.609143", "user_id": "01", "username": "admin", "role": "admin", "department_id": null, "action": "QUERY", "entity_type": "chatbot", "entity_id": "01", "details": {"query": "Stock Monitoring for all the medicins"}, "ip_address": "localhost", "user_agent": "Flask App"}, {"id": "96", "timestamp": "2025-07-26T11:33:38.578492", "user_id": "01", "username": "admin", "role": "admin", "department_id": null, "action": "QUERY", "entity_type": "chatbot", "entity_id": "01", "details": {"query": "how many medicine in the database"}, "ip_address": "localhost", "user_agent": "Flask App"}, {"id": "97", "timestamp": "2025-07-26T11:34:06.147262", "user_id": "01", "username": "admin", "role": "admin", "department_id": null, "action": "QUERY", "entity_type": "chatbot", "entity_id": "01", "details": {"query": "give me all the names only"}, "ip_address": "localhost", "user_agent": "Flask App"}, {"id": "98", "timestamp": "2025-07-26T11:41:43.100861", "user_id": "01", "username": "admin", "role": "admin", "department_id": null, "action": "LOGIN", "entity_type": "user", "entity_id": "01", "details": {"username": "admin", "role": "admin"}, "ip_address": "localhost", "user_agent": "Flask App"}, {"id": "99", "timestamp": "2025-07-26T11:41:43.284482", "user_id": "01", "username": "admin", "role": "admin", "department_id": null, "action": "QUERY", "entity_type": "chatbot", "entity_id": "01", "details": {"query": "give me names of all the medicines in the database"}, "ip_address": "localhost", "user_agent": "Flask App"}, {"id": "100", "timestamp": "2025-07-26T11:41:43.298135", "user_id": "01", "username": "admin", "role": "admin", "department_id": null, "action": "AI_COMMAND", "entity_type": "chatbot", "entity_id": "01", "details": {"command": "give me names of all the medicines in the database", "command_type": "medicine_names_list"}, "ip_address": "localhost", "user_agent": "Flask App"}, {"id": "101", "timestamp": "2025-07-26T11:41:43.449420", "user_id": "01", "username": "admin", "role": "admin", "department_id": null, "action": "QUERY", "entity_type": "chatbot", "entity_id": "01", "details": {"query": "show me all medicine names"}, "ip_address": "localhost", "user_agent": "Flask App"}, {"id": "102", "timestamp": "2025-07-26T11:41:43.461883", "user_id": "01", "username": "admin", "role": "admin", "department_id": null, "action": "AI_COMMAND", "entity_type": "chatbot", "entity_id": "01", "details": {"command": "show me all medicine names", "command_type": "medicine_names_list"}, "ip_address": "localhost", "user_agent": "Flask App"}, {"id": "103", "timestamp": "2025-07-26T11:41:43.617006", "user_id": "01", "username": "admin", "role": "admin", "department_id": null, "action": "QUERY", "entity_type": "chatbot", "entity_id": "01", "details": {"query": "list all medicines in database"}, "ip_address": "localhost", "user_agent": "Flask App"}, {"id": "104", "timestamp": "2025-07-26T11:41:43.630303", "user_id": "01", "username": "admin", "role": "admin", "department_id": null, "action": "AI_COMMAND", "entity_type": "chatbot", "entity_id": "01", "details": {"command": "list all medicines in database", "command_type": "medicine_names_list"}, "ip_address": "localhost", "user_agent": "Flask App"}, {"id": "105", "timestamp": "2025-07-26T11:41:43.768207", "user_id": "01", "username": "admin", "role": "admin", "department_id": null, "action": "QUERY", "entity_type": "chatbot", "entity_id": "01", "details": {"query": "what are all the medicines"}, "ip_address": "localhost", "user_agent": "Flask App"}, {"id": "106", "timestamp": "2025-07-26T11:41:43.781591", "user_id": "01", "username": "admin", "role": "admin", "department_id": null, "action": "AI_COMMAND", "entity_type": "chatbot", "entity_id": "01", "details": {"command": "what are all the medicines", "command_type": "medicine_names_list"}, "ip_address": "localhost", "user_agent": "Flask App"}, {"id": "107", "timestamp": "2025-07-26T11:41:43.930969", "user_id": "01", "username": "admin", "role": "admin", "department_id": null, "action": "QUERY", "entity_type": "chatbot", "entity_id": "01", "details": {"query": "show complete list of medicines"}, "ip_address": "localhost", "user_agent": "Flask App"}, {"id": "108", "timestamp": "2025-07-26T11:41:43.944258", "user_id": "01", "username": "admin", "role": "admin", "department_id": null, "action": "AI_COMMAND", "entity_type": "chatbot", "entity_id": "01", "details": {"command": "show complete list of medicines", "command_type": "medicine_names_list"}, "ip_address": "localhost", "user_agent": "Flask App"}, {"id": "109", "timestamp": "2025-07-26T11:42:20.686749", "user_id": "01", "username": "admin", "role": "admin", "department_id": null, "action": "LOGIN", "entity_type": "user", "entity_id": "01", "details": {"username": "admin", "role": "admin"}, "ip_address": "localhost", "user_agent": "Flask App"}, {"id": "110", "timestamp": "2025-07-26T11:42:20.850256", "user_id": "01", "username": "admin", "role": "admin", "department_id": null, "action": "QUERY", "entity_type": "chatbot", "entity_id": "01", "details": {"query": "give me names of all the medicines in the database"}, "ip_address": "localhost", "user_agent": "Flask App"}, {"id": "111", "timestamp": "2025-07-26T11:42:20.863217", "user_id": "01", "username": "admin", "role": "admin", "department_id": null, "action": "AI_COMMAND", "entity_type": "chatbot", "entity_id": "01", "details": {"command": "give me names of all the medicines in the database", "command_type": "medicine_names_list"}, "ip_address": "localhost", "user_agent": "Flask App"}, {"id": "112", "timestamp": "2025-07-26T11:42:21.027336", "user_id": "01", "username": "admin", "role": "admin", "department_id": null, "action": "QUERY", "entity_type": "chatbot", "entity_id": "01", "details": {"query": "show me all medicine names"}, "ip_address": "localhost", "user_agent": "Flask App"}, {"id": "113", "timestamp": "2025-07-26T11:42:21.044417", "user_id": "01", "username": "admin", "role": "admin", "department_id": null, "action": "AI_COMMAND", "entity_type": "chatbot", "entity_id": "01", "details": {"command": "show me all medicine names", "command_type": "medicine_names_list"}, "ip_address": "localhost", "user_agent": "Flask App"}, {"id": "114", "timestamp": "2025-07-26T11:42:21.212149", "user_id": "01", "username": "admin", "role": "admin", "department_id": null, "action": "QUERY", "entity_type": "chatbot", "entity_id": "01", "details": {"query": "list all medicines"}, "ip_address": "localhost", "user_agent": "Flask App"}, {"id": "115", "timestamp": "2025-07-26T11:42:21.379593", "user_id": "01", "username": "admin", "role": "admin", "department_id": null, "action": "QUERY", "entity_type": "chatbot", "entity_id": "01", "details": {"query": "what medicines do we have"}, "ip_address": "localhost", "user_agent": "Flask App"}, {"id": "116", "timestamp": "2025-07-26T11:42:47.000457", "user_id": "01", "username": "admin", "role": "admin", "department_id": null, "action": "LOGIN", "entity_type": "user", "entity_id": "01", "details": {"username": "admin", "role": "admin"}, "ip_address": "localhost", "user_agent": "Flask App"}, {"id": "117", "timestamp": "2025-07-26T11:42:47.204382", "user_id": "01", "username": "admin", "role": "admin", "department_id": null, "action": "QUERY", "entity_type": "chatbot", "entity_id": "01", "details": {"query": "give me names of all the medicines in the database"}, "ip_address": "localhost", "user_agent": "Flask App"}, {"id": "118", "timestamp": "2025-07-26T11:42:47.219901", "user_id": "01", "username": "admin", "role": "admin", "department_id": null, "action": "AI_COMMAND", "entity_type": "chatbot", "entity_id": "01", "details": {"command": "give me names of all the medicines in the database", "command_type": "medicine_names_list"}, "ip_address": "localhost", "user_agent": "Flask App"}, {"id": "119", "timestamp": "2025-07-26T11:42:47.414212", "user_id": "01", "username": "admin", "role": "admin", "department_id": null, "action": "QUERY", "entity_type": "chatbot", "entity_id": "01", "details": {"query": "show me all medicine names"}, "ip_address": "localhost", "user_agent": "Flask App"}, {"id": "120", "timestamp": "2025-07-26T11:42:47.439563", "user_id": "01", "username": "admin", "role": "admin", "department_id": null, "action": "AI_COMMAND", "entity_type": "chatbot", "entity_id": "01", "details": {"command": "show me all medicine names", "command_type": "medicine_names_list"}, "ip_address": "localhost", "user_agent": "Flask App"}, {"id": "121", "timestamp": "2025-07-26T11:42:47.623314", "user_id": "01", "username": "admin", "role": "admin", "department_id": null, "action": "QUERY", "entity_type": "chatbot", "entity_id": "01", "details": {"query": "list all medicines"}, "ip_address": "localhost", "user_agent": "Flask App"}, {"id": "122", "timestamp": "2025-07-26T11:42:47.640416", "user_id": "01", "username": "admin", "role": "admin", "department_id": null, "action": "AI_COMMAND", "entity_type": "chatbot", "entity_id": "01", "details": {"command": "list all medicines", "command_type": "medicine_names_list"}, "ip_address": "localhost", "user_agent": "Flask App"}, {"id": "123", "timestamp": "2025-07-26T11:42:47.810998", "user_id": "01", "username": "admin", "role": "admin", "department_id": null, "action": "QUERY", "entity_type": "chatbot", "entity_id": "01", "details": {"query": "what medicines do we have"}, "ip_address": "localhost", "user_agent": "Flask App"}, {"id": "124", "timestamp": "2025-07-26T11:42:47.825182", "user_id": "01", "username": "admin", "role": "admin", "department_id": null, "action": "AI_COMMAND", "entity_type": "chatbot", "entity_id": "01", "details": {"command": "what medicines do we have", "command_type": "medicine_names_list"}, "ip_address": "localhost", "user_agent": "Flask App"}, {"id": "125", "timestamp": "2025-07-26T11:47:32.915290", "user_id": "01", "username": "admin", "role": "admin", "department_id": null, "action": "QUERY", "entity_type": "chatbot", "entity_id": "01", "details": {"query": "list all the medicins named"}, "ip_address": "localhost", "user_agent": "Flask App"}, {"id": "126", "timestamp": "2025-07-26T11:48:05.537316", "user_id": "01", "username": "admin", "role": "admin", "department_id": null, "action": "QUERY", "entity_type": "chatbot", "entity_id": "01", "details": {"query": "hi"}, "ip_address": "localhost", "user_agent": "Flask App"}, {"id": "127", "timestamp": "2025-07-26T11:48:28.944787", "user_id": "01", "username": "admin", "role": "admin", "department_id": null, "action": "QUERY", "entity_type": "chatbot", "entity_id": "01", "details": {"query": "names of all the medicines"}, "ip_address": "localhost", "user_agent": "Flask App"}, {"id": "128", "timestamp": "2025-07-26T11:48:41.684392", "user_id": "01", "username": "admin", "role": "admin", "department_id": null, "action": "QUERY", "entity_type": "chatbot", "entity_id": "01", "details": {"query": "What is the highest stock medicine and its current level?"}, "ip_address": "localhost", "user_agent": "Flask App"}, {"id": "129", "timestamp": "2025-07-26T11:49:29.549171", "user_id": "01", "username": "admin", "role": "admin", "department_id": null, "action": "QUERY", "entity_type": "chatbot", "entity_id": "01", "details": {"query": "show me all medicine names"}, "ip_address": "localhost", "user_agent": "Flask App"}, {"id": "130", "timestamp": "2025-07-26T12:12:09.591326", "user_id": "01", "username": "admin", "role": "admin", "department_id": null, "action": "QUERY", "entity_type": "chatbot", "entity_id": "01", "details": {"query": "hi"}, "ip_address": "localhost", "user_agent": "Flask App"}, {"id": "131", "timestamp": "2025-07-26T12:12:33.419912", "user_id": "01", "username": "admin", "role": "admin", "department_id": null, "action": "QUERY", "entity_type": "chatbot", "entity_id": "01", "details": {"query": "tell me how u can help me"}, "ip_address": "localhost", "user_agent": "Flask App"}, {"id": "132", "timestamp": "2025-07-26T12:12:55.905141", "user_id": "01", "username": "admin", "role": "admin", "department_id": null, "action": "LOGIN", "entity_type": "user", "entity_id": "01", "details": {"username": "admin", "role": "admin"}, "ip_address": "localhost", "user_agent": "Flask App"}, {"id": "133", "timestamp": "2025-07-26T12:13:25.215495", "user_id": "01", "username": "admin", "role": "admin", "department_id": null, "action": "QUERY", "entity_type": "chatbot", "entity_id": "01", "details": {"query": "how many medicines do we have"}, "ip_address": "localhost", "user_agent": "Flask App"}, {"id": "134", "timestamp": "2025-07-26T12:13:40.486329", "user_id": "01", "username": "admin", "role": "admin", "department_id": null, "action": "QUERY", "entity_type": "chatbot", "entity_id": "01", "details": {"query": "medicin count"}, "ip_address": "localhost", "user_agent": "Flask App"}, {"id": "135", "timestamp": "2025-07-26T12:14:21.906859", "user_id": "01", "username": "admin", "role": "admin", "department_id": null, "action": "QUERY", "entity_type": "chatbot", "entity_id": "01", "details": {"query": "Which department has the lowest average stock levels?"}, "ip_address": "localhost", "user_agent": "Flask App"}, {"id": "136", "timestamp": "2025-07-26T12:14:31.739242", "user_id": "01", "username": "admin", "role": "admin", "department_id": null, "action": "QUERY", "entity_type": "chatbot", "entity_id": "01", "details": {"query": "What medicines are running low on stock?"}, "ip_address": "localhost", "user_agent": "Flask App"}, {"id": "137", "timestamp": "2025-07-26T12:14:54.574890", "user_id": "01", "username": "admin", "role": "admin", "department_id": null, "action": "QUERY", "entity_type": "chatbot", "entity_id": "01", "details": {"query": "Which patient has consumed the most medicines this month?"}, "ip_address": "localhost", "user_agent": "Flask App"}, {"id": "138", "timestamp": "2025-07-26T12:15:17.445389", "user_id": "01", "username": "admin", "role": "admin", "department_id": null, "action": "QUERY", "entity_type": "chatbot", "entity_id": "01", "details": {"query": "a"}, "ip_address": "localhost", "user_agent": "Flask App"}, {"id": "139", "timestamp": "2025-07-26T12:15:32.806772", "user_id": "01", "username": "admin", "role": "admin", "department_id": null, "action": "QUERY", "entity_type": "chatbot", "entity_id": "01", "details": {"query": "What is the highest stock medicine and its current level?"}, "ip_address": "localhost", "user_agent": "Flask App"}, {"id": "140", "timestamp": "2025-07-26T12:15:39.102649", "user_id": "01", "username": "admin", "role": "admin", "department_id": null, "action": "QUERY", "entity_type": "chatbot", "entity_id": "01", "details": {"query": "Show me the top 5 most expensive purchases this year"}, "ip_address": "localhost", "user_agent": "Flask App"}, {"id": "141", "timestamp": "2025-07-26T12:15:39.511124", "user_id": "01", "username": "admin", "role": "admin", "department_id": null, "action": "QUERY", "entity_type": "chatbot", "entity_id": "01", "details": {"query": "Show me the top 5 most expensive purchases this year"}, "ip_address": "localhost", "user_agent": "Flask App"}, {"id": "142", "timestamp": "2025-07-26T12:15:42.748239", "user_id": "01", "username": "admin", "role": "admin", "department_id": null, "action": "QUERY", "entity_type": "chatbot", "entity_id": "01", "details": {"query": "medicine count"}, "ip_address": "localhost", "user_agent": "Flask App"}, {"id": "143", "timestamp": "2025-07-26T12:15:42.775965", "user_id": "01", "username": "admin", "role": "admin", "department_id": null, "action": "AI_COMMAND", "entity_type": "chatbot", "entity_id": "01", "details": {"command": "medicine count", "corrected_command": "medicine count", "command_type": "medicines_count"}, "ip_address": "localhost", "user_agent": "Flask App"}, {"id": "144", "timestamp": "2025-07-26T12:15:45.096402", "user_id": "01", "username": "admin", "role": "admin", "department_id": null, "action": "QUERY", "entity_type": "chatbot", "entity_id": "01", "details": {"query": "Show me medicines expiring within 30 days"}, "ip_address": "localhost", "user_agent": "Flask App"}, {"id": "145", "timestamp": "2025-07-26T12:16:03.016840", "user_id": "01", "username": "admin", "role": "admin", "department_id": null, "action": "QUERY", "entity_type": "chatbot", "entity_id": "01", "details": {"query": "medicin list"}, "ip_address": "localhost", "user_agent": "Flask App"}, {"id": "146", "timestamp": "2025-07-26T12:16:03.080193", "user_id": "01", "username": "admin", "role": "admin", "department_id": null, "action": "AI_COMMAND", "entity_type": "chatbot", "entity_id": "01", "details": {"command": "medicin list", "corrected_command": "medicine list", "command_type": "suppliers_by_type"}, "ip_address": "localhost", "user_agent": "Flask App"}, {"id": "147", "timestamp": "2025-07-26T12:16:16.599592", "user_id": "01", "username": "admin", "role": "admin", "department_id": null, "action": "QUERY", "entity_type": "chatbot", "entity_id": "01", "details": {"query": "list all medicines"}, "ip_address": "localhost", "user_agent": "Flask App"}, {"id": "148", "timestamp": "2025-07-26T12:16:16.636664", "user_id": "01", "username": "admin", "role": "admin", "department_id": null, "action": "AI_COMMAND", "entity_type": "chatbot", "entity_id": "01", "details": {"command": "list all medicines", "corrected_command": "list all medicine", "command_type": "medicines_list"}, "ip_address": "localhost", "user_agent": "Flask App"}, {"id": "149", "timestamp": "2025-07-26T12:16:35.446193", "user_id": "01", "username": "admin", "role": "admin", "department_id": null, "action": "QUERY", "entity_type": "chatbot", "entity_id": "01", "details": {"query": "tell me about medicines and patients"}, "ip_address": "localhost", "user_agent": "Flask App"}, {"id": "150", "timestamp": "2025-07-26T12:16:45.189848", "user_id": "01", "username": "admin", "role": "admin", "department_id": null, "action": "QUERY", "entity_type": "chatbot", "entity_id": "01", "details": {"query": "count medicines"}, "ip_address": "localhost", "user_agent": "Flask App"}, {"id": "151", "timestamp": "2025-07-26T12:16:45.259722", "user_id": "01", "username": "admin", "role": "admin", "department_id": null, "action": "AI_COMMAND", "entity_type": "chatbot", "entity_id": "01", "details": {"command": "count medicine", "corrected_command": "count medicine", "command_type": "medicines_count"}, "ip_address": "localhost", "user_agent": "Flask App"}, {"id": "152", "timestamp": "2025-07-26T12:16:51.775740", "user_id": "01", "username": "admin", "role": "admin", "department_id": null, "action": "QUERY", "entity_type": "chatbot", "entity_id": "01", "details": {"query": "a"}, "ip_address": "localhost", "user_agent": "Flask App"}, {"id": "153", "timestamp": "2025-07-26T12:17:06.145467", "user_id": "01", "username": "admin", "role": "admin", "department_id": null, "action": "QUERY", "entity_type": "chatbot", "entity_id": "01", "details": {"query": "a"}, "ip_address": "localhost", "user_agent": "Flask App"}, {"id": "154", "timestamp": "2025-07-26T12:17:07.784078", "user_id": "01", "username": "admin", "role": "admin", "department_id": null, "action": "QUERY", "entity_type": "chatbot", "entity_id": "01", "details": {"query": "list"}, "ip_address": "localhost", "user_agent": "Flask App"}, {"id": "155", "timestamp": "2025-07-26T12:17:13.866505", "user_id": "01", "username": "admin", "role": "admin", "department_id": null, "action": "QUERY", "entity_type": "chatbot", "entity_id": "01", "details": {"query": "What medicines are running low on stock?"}, "ip_address": "localhost", "user_agent": "Flask App"}, {"id": "156", "timestamp": "2025-07-26T12:17:18.567833", "user_id": "01", "username": "admin", "role": "admin", "department_id": null, "action": "QUERY", "entity_type": "chatbot", "entity_id": "01", "details": {"query": "list medicines"}, "ip_address": "localhost", "user_agent": "Flask App"}, {"id": "157", "timestamp": "2025-07-26T12:17:18.598227", "user_id": "01", "username": "admin", "role": "admin", "department_id": null, "action": "AI_COMMAND", "entity_type": "chatbot", "entity_id": "01", "details": {"command": "list medicines", "corrected_command": "list medicine", "command_type": "medicines_list"}, "ip_address": "localhost", "user_agent": "Flask App"}, {"id": "158", "timestamp": "2025-07-26T12:18:39.098819", "user_id": "01", "username": "admin", "role": "admin", "department_id": null, "action": "QUERY", "entity_type": "chatbot", "entity_id": "01", "details": {"query": "show low stock medicines"}, "ip_address": "localhost", "user_agent": "Flask App"}, {"id": "159", "timestamp": "2025-07-26T12:18:39.175405", "user_id": "01", "username": "admin", "role": "admin", "department_id": null, "action": "AI_COMMAND", "entity_type": "chatbot", "entity_id": "01", "details": {"command": "show low stock medicines", "corrected_command": "show low stock medicine", "command_type": "medicines_low_stock"}, "ip_address": "localhost", "user_agent": "Flask App"}, {"id": "160", "timestamp": "2025-07-26T12:18:57.424352", "user_id": "01", "username": "admin", "role": "admin", "department_id": null, "action": "QUERY", "entity_type": "chatbot", "entity_id": "01", "details": {"query": "pateint informaton"}, "ip_address": "localhost", "user_agent": "Flask App"}, {"id": "161", "timestamp": "2025-07-26T13:52:18.979799", "user_id": "01", "username": "admin", "role": "admin", "department_id": null, "action": "QUERY", "entity_type": "chatbot", "entity_id": "01", "details": {"query": "consumption analysis"}, "ip_address": "localhost", "user_agent": "Flask App"}, {"id": "162", "timestamp": "2025-07-26T13:52:22.697466", "user_id": "01", "username": "admin", "role": "admin", "department_id": null, "action": "QUERY", "entity_type": "chatbot", "entity_id": "01", "details": {"query": "a"}, "ip_address": "localhost", "user_agent": "Flask App"}, {"id": "163", "timestamp": "2025-07-26T13:52:45.647496", "user_id": "01", "username": "admin", "role": "admin", "department_id": null, "action": "QUERY", "entity_type": "chatbot", "entity_id": "01", "details": {"query": "b"}, "ip_address": "localhost", "user_agent": "Flask App"}, {"id": "164", "timestamp": "2025-07-26T13:52:55.089940", "user_id": "01", "username": "admin", "role": "admin", "department_id": null, "action": "QUERY", "entity_type": "chatbot", "entity_id": "01", "details": {"query": "a"}, "ip_address": "localhost", "user_agent": "Flask App"}, {"id": "165", "timestamp": "2025-07-26T13:56:21.155368", "user_id": "01", "username": "admin", "role": "admin", "department_id": null, "action": "QUERY", "entity_type": "chatbot", "entity_id": "01", "details": {"query": "hi"}, "ip_address": "localhost", "user_agent": "Flask App"}, {"id": "166", "timestamp": "2025-07-26T13:57:19.573205", "user_id": "01", "username": "admin", "role": "admin", "department_id": null, "action": "QUERY", "entity_type": "chatbot", "entity_id": "01", "details": {"query": "hi"}, "ip_address": "localhost", "user_agent": "Flask App"}, {"id": "167", "timestamp": "2025-07-26T13:57:43.875844", "user_id": "01", "username": "admin", "role": "admin", "department_id": null, "action": "QUERY", "entity_type": "chatbot", "entity_id": "01", "details": {"query": "rack Medicine Stock"}, "ip_address": "localhost", "user_agent": "Flask App"}, {"id": "168", "timestamp": "2025-07-26T13:59:36.254225", "user_id": "01", "username": "admin", "role": "admin", "department_id": null, "action": "QUERY", "entity_type": "chatbot", "entity_id": "01", "details": {"query": "supplier list"}, "ip_address": "localhost", "user_agent": "Flask App"}, {"id": "169", "timestamp": "2025-07-26T14:00:04.286709", "user_id": "01", "username": "admin", "role": "admin", "department_id": null, "action": "QUERY", "entity_type": "chatbot", "entity_id": "01", "details": {"query": "supplier list"}, "ip_address": "localhost", "user_agent": "Flask App"}, {"id": "170", "timestamp": "2025-07-26T14:01:08.638858", "user_id": "01", "username": "admin", "role": "admin", "department_id": null, "action": "QUERY", "entity_type": "chatbot", "entity_id": "01", "details": {"query": "supplier list"}, "ip_address": "localhost", "user_agent": "Flask App"}, {"id": "171", "timestamp": "2025-07-26T14:01:11.342786", "user_id": "01", "username": "admin", "role": "admin", "department_id": null, "action": "AI_COMMAND", "entity_type": "chatbot", "entity_id": "anonymous", "details": {"command": "supplier list", "corrected_command": "supplier list", "command_type": "suppliers_performance"}, "ip_address": "localhost", "user_agent": "Flask App"}, {"id": "172", "timestamp": "2025-07-26T14:01:26.572575", "user_id": "01", "username": "admin", "role": "admin", "department_id": null, "action": "QUERY", "entity_type": "chatbot", "entity_id": "01", "details": {"query": "supplier list"}, "ip_address": "localhost", "user_agent": "Flask App"}, {"id": "173", "timestamp": "2025-07-26T14:01:52.123958", "user_id": "01", "username": "admin", "role": "admin", "department_id": null, "action": "QUERY", "entity_type": "chatbot", "entity_id": "01", "details": {"query": "supplier list"}, "ip_address": "localhost", "user_agent": "Flask App"}, {"id": "174", "timestamp": "2025-07-26T14:02:05.687371", "user_id": "01", "username": "admin", "role": "admin", "department_id": null, "action": "QUERY", "entity_type": "chatbot", "entity_id": "01", "details": {"query": "1"}, "ip_address": "localhost", "user_agent": "Flask App"}, {"id": "175", "timestamp": "2025-07-26T14:02:22.598333", "user_id": "01", "username": "admin", "role": "admin", "department_id": null, "action": "QUERY", "entity_type": "chatbot", "entity_id": "01", "details": {"query": "supplier list"}, "ip_address": "localhost", "user_agent": "Flask App"}, {"id": "176", "timestamp": "2025-07-26T14:02:44.436893", "user_id": "01", "username": "admin", "role": "admin", "department_id": null, "action": "QUERY", "entity_type": "chatbot", "entity_id": "01", "details": {"query": "What is the highest stock medicine and its current level?"}, "ip_address": "localhost", "user_agent": "Flask App"}, {"id": "177", "timestamp": "2025-07-26T14:09:16.004916", "user_id": "01", "username": "admin", "role": "admin", "department_id": null, "action": "QUERY", "entity_type": "chatbot", "entity_id": "01", "details": {"query": "medicine count"}, "ip_address": "localhost", "user_agent": "Flask App"}, {"id": "178", "timestamp": "2025-07-26T14:10:04.196046", "user_id": "01", "username": "admin", "role": "admin", "department_id": null, "action": "QUERY", "entity_type": "chatbot", "entity_id": "01", "details": {"query": "recent purchases"}, "ip_address": "localhost", "user_agent": "Flask App"}, {"id": "179", "timestamp": "2025-07-26T14:10:37.314741", "user_id": "01", "username": "admin", "role": "admin", "department_id": null, "action": "QUERY", "entity_type": "chatbot", "entity_id": "01", "details": {"query": "patient count"}, "ip_address": "localhost", "user_agent": "Flask App"}, {"id": "180", "timestamp": "2025-07-26T14:11:11.720761", "user_id": "01", "username": "admin", "role": "admin", "department_id": null, "action": "QUERY", "entity_type": "chatbot", "entity_id": "01", "details": {"query": "list all medicines"}, "ip_address": "localhost", "user_agent": "Flask App"}, {"id": "181", "timestamp": "2025-07-26T14:11:57.589724", "user_id": "01", "username": "admin", "role": "admin", "department_id": null, "action": "QUERY", "entity_type": "chatbot", "entity_id": "01", "details": {"query": "complete database overview"}, "ip_address": "localhost", "user_agent": "Flask App"}, {"id": "182", "timestamp": "2025-07-26T14:12:27.955948", "user_id": "01", "username": "admin", "role": "admin", "department_id": null, "action": "QUERY", "entity_type": "chatbot", "entity_id": "01", "details": {"query": "help"}, "ip_address": "localhost", "user_agent": "Flask App"}, {"id": "183", "timestamp": "2025-07-26T14:12:59.674350", "user_id": "01", "username": "admin", "role": "admin", "department_id": null, "action": "QUERY", "entity_type": "chatbot", "entity_id": "01", "details": {"query": "transfer count"}, "ip_address": "localhost", "user_agent": "Flask App"}, {"id": "184", "timestamp": "2025-07-26T14:13:33.349827", "user_id": "01", "username": "admin", "role": "admin", "department_id": null, "action": "QUERY", "entity_type": "chatbot", "entity_id": "01", "details": {"query": "recent transfers"}, "ip_address": "localhost", "user_agent": "Flask App"}, {"id": "185", "timestamp": "2025-07-26T14:13:53.155034", "user_id": "01", "username": "admin", "role": "admin", "department_id": null, "action": "QUERY", "entity_type": "chatbot", "entity_id": "01", "details": {"query": "stores by department"}, "ip_address": "localhost", "user_agent": "Flask App"}, {"id": "186", "timestamp": "2025-07-26T14:31:07.678207", "user_id": "01", "username": "admin", "role": "admin", "department_id": null, "action": "CREATE", "entity_type": "user", "entity_id": "08", "details": {"username": "surgery_user", "role": "department_user", "department_id": "07"}, "ip_address": "localhost", "user_agent": "Flask App"}, {"id": "187", "timestamp": "2025-07-26T14:32:16.014572", "user_id": "01", "username": "admin", "role": "admin", "department_id": null, "action": "CREATE", "entity_type": "department", "entity_id": "11", "details": {"name": "Radiology", "responsible_person": "Dr. <PERSON>"}, "ip_address": "localhost", "user_agent": "Flask App"}, {"id": "188", "timestamp": "2025-07-26T14:32:16.558577", "user_id": "01", "username": "admin", "role": "admin", "department_id": null, "action": "CREATE", "entity_type": "user", "entity_id": "09", "details": {"username": "radiology_user", "role": "department_user", "department_id": "11"}, "ip_address": "localhost", "user_agent": "Flask App"}, {"id": "189", "timestamp": "2025-07-26T14:32:49.684697", "user_id": "01", "username": "admin", "role": "admin", "department_id": null, "action": "UPDATE", "entity_type": "user", "entity_id": "09", "details": {"updated_fields": ["username", "role", "name", "email", "department_id", "id", "updated_at"], "username": "radiology_user"}, "ip_address": "localhost", "user_agent": "Flask App"}, {"id": "190", "timestamp": "2025-07-26T14:34:38.484696", "user_id": "01", "username": "admin", "role": "admin", "department_id": null, "action": "DELETE", "entity_type": "user", "entity_id": "09", "details": {"username": "radiology_user", "role": "department_user"}, "ip_address": "localhost", "user_agent": "Flask App"}, {"id": "191", "timestamp": "2025-07-26T14:34:38.512813", "user_id": "01", "username": "admin", "role": "admin", "department_id": null, "action": "DELETE", "entity_type": "department", "entity_id": "11", "details": {"name": "Radiology", "deleted_users": ["radiology_user"], "store_deleted": true}, "ip_address": "localhost", "user_agent": "Flask App"}, {"id": "192", "timestamp": "2025-07-26T17:11:07.804401", "user_id": "01", "username": "admin", "role": "admin", "department_id": null, "action": "LOGIN", "entity_type": "user", "entity_id": "01", "details": {"username": "admin", "role": "admin"}, "ip_address": "localhost", "user_agent": "Flask App"}, {"id": "193", "timestamp": "2025-07-26T17:22:12.003962", "user_id": "01", "username": "admin", "role": "admin", "department_id": null, "action": "CREATE", "entity_type": "medicine", "entity_id": "51", "details": {"name": "Test Medicine", "supplier_id": "01"}, "ip_address": "localhost", "user_agent": "Flask App"}, {"id": "194", "timestamp": "2025-07-27T01:08:32.387659", "user_id": "01", "username": "admin", "role": "admin", "department_id": null, "action": "LOGIN", "entity_type": "user", "entity_id": "01", "details": {"username": "admin", "role": "admin"}, "ip_address": "localhost", "user_agent": "Flask App"}, {"id": "195", "timestamp": "2025-07-27T01:09:04.129870", "user_id": "01", "username": "admin", "role": "admin", "department_id": null, "action": "QUERY", "entity_type": "chatbot", "entity_id": "01", "details": {"query": "Show me a quick overview of medicine inventory"}, "ip_address": "localhost", "user_agent": "Flask App"}, {"id": "196", "timestamp": "2025-07-27T02:37:37.489509", "user_id": "01", "username": "admin", "role": "admin", "department_id": null, "action": "LOGOUT", "entity_type": "user", "entity_id": "01", "details": {"username": "admin"}, "ip_address": "localhost", "user_agent": "Flask App"}, {"id": "197", "timestamp": "2025-07-27T03:12:09.284395", "user_id": "01", "username": "admin", "role": "admin", "department_id": null, "action": "LOGIN", "entity_type": "user", "entity_id": "01", "details": {"username": "admin", "role": "admin"}, "ip_address": "localhost", "user_agent": "Flask App"}, {"id": "198", "timestamp": "2025-07-27T03:12:48.924925", "user_id": "01", "username": "admin", "role": "admin", "department_id": null, "action": "QUERY", "entity_type": "chatbot", "entity_id": "01", "details": {"query": "How many medicines do we have in total?"}, "ip_address": "localhost", "user_agent": "Flask App"}, {"id": "199", "timestamp": "2025-07-27T03:24:23.906783", "user_id": "01", "username": "admin", "role": "admin", "department_id": null, "action": "LOGIN", "entity_type": "user", "entity_id": "01", "details": {"username": "admin", "role": "admin"}, "ip_address": "localhost", "user_agent": "Flask App"}, {"id": "200", "timestamp": "2025-07-27T03:25:39.050736", "user_id": "01", "username": "admin", "role": "admin", "department_id": null, "action": "LOGIN", "entity_type": "user", "entity_id": "01", "details": {"username": "admin", "role": "admin"}, "ip_address": "localhost", "user_agent": "Flask App"}, {"id": "201", "timestamp": "2025-07-27T03:53:20.051334", "user_id": "01", "username": "admin", "role": "admin", "department_id": null, "action": "CREATE", "entity_type": "transfer", "entity_id": "68", "details": {"source_store_id": "01", "destination_store_id": "02", "medicines_count": 1, "requested_by": "Dr. <PERSON>", "approved_by": "Pharmacy Director"}, "ip_address": "localhost", "user_agent": "Flask App"}, {"id": "202", "timestamp": "2025-07-27T03:53:33.285653", "user_id": "01", "username": "admin", "role": "admin", "department_id": null, "action": "VIEW", "entity_type": "transfer", "entity_id": "68", "details": {}, "ip_address": "localhost", "user_agent": "Flask App"}, {"id": "203", "timestamp": "2025-07-27T03:55:31.751255", "user_id": "01", "username": "admin", "role": "admin", "department_id": null, "action": "QUERY", "entity_type": "chatbot", "entity_id": "01", "details": {"query": "Show me the latest transfer with requested by and approved by information"}, "ip_address": "localhost", "user_agent": "Flask App"}, {"id": "204", "timestamp": "2025-07-27T03:55:52.197019", "user_id": "01", "username": "admin", "role": "admin", "department_id": null, "action": "QUERY", "entity_type": "chatbot", "entity_id": "01", "details": {"query": "medicine count"}, "ip_address": "localhost", "user_agent": "Flask App"}, {"id": "205", "timestamp": "2025-07-27T08:59:53.323902", "user_id": "01", "username": "admin", "role": "admin", "department_id": null, "action": "LOGIN", "entity_type": "user", "entity_id": "01", "details": {"username": "admin", "role": "admin"}, "ip_address": "localhost", "user_agent": "Flask App"}, {"id": "206", "timestamp": "2025-07-27T13:23:25.564511", "user_id": "system", "username": "system", "role": "system", "department_id": null, "action": "AUTH_ATTEMPT", "entity_type": "user", "entity_id": null, "details": {"username": "admin", "timestamp": "2025-07-27T13:23:25.561036", "ip_address": "localhost"}, "ip_address": "localhost", "user_agent": "Flask App"}, {"id": "207", "timestamp": "2025-07-27T13:23:25.580513", "user_id": "system", "username": "system", "role": "system", "department_id": null, "action": "AUTH_FAILED", "entity_type": "user", "entity_id": "01", "details": {"username": "admin", "reason": "invalid_password", "failed_attempts": 1, "timestamp": "2025-07-27T13:23:25.570716"}, "ip_address": "localhost", "user_agent": "Flask App"}, {"id": "208", "timestamp": "2025-07-27T13:23:25.599327", "user_id": "system", "username": "system", "role": "system", "department_id": null, "action": "UPDATE", "entity_type": "user", "entity_id": "01", "details": {"updated_fields": ["failed_login_attempts", "last_failed_login"], "username": "admin", "updated_by": "system", "password_changed": false}, "ip_address": "localhost", "user_agent": "Flask App"}, {"id": "209", "timestamp": "2025-07-27T13:24:06.539670", "user_id": "system", "username": "system", "role": "system", "department_id": null, "action": "AUTH_ATTEMPT", "entity_type": "user", "entity_id": null, "details": {"username": "admin", "timestamp": "2025-07-27T13:24:06.537696", "ip_address": "localhost"}, "ip_address": "localhost", "user_agent": "Flask App"}, {"id": "210", "timestamp": "2025-07-27T13:24:06.557209", "user_id": "system", "username": "system", "role": "system", "department_id": null, "action": "UPDATE", "entity_type": "user", "entity_id": "01", "details": {"updated_fields": ["failed_login_attempts", "last_successful_login"], "username": "admin", "updated_by": "system", "password_changed": false}, "ip_address": "localhost", "user_agent": "Flask App"}, {"id": "211", "timestamp": "2025-07-27T13:24:06.572629", "user_id": "system", "username": "system", "role": "system", "department_id": null, "action": "AUTH_SUCCESS", "entity_type": "user", "entity_id": "01", "details": {"username": "admin", "timestamp": "2025-07-27T13:24:06.563830", "note": "legacy_password_used"}, "ip_address": "localhost", "user_agent": "Flask App"}, {"id": "212", "timestamp": "2025-07-27T13:24:06.588626", "user_id": "01", "username": "admin", "role": "admin", "department_id": null, "action": "LOGIN", "entity_type": "user", "entity_id": "01", "details": {"username": "admin", "role": "admin"}, "ip_address": "localhost", "user_agent": "Flask App"}, {"id": "213", "timestamp": "2025-07-27T13:25:13.750799", "user_id": "01", "username": "admin", "role": "admin", "department_id": null, "action": "CREATE", "entity_type": "department", "entity_id": "19", "details": {"name": "Test Department", "responsible_person": "Dr. Test Person"}, "ip_address": "localhost", "user_agent": "Flask App"}, {"id": "214", "timestamp": "2025-07-27T13:25:14.212382", "user_id": "01", "username": "admin", "role": "admin", "department_id": null, "action": "CREATE", "entity_type": "user", "entity_id": "09", "details": {"username": "test_department_user", "role": "department_user", "department_id": "19", "created_by": "admin", "password_strength_score": 120}, "ip_address": "localhost", "user_agent": "Flask App"}, {"id": "215", "timestamp": "2025-07-27T13:26:11.619545", "user_id": "01", "username": "admin", "role": "admin", "department_id": null, "action": "VIEW", "entity_type": "profile", "entity_id": "01", "details": {}, "ip_address": "localhost", "user_agent": "Flask App"}, {"id": "216", "timestamp": "2025-07-27T13:26:13.508667", "user_id": "01", "username": "admin", "role": "admin", "department_id": null, "action": "VIEW", "entity_type": "profile", "entity_id": "01", "details": {}, "ip_address": "localhost", "user_agent": "Flask App"}, {"id": "217", "timestamp": "2025-07-27T13:26:13.894153", "user_id": "01", "username": "admin", "role": "admin", "department_id": null, "action": "VIEW", "entity_type": "profile", "entity_id": "01", "details": {}, "ip_address": "localhost", "user_agent": "Flask App"}, {"id": "218", "timestamp": "2025-07-27T13:26:14.217457", "user_id": "01", "username": "admin", "role": "admin", "department_id": null, "action": "VIEW", "entity_type": "profile", "entity_id": "01", "details": {}, "ip_address": "localhost", "user_agent": "Flask App"}, {"id": "219", "timestamp": "2025-07-27T13:44:42.763332", "user_id": "01", "username": "admin", "role": "admin", "department_id": null, "action": "LOGOUT", "entity_type": "user", "entity_id": "01", "details": {"username": "admin"}, "ip_address": "localhost", "user_agent": "Flask App"}, {"id": "220", "timestamp": "2025-07-27T13:45:04.333326", "user_id": "system", "username": "system", "role": "system", "department_id": null, "action": "AUTH_ATTEMPT", "entity_type": "user", "entity_id": null, "details": {"username": "pharmacy", "timestamp": "2025-07-27T13:45:04.331388", "ip_address": "localhost"}, "ip_address": "localhost", "user_agent": "Flask App"}, {"id": "221", "timestamp": "2025-07-27T13:45:04.349845", "user_id": "system", "username": "system", "role": "system", "department_id": null, "action": "AUTH_SUCCESS", "entity_type": "user", "entity_id": "02", "details": {"username": "pharmacy", "timestamp": "2025-07-27T13:45:04.340147", "note": "legacy_password_used"}, "ip_address": "localhost", "user_agent": "Flask App"}, {"id": "222", "timestamp": "2025-07-27T13:45:04.365872", "user_id": "02", "username": "pharmacy", "role": "department_user", "department_id": "01", "action": "LOGIN", "entity_type": "user", "entity_id": "02", "details": {"username": "pharmacy", "role": "department_user"}, "ip_address": "localhost", "user_agent": "Flask App"}, {"id": "223", "timestamp": "2025-07-27T13:46:08.326294", "user_id": "02", "username": "pharmacy", "role": "department_user", "department_id": "01", "action": "LOGOUT", "entity_type": "user", "entity_id": "02", "details": {"username": "pharmacy"}, "ip_address": "localhost", "user_agent": "Flask App"}, {"id": "224", "timestamp": "2025-07-27T13:48:31.917270", "user_id": "system", "username": "system", "role": "system", "department_id": null, "action": "AUTH_ATTEMPT", "entity_type": "user", "entity_id": null, "details": {"username": "admin", "timestamp": "2025-07-27T13:48:31.915131", "ip_address": "localhost"}, "ip_address": "localhost", "user_agent": "Flask App"}, {"id": "225", "timestamp": "2025-07-27T13:48:31.933443", "user_id": "system", "username": "system", "role": "system", "department_id": null, "action": "AUTH_SUCCESS", "entity_type": "user", "entity_id": "01", "details": {"username": "admin", "timestamp": "2025-07-27T13:48:31.923860", "note": "legacy_password_used"}, "ip_address": "localhost", "user_agent": "Flask App"}, {"id": "226", "timestamp": "2025-07-27T13:48:31.950396", "user_id": "01", "username": "admin", "role": "admin", "department_id": null, "action": "LOGIN", "entity_type": "user", "entity_id": "01", "details": {"username": "admin", "role": "admin"}, "ip_address": "localhost", "user_agent": "Flask App"}, {"id": "227", "timestamp": "2025-07-27T14:27:58.457904", "user_id": "system", "username": "system", "role": "system", "department_id": null, "action": "AUTH_ATTEMPT", "entity_type": "user", "entity_id": null, "details": {"username": "admin", "timestamp": "2025-07-27T14:27:58.455879", "ip_address": "localhost"}, "ip_address": "localhost", "user_agent": "Flask App"}, {"id": "228", "timestamp": "2025-07-27T14:27:58.476065", "user_id": "system", "username": "system", "role": "system", "department_id": null, "action": "AUTH_SUCCESS", "entity_type": "user", "entity_id": "01", "details": {"username": "admin", "timestamp": "2025-07-27T14:27:58.465702", "note": "legacy_password_used"}, "ip_address": "localhost", "user_agent": "Flask App"}, {"id": "229", "timestamp": "2025-07-27T14:27:58.494053", "user_id": "01", "username": "admin", "role": "admin", "department_id": null, "action": "LOGIN", "entity_type": "user", "entity_id": "01", "details": {"username": "admin", "role": "admin"}, "ip_address": "localhost", "user_agent": "Flask App"}, {"id": "230", "timestamp": "2025-07-27T14:28:32.975616", "user_id": "01", "username": "admin", "role": "admin", "department_id": null, "action": "CREATE", "entity_type": "department", "entity_id": "20", "details": {"name": "Test Department 142830", "responsible_person": "Dr. Test Person"}, "ip_address": "localhost", "user_agent": "Flask App"}, {"id": "231", "timestamp": "2025-07-27T14:28:33.404164", "user_id": "01", "username": "admin", "role": "admin", "department_id": null, "action": "CREATE", "entity_type": "user", "entity_id": "10", "details": {"username": "test_department_user1", "role": "department_user", "department_id": "20", "created_by": "admin", "password_strength_score": 120}, "ip_address": "localhost", "user_agent": "Flask App"}, {"id": "232", "timestamp": "2025-07-27T14:30:32.921252", "user_id": "system", "username": "system", "role": "system", "department_id": null, "action": "AUTH_ATTEMPT", "entity_type": "user", "entity_id": null, "details": {"username": "admin", "timestamp": "2025-07-27T14:30:32.917094", "ip_address": "localhost"}, "ip_address": "localhost", "user_agent": "Flask App"}, {"id": "233", "timestamp": "2025-07-27T14:30:32.942253", "user_id": "system", "username": "system", "role": "system", "department_id": null, "action": "AUTH_SUCCESS", "entity_type": "user", "entity_id": "01", "details": {"username": "admin", "timestamp": "2025-07-27T14:30:32.930239", "note": "legacy_password_used"}, "ip_address": "localhost", "user_agent": "Flask App"}, {"id": "234", "timestamp": "2025-07-27T14:30:32.959501", "user_id": "01", "username": "admin", "role": "admin", "department_id": null, "action": "LOGIN", "entity_type": "user", "entity_id": "01", "details": {"username": "admin", "role": "admin"}, "ip_address": "localhost", "user_agent": "Flask App"}, {"id": "235", "timestamp": "2025-07-27T14:30:36.501345", "user_id": "01", "username": "admin", "role": "admin", "department_id": null, "action": "CREATE", "entity_type": "medicine", "entity_id": "105", "details": {"name": "Test Medicine 143034", "supplier_id": "01"}, "ip_address": "localhost", "user_agent": "Flask App"}, {"id": "236", "timestamp": "2025-07-27T14:40:12.938095", "user_id": "system", "username": "system", "role": "system", "department_id": null, "action": "AUTH_ATTEMPT", "entity_type": "user", "entity_id": null, "details": {"username": "admin", "timestamp": "2025-07-27T14:40:12.933113", "ip_address": "localhost"}, "ip_address": "localhost", "user_agent": "Flask App"}, {"id": "237", "timestamp": "2025-07-27T14:40:12.965675", "user_id": "system", "username": "system", "role": "system", "department_id": null, "action": "AUTH_SUCCESS", "entity_type": "user", "entity_id": "01", "details": {"username": "admin", "timestamp": "2025-07-27T14:40:12.951571", "note": "legacy_password_used"}, "ip_address": "localhost", "user_agent": "Flask App"}, {"id": "238", "timestamp": "2025-07-27T14:40:12.987832", "user_id": "01", "username": "admin", "role": "admin", "department_id": null, "action": "LOGIN", "entity_type": "user", "entity_id": "01", "details": {"username": "admin", "role": "admin"}, "ip_address": "localhost", "user_agent": "Flask App"}, {"id": "239", "timestamp": "2025-07-27T15:59:43.583910", "user_id": "system", "username": "system", "role": "system", "department_id": null, "action": "AUTH_ATTEMPT", "entity_type": "user", "entity_id": null, "details": {"username": "admin", "timestamp": "2025-07-27T15:59:43.581130", "ip_address": "localhost"}, "ip_address": "localhost", "user_agent": "Flask App"}, {"id": "240", "timestamp": "2025-07-27T15:59:43.605228", "user_id": "system", "username": "system", "role": "system", "department_id": null, "action": "AUTH_FAILED", "entity_type": "user", "entity_id": "01", "details": {"username": "admin", "reason": "invalid_password", "failed_attempts": 1, "timestamp": "2025-07-27T15:59:43.592588"}, "ip_address": "localhost", "user_agent": "Flask App"}, {"id": "241", "timestamp": "2025-07-27T15:59:43.626414", "user_id": "system", "username": "system", "role": "system", "department_id": null, "action": "UPDATE", "entity_type": "user", "entity_id": "01", "details": {"updated_fields": ["failed_login_attempts", "last_failed_login"], "username": "admin", "updated_by": "system", "password_changed": false}, "ip_address": "localhost", "user_agent": "Flask App"}, {"id": "242", "timestamp": "2025-07-27T16:00:03.835249", "user_id": "system", "username": "system", "role": "system", "department_id": null, "action": "AUTH_ATTEMPT", "entity_type": "user", "entity_id": null, "details": {"username": "admin", "timestamp": "2025-07-27T16:00:03.830006", "ip_address": "localhost"}, "ip_address": "localhost", "user_agent": "Flask App"}, {"id": "243", "timestamp": "2025-07-27T16:00:03.861305", "user_id": "system", "username": "system", "role": "system", "department_id": null, "action": "UPDATE", "entity_type": "user", "entity_id": "01", "details": {"updated_fields": ["failed_login_attempts", "last_successful_login"], "username": "admin", "updated_by": "system", "password_changed": false}, "ip_address": "localhost", "user_agent": "Flask App"}, {"id": "244", "timestamp": "2025-07-27T16:00:03.878795", "user_id": "system", "username": "system", "role": "system", "department_id": null, "action": "AUTH_SUCCESS", "entity_type": "user", "entity_id": "01", "details": {"username": "admin", "timestamp": "2025-07-27T16:00:03.868951", "note": "legacy_password_used"}, "ip_address": "localhost", "user_agent": "Flask App"}, {"id": "245", "timestamp": "2025-07-27T16:00:03.896183", "user_id": "01", "username": "admin", "role": "admin", "department_id": null, "action": "LOGIN", "entity_type": "user", "entity_id": "01", "details": {"username": "admin", "role": "admin"}, "ip_address": "localhost", "user_agent": "Flask App"}, {"id": "246", "timestamp": "2025-07-27T22:31:29.982661", "user_id": "system", "username": "system", "role": "system", "department_id": null, "action": "AUTH_ATTEMPT", "entity_type": "user", "entity_id": null, "details": {"username": "admin", "timestamp": "2025-07-27T22:31:29.980465", "ip_address": "localhost"}, "ip_address": "localhost", "user_agent": "Flask App"}, {"id": "247", "timestamp": "2025-07-27T22:31:30.005177", "user_id": "system", "username": "system", "role": "system", "department_id": null, "action": "AUTH_SUCCESS", "entity_type": "user", "entity_id": "01", "details": {"username": "admin", "timestamp": "2025-07-27T22:31:29.991594", "note": "legacy_password_used"}, "ip_address": "localhost", "user_agent": "Flask App"}, {"id": "248", "timestamp": "2025-07-27T22:31:30.029651", "user_id": "01", "username": "admin", "role": "admin", "department_id": null, "action": "LOGIN", "entity_type": "user", "entity_id": "01", "details": {"username": "admin", "role": "admin"}, "ip_address": "localhost", "user_agent": "Flask App"}, {"id": "249", "timestamp": "2025-07-27T22:31:30.373027", "user_id": "01", "username": "admin", "role": "admin", "department_id": null, "action": "AUTH_ATTEMPT", "entity_type": "user", "entity_id": null, "details": {"username": "admin", "timestamp": "2025-07-27T22:31:30.367543", "ip_address": "localhost"}, "ip_address": "localhost", "user_agent": "Flask App"}, {"id": "250", "timestamp": "2025-07-27T22:31:30.400142", "user_id": "01", "username": "admin", "role": "admin", "department_id": null, "action": "AUTH_SUCCESS", "entity_type": "user", "entity_id": "01", "details": {"username": "admin", "timestamp": "2025-07-27T22:31:30.387627", "note": "legacy_password_used"}, "ip_address": "localhost", "user_agent": "Flask App"}, {"id": "251", "timestamp": "2025-07-27T22:31:30.419251", "user_id": "01", "username": "admin", "role": "admin", "department_id": null, "action": "LOGIN", "entity_type": "user", "entity_id": "01", "details": {"username": "admin", "role": "admin"}, "ip_address": "localhost", "user_agent": "Flask App"}, {"id": "252", "timestamp": "2025-07-27T23:07:17.603320", "user_id": "01", "username": "admin", "role": "admin", "department_id": null, "action": "DELETE", "entity_type": "user", "entity_id": "09", "details": {"username": "test_department_user", "role": "department_user"}, "ip_address": "localhost", "user_agent": "Flask App"}, {"id": "253", "timestamp": "2025-07-27T23:07:17.625875", "user_id": "01", "username": "admin", "role": "admin", "department_id": null, "action": "DELETE", "entity_type": "department", "entity_id": "19", "details": {"name": "Test Department", "deleted_users": ["test_department_user"], "store_deleted": true}, "ip_address": "localhost", "user_agent": "Flask App"}, {"id": "254", "timestamp": "2025-07-27T23:07:23.966048", "user_id": "01", "username": "admin", "role": "admin", "department_id": null, "action": "DELETE", "entity_type": "user", "entity_id": "10", "details": {"username": "test_department_user1", "role": "department_user"}, "ip_address": "localhost", "user_agent": "Flask App"}, {"id": "255", "timestamp": "2025-07-27T23:07:23.988758", "user_id": "01", "username": "admin", "role": "admin", "department_id": null, "action": "DELETE", "entity_type": "department", "entity_id": "20", "details": {"name": "Test Department 142830", "deleted_users": ["test_department_user1"], "store_deleted": true}, "ip_address": "localhost", "user_agent": "Flask App"}, {"id": "256", "timestamp": "2025-07-27T23:10:59.261268", "user_id": "01", "username": "admin", "role": "admin", "department_id": null, "action": "CREATE", "entity_type": "department", "entity_id": "19", "details": {"name": "TEST-TEST", "responsible_person": "test"}, "ip_address": "localhost", "user_agent": "Flask App"}, {"id": "257", "timestamp": "2025-07-27T23:10:59.700544", "user_id": "01", "username": "admin", "role": "admin", "department_id": null, "action": "CREATE", "entity_type": "user", "entity_id": "09", "details": {"username": "test_test_user", "role": "department_user", "department_id": "19", "created_by": "admin", "password_strength_score": 120}, "ip_address": "localhost", "user_agent": "Flask App"}, {"id": "258", "timestamp": "2025-07-27T23:34:51.348956", "user_id": "system", "username": "system", "role": "system", "department_id": null, "action": "AUTH_ATTEMPT", "entity_type": "user", "entity_id": null, "details": {"username": "admin", "timestamp": "2025-07-27T23:34:51.345731", "ip_address": "localhost"}, "ip_address": "localhost", "user_agent": "Flask App"}, {"id": "259", "timestamp": "2025-07-27T23:34:51.369972", "user_id": "system", "username": "system", "role": "system", "department_id": null, "action": "AUTH_SUCCESS", "entity_type": "user", "entity_id": "01", "details": {"username": "admin", "timestamp": "2025-07-27T23:34:51.358641", "note": "legacy_password_used"}, "ip_address": "localhost", "user_agent": "Flask App"}, {"id": "260", "timestamp": "2025-07-27T23:34:51.389700", "user_id": "01", "username": "admin", "role": "admin", "department_id": null, "action": "LOGIN", "entity_type": "user", "entity_id": "01", "details": {"username": "admin", "role": "admin"}, "ip_address": "localhost", "user_agent": "Flask App"}, {"id": "261", "timestamp": "2025-07-28T02:32:10.706745", "user_id": "system", "username": "system", "role": "system", "department_id": null, "action": "AUTH_ATTEMPT", "entity_type": "user", "entity_id": null, "details": {"username": "admin", "timestamp": "2025-07-28T02:32:10.693278", "ip_address": "localhost"}, "ip_address": "localhost", "user_agent": "Flask App"}, {"id": "262", "timestamp": "2025-07-28T02:32:10.729757", "user_id": "system", "username": "system", "role": "system", "department_id": null, "action": "AUTH_SUCCESS", "entity_type": "user", "entity_id": "01", "details": {"username": "admin", "timestamp": "2025-07-28T02:32:10.717058", "note": "legacy_password_used"}, "ip_address": "localhost", "user_agent": "Flask App"}, {"id": "263", "timestamp": "2025-07-28T02:32:10.755196", "user_id": "01", "username": "admin", "role": "admin", "department_id": null, "action": "LOGIN", "entity_type": "user", "entity_id": "01", "details": {"username": "admin", "role": "admin"}, "ip_address": "localhost", "user_agent": "Flask App"}, {"id": "264", "timestamp": "2025-07-28T02:33:35.404042", "user_id": "system", "username": "system", "role": "system", "department_id": null, "action": "AUTH_ATTEMPT", "entity_type": "user", "entity_id": null, "details": {"username": "admin", "timestamp": "2025-07-28T02:33:35.400380", "ip_address": "localhost"}, "ip_address": "localhost", "user_agent": "Flask App"}, {"id": "265", "timestamp": "2025-07-28T02:33:35.426960", "user_id": "system", "username": "system", "role": "system", "department_id": null, "action": "AUTH_SUCCESS", "entity_type": "user", "entity_id": "01", "details": {"username": "admin", "timestamp": "2025-07-28T02:33:35.415898", "note": "legacy_password_used"}, "ip_address": "localhost", "user_agent": "Flask App"}, {"id": "266", "timestamp": "2025-07-28T02:33:35.444665", "user_id": "01", "username": "admin", "role": "admin", "department_id": null, "action": "LOGIN", "entity_type": "user", "entity_id": "01", "details": {"username": "admin", "role": "admin"}, "ip_address": "localhost", "user_agent": "Flask App"}]