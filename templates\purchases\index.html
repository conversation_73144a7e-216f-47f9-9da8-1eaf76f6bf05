{% extends "base.html" %}

{% block title %}Purchases Management - Hospital Pharmacy Management System{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1><i class="bi bi-cart-plus"></i> Purchases Management</h1>
    <div>
        <a href="{{ url_for('purchases.add') }}" class="btn btn-primary">
            <i class="bi bi-plus-circle"></i> Create Purchase
        </a>
        <div class="btn-group" role="group">
            <button type="button" class="btn btn-outline-secondary dropdown-toggle" data-bs-toggle="dropdown">
                <i class="bi bi-download"></i> Export
            </button>
            <ul class="dropdown-menu">
                <li><a class="dropdown-item" href="#" onclick="exportPurchasesToCSV()">
                    <i class="bi bi-file-earmark-spreadsheet"></i> Export Detailed CSV
                </a></li>
                <li><a class="dropdown-item" href="#" onclick="exportTableToCSV('purchasesTable', 'purchases_summary')">
                    <i class="bi bi-file-earmark-text"></i> Export Summary CSV
                </a></li>
            </ul>
        </div>
    </div>
</div>

<!-- Search and Filter -->
<div class="row mb-3">
    <div class="col-md-2">
        <div class="input-group">
            <span class="input-group-text"><i class="bi bi-search"></i></span>
            <input type="text" class="form-control" id="searchInput" placeholder="Search purchases..." onkeyup="filterTable()">
        </div>
    </div>
    <div class="col-md-2">
        <select class="form-select" id="supplierFilter" onchange="filterTable()">
            <option value="">All Suppliers</option>
            {% for supplier in suppliers %}
            <option value="{{ supplier.id }}">{{ supplier.name }}</option>
            {% endfor %}
        </select>
    </div>
    <div class="col-md-2">
        <select class="form-select" id="statusFilter" onchange="filterTable()">
            <option value="">All Status</option>
            <option value="complete">Complete</option>
            <option value="pending">Pending</option>
            <option value="delivered">Delivered</option>
        </select>
    </div>
    <div class="col-md-2">
        <select class="form-select" id="purchaserFilter" onchange="filterTable()">
            <option value="">All Purchasers</option>
            {% set purchasers = purchases|map(attribute='purchaser_name')|unique|list %}
            {% for purchaser in purchasers %}
            <option value="{{ purchaser }}">{{ purchaser }}</option>
            {% endfor %}
        </select>
    </div>
    <div class="col-md-1">
        <input type="date" class="form-control" id="dateFromFilter" placeholder="From Date" onchange="filterTable()">
    </div>
    <div class="col-md-1">
        <input type="date" class="form-control" id="dateToFilter" placeholder="To Date" onchange="filterTable()">
    </div>
    <div class="col-md-2">
        <button class="btn btn-outline-secondary" onclick="clearFilters()">
            <i class="bi bi-x-circle"></i> Clear
        </button>
    </div>
</div>

<!-- Purchases Table -->
<div class="card">
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-hover" id="purchasesTable">
                <thead>
                    <tr>
                        <th>ID</th>
                        <th>Date</th>
                        <th>Invoice Number</th>
                        <th>Supplier</th>
                        <th>Items</th>
                        <th>Total Quantity</th>
                        <th>Purchaser</th>
                        <th>Status</th>
                        <th>Delivery Date</th>
                        <th>Received By</th>
                        <th>Notes</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    {% for purchase in purchases %}
                    {% set purchase_date = purchase.date or purchase.purchase_date %}
                    <tr data-supplier-id="{{ purchase.supplier_id }}" data-date="{{ purchase_date }}"
                        data-status="{{ purchase.status }}" data-purchaser="{{ purchase.purchaser_name }}">
                        <td><strong>{{ purchase.id }}</strong></td>
                        <td>{{ purchase_date }}</td>
                        <td>{{ purchase.invoice_number }}</td>
                        <td data-export-value="{% if suppliers %}{% set supplier = suppliers|selectattr('id', 'equalto', purchase.supplier_id)|first %}{{ supplier.name if supplier else 'Unknown' }}{% else %}Unknown{% endif %}">
                            {% if suppliers %}
                                {% set supplier = suppliers|selectattr('id', 'equalto', purchase.supplier_id)|first %}
                                {{ supplier.name if supplier else 'Unknown' }}
                            {% else %}
                                Unknown
                            {% endif %}
                        </td>
                        <td data-export-value="{{ purchase.medicines|length }} items">
                            <button class="btn btn-sm btn-outline-info" data-bs-toggle="modal"
                                    data-bs-target="#itemsModal{{ purchase.id }}">
                                <i class="bi bi-list"></i> {{ purchase.medicines|length }} items
                            </button>
                        </td>
                        <td data-export-value="{{ purchase.medicines|sum(attribute='quantity') }}">
                            {% set total_qty = purchase.medicines|sum(attribute='quantity') %}
                            <span class="badge bg-primary">{{ total_qty }}</span>
                        </td>
                        <td>{{ purchase.purchaser_name }}</td>
                        <td>
                            {% if purchase.status == 'complete' %}
                                <span class="badge bg-success">Complete</span>
                            {% elif purchase.status == 'pending' %}
                                <span class="badge bg-warning">Pending</span>
                            {% elif purchase.status == 'delivered' %}
                                <span class="badge bg-info">Delivered</span>
                            {% else %}
                                <span class="badge bg-secondary">{{ purchase.status|title }}</span>
                            {% endif %}
                        </td>
                        <td>
                            {% if purchase.status == 'delivered' and purchase.delivery_date %}
                                {{ purchase.delivery_date }}
                            {% else %}
                                -
                            {% endif %}
                        </td>
                        <td>
                            {% if purchase.status == 'delivered' and purchase.received_by %}
                                {{ purchase.received_by }}
                            {% else %}
                                -
                            {% endif %}
                        </td>
                        <td>
                            {% if purchase.notes %}
                                <span title="{{ purchase.notes }}">{{ purchase.notes[:30] }}{% if purchase.notes|length > 30 %}...{% endif %}</span>
                            {% else %}
                                -
                            {% endif %}
                        </td>
                        <td class="action-buttons">
                            <a href="{{ url_for('purchases.print_purchase', purchase_id=purchase.id) }}"
                               class="btn btn-sm btn-outline-secondary" title="Print" target="_blank">
                                <i class="bi bi-printer"></i>
                            </a>
                            <a href="{{ url_for('purchases.edit', purchase_id=purchase.id) }}" 
                               class="btn btn-sm btn-outline-primary" title="Edit">
                                <i class="bi bi-pencil"></i>
                            </a>
                            <a href="{{ url_for('purchases.delete', purchase_id=purchase.id) }}" 
                               class="btn btn-sm btn-outline-danger" title="Delete"
                               onclick="return confirmDelete('Are you sure you want to delete this purchase?')">
                                <i class="bi bi-trash"></i>
                            </a>
                        </td>
                    </tr>
                    {% else %}
                    <tr>
                        <td colspan="8" class="text-center text-muted">
                            <i class="bi bi-inbox"></i> No purchases found. 
                            <a href="{{ url_for('purchases.add') }}">Create the first purchase</a>.
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>
</div>

<!-- Items Modals for each purchase -->
{% for purchase in purchases %}
<div class="modal fade" id="itemsModal{{ purchase.id }}" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Purchase Items - {{ purchase.invoice_number }}</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="table-responsive">
                    <table class="table table-sm">
                        <thead>
                            <tr>
                                <th>Medicine</th>
                                <th>Form/Dosage</th>
                                <th>Quantity</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for medicine_item in purchase.medicines %}
                            {% set medicine = medicines|selectattr('id', 'equalto', medicine_item.medicine_id)|first %}
                            <tr>
                                <td>{{ medicine.name if medicine else 'Unknown' }}</td>
                                <td>{{ medicine.form_dosage if medicine else 'N/A' }}</td>
                                <td><span class="badge bg-primary">{{ medicine_item.quantity }}</span></td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% if purchase.notes %}
                <div class="mt-3">
                    <strong>Notes:</strong> {{ purchase.notes }}
                </div>
                {% endif %}
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                <button type="button" class="btn btn-primary" onclick="printPurchase('{{ purchase.id }}')">
                    <i class="bi bi-printer"></i> Print
                </button>
            </div>
        </div>
    </div>
</div>
{% endfor %}
{% endblock %}

{% block extra_js %}
<script>
// Print purchase
function printPurchase(purchaseId) {
    // TODO: Implement print functionality
    alert('Print purchase ID: ' + purchaseId);
}

// Filter table based on search and filter inputs
function filterTable() {
    const searchInput = document.getElementById('searchInput').value.toLowerCase();
    const supplierFilter = document.getElementById('supplierFilter').value;
    const statusFilter = document.getElementById('statusFilter').value;
    const purchaserFilter = document.getElementById('purchaserFilter').value;
    const dateFromFilter = document.getElementById('dateFromFilter').value;
    const dateToFilter = document.getElementById('dateToFilter').value;

    const table = document.getElementById('purchasesTable');
    const rows = table.getElementsByTagName('tbody')[0].getElementsByTagName('tr');

    for (let i = 0; i < rows.length; i++) {
        const row = rows[i];
        const supplierID = row.getAttribute('data-supplier-id');
        const status = row.getAttribute('data-status');
        const purchaser = row.getAttribute('data-purchaser');
        const date = row.getAttribute('data-date');
        const rowText = row.textContent.toLowerCase();

        let showRow = true;

        // Filter by search text
        if (searchInput && !rowText.includes(searchInput)) {
            showRow = false;
        }

        // Filter by supplier
        if (supplierFilter && supplierID !== supplierFilter) {
            showRow = false;
        }

        // Filter by status
        if (statusFilter && status !== statusFilter) {
            showRow = false;
        }

        // Filter by purchaser
        if (purchaserFilter && purchaser !== purchaserFilter) {
            showRow = false;
        }

        // Filter by date range
        if (dateFromFilter && date < dateFromFilter) {
            showRow = false;
        }
        if (dateToFilter && date > dateToFilter) {
            showRow = false;
        }

        row.style.display = showRow ? '' : 'none';
    }
}

// Clear filters
function clearFilters() {
    document.getElementById('searchInput').value = '';
    document.getElementById('supplierFilter').value = '';
    document.getElementById('statusFilter').value = '';
    document.getElementById('purchaserFilter').value = '';
    document.getElementById('dateFromFilter').value = '';
    document.getElementById('dateToFilter').value = '';

    // Reset table display
    const rows = document.querySelectorAll('#purchasesTable tbody tr');
    rows.forEach(row => row.style.display = 'table-row');
}

// Filter functionality
document.addEventListener('DOMContentLoaded', function() {
    // Search functionality
    searchTable('searchInput', 'purchasesTable');
    
    // Supplier filter
    document.getElementById('supplierFilter').addEventListener('change', function() {
        const filter = this.value;
        const rows = document.querySelectorAll('#purchasesTable tbody tr[data-supplier-id]');
        
        rows.forEach(row => {
            const supplierId = row.getAttribute('data-supplier-id');
            if (filter === '' || supplierId === filter) {
                row.style.display = 'table-row';
            } else {
                row.style.display = 'none';
            }
        });
    });
    
    // Date filters
    function filterByDate() {
        const fromDate = document.getElementById('dateFromFilter').value;
        const toDate = document.getElementById('dateToFilter').value;
        const rows = document.querySelectorAll('#purchasesTable tbody tr[data-date]');
        
        rows.forEach(row => {
            const rowDate = row.getAttribute('data-date');
            let show = true;
            
            if (fromDate && rowDate < fromDate) show = false;
            if (toDate && rowDate > toDate) show = false;
            
            row.style.display = show ? 'table-row' : 'none';
        });
    }
    
    document.getElementById('dateFromFilter').addEventListener('change', filterByDate);
    document.getElementById('dateToFilter').addEventListener('change', filterByDate);
});
</script>
{% endblock %}
