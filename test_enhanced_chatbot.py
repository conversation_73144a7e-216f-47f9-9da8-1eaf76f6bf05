#!/usr/bin/env python3
"""
Test script for enhanced chatbot functionality
Tests the new comprehensive database analysis capabilities
"""

import sys
import os
sys.path.append('.')

from utils.chatbot_agent import PharmacyAIAgent

def test_comprehensive_queries():
    """Test the new comprehensive analysis queries"""
    print("🧪 Testing Enhanced Chatbot Functionality\n")
    
    # Initialize the AI agent
    agent = PharmacyAIAgent()
    
    # Test queries for comprehensive analysis
    test_queries = [
        "Show me complete overview of all database tables with detailed analysis",
        "Give me complete analysis of medicines table with all medicines, stock levels, and categories",
        "Show me all patients data with consumption patterns and department assignments",
        "Provide comprehensive supplier analysis with performance metrics",
        "List all departments with responsible persons and inventory levels",
        "Analyze inventory across all stores and departments with optimization recommendations",
        "Provide complete financial analysis across purchases and consumption",
        "Show performance metrics for suppliers, departments, and medicine categories"
    ]
    
    print("Testing comprehensive database analysis queries:\n")
    
    for i, query in enumerate(test_queries, 1):
        print(f"🔍 Test {i}: {query}")
        print("-" * 80)
        
        try:
            # Execute the query
            result = agent.process_command(query, user_id="test_user")
            
            if result.get('success'):
                print("✅ SUCCESS")
                response = result.get('response', '')
                # Show first 200 characters of response
                preview = response[:200] + "..." if len(response) > 200 else response
                print(f"Response preview: {preview}")
            else:
                print("❌ FAILED")
                print(f"Error: {result.get('response', 'Unknown error')}")
                
        except Exception as e:
            print(f"❌ EXCEPTION: {str(e)}")
        
        print("\n" + "="*80 + "\n")
    
    print("🎯 Enhanced Chatbot Testing Complete!")

if __name__ == "__main__":
    test_comprehensive_queries()
