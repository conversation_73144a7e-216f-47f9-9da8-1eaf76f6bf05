{% extends "base.html" %}

{% block title %}Transfer Details - Hospital Pharmacy Management System{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1><i class="bi bi-eye"></i> Transfer Details</h1>
    <div>
        <a href="{{ url_for('transfers.index') }}" class="btn btn-outline-secondary">
            <i class="bi bi-arrow-left"></i> Back to Transfers
        </a>
        <button onclick="window.print()" class="btn btn-outline-primary">
            <i class="bi bi-printer"></i> Print
        </button>
    </div>
</div>

<div class="row">
    <div class="col-md-8">
        <!-- Transfer Information -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="card-title mb-0"><i class="bi bi-info-circle"></i> Transfer Information</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <p><strong>Transfer ID:</strong> <code>{{ transfer.id }}</code></p>
                        <p><strong>Date:</strong> {{ transfer.created_at[:19].replace('T', ' ') }}</p>
                        <p><strong>Status:</strong> 
                            <span class="badge {% if transfer.status == 'completed' %}bg-success{% else %}bg-warning{% endif %}">
                                {{ transfer.status|title }}
                            </span>
                        </p>
                    </div>
                    <div class="col-md-6">
                        <p><strong>From Store:</strong> 
                            <span class="badge bg-secondary">{{ transfer.source_store_name }}</span>
                        </p>
                        <p><strong>To Store:</strong> 
                            <span class="badge bg-primary">{{ transfer.destination_store_name }}</span>
                        </p>
                        <p><strong>Total Items:</strong>
                            <span class="badge bg-info">{{ transfer.medicines|length }}</span>
                        </p>
                    </div>
                </div>

                <div class="row mt-3">
                    <div class="col-md-6">
                        {% if transfer.requested_by %}
                        <p><strong>Requested By:</strong>
                            <span class="text-primary">{{ transfer.requested_by }}</span>
                        </p>
                        {% endif %}
                    </div>
                    <div class="col-md-6">
                        {% if transfer.approved_by %}
                        <p><strong>Approved By:</strong>
                            <span class="text-success">{{ transfer.approved_by }}</span>
                        </p>
                        {% endif %}
                    </div>
                </div>

                {% if transfer.notes %}
                <div class="mt-3">
                    <strong>Notes:</strong>
                    <div class="alert alert-light mt-2">{{ transfer.notes }}</div>
                </div>
                {% endif %}
            </div>
        </div>
        
        <!-- Transfer Items -->
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0"><i class="bi bi-list-ul"></i> Transferred Items</h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>#</th>
                                <th>Medicine Name</th>
                                <th>Quantity Transferred</th>
                                <th>Status</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for item in transfer.medicines %}
                            <tr>
                                <td>{{ loop.index }}</td>
                                <td>
                                    <strong>{{ item.medicine_name }}</strong>
                                </td>
                                <td>
                                    <span class="badge bg-primary">{{ item.quantity }} units</span>
                                </td>
                                <td>
                                    <span class="badge bg-success">Transferred</span>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                        <tfoot>
                            <tr class="table-light">
                                <th colspan="2">Total Items</th>
                                <th>{{ transfer.medicines|map(attribute='quantity')|sum }} units</th>
                                <th>{{ transfer.medicines|length }} medicines</th>
                            </tr>
                        </tfoot>
                    </table>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-4">
        <!-- Transfer Summary -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="card-title mb-0"><i class="bi bi-graph-up"></i> Transfer Summary</h5>
            </div>
            <div class="card-body">
                <div class="text-center mb-3">
                    <div class="display-6 text-primary">{{ transfer.medicines|length }}</div>
                    <p class="text-muted mb-0">Medicines Transferred</p>
                </div>
                
                <div class="text-center mb-3">
                    <div class="display-6 text-success">{{ transfer.medicines|map(attribute='quantity')|sum }}</div>
                    <p class="text-muted mb-0">Total Units</p>
                </div>
                
                <div class="text-center">
                    <div class="h5 text-info">{{ transfer.created_at[:10] }}</div>
                    <p class="text-muted mb-0">Transfer Date</p>
                </div>
            </div>
        </div>
        
        <!-- Transfer Flow -->
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0"><i class="bi bi-arrow-left-right"></i> Transfer Flow</h5>
            </div>
            <div class="card-body">
                <div class="d-flex flex-column align-items-center">
                    <!-- Source Store -->
                    <div class="text-center mb-3">
                        <div class="bg-secondary text-white rounded-circle d-flex align-items-center justify-content-center mx-auto mb-2" 
                             style="width: 60px; height: 60px;">
                            <i class="bi bi-box-seam"></i>
                        </div>
                        <strong>{{ transfer.source_store_name }}</strong>
                        <br>
                        <small class="text-muted">Source Store</small>
                    </div>
                    
                    <!-- Arrow -->
                    <div class="text-center mb-3">
                        <i class="bi bi-arrow-down display-6 text-primary"></i>
                        <br>
                        <small class="text-muted">{{ transfer.medicines|length }} items transferred</small>
                    </div>
                    
                    <!-- Destination Store -->
                    <div class="text-center">
                        <div class="bg-primary text-white rounded-circle d-flex align-items-center justify-content-center mx-auto mb-2" 
                             style="width: 60px; height: 60px;">
                            <i class="bi bi-box-seam"></i>
                        </div>
                        <strong>{{ transfer.destination_store_name }}</strong>
                        <br>
                        <small class="text-muted">Destination Store</small>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Print Styles -->
<style>
@media print {
    .btn, .card-header {
        display: none !important;
    }
    
    .card {
        border: none !important;
        box-shadow: none !important;
    }
    
    .card-body {
        padding: 0 !important;
    }
    
    .col-md-4 {
        display: none !important;
    }
    
    .col-md-8 {
        width: 100% !important;
    }
    
    h1 {
        font-size: 1.5rem !important;
        margin-bottom: 1rem !important;
    }
    
    .table {
        font-size: 0.9rem !important;
    }
    
    .badge {
        background-color: #6c757d !important;
        color: white !important;
    }
}
</style>
{% endblock %}

{% block extra_js %}
<script>
// Add any additional JavaScript for the view page here
document.addEventListener('DOMContentLoaded', function() {
    // You can add interactive features here if needed
});
</script>
{% endblock %}
