#!/usr/bin/env python3
"""
Simulate frontend behavior to identify the exact issue
"""

import requests
import json
import time

def simulate_frontend_workflow():
    """Simulate the exact frontend workflow"""
    base_url = "http://127.0.0.1:5000"
    
    print("🌐 Simulating Frontend Workflow\n")
    
    # Create a session to maintain cookies (like a browser)
    session = requests.Session()
    
    # Step 1: Access the main page (like opening the website)
    print("1. Accessing main page...")
    main_response = session.get(f"{base_url}/")
    print(f"   Status: {main_response.status_code}")
    print(f"   Final URL: {main_response.url}")
    
    # Step 2: Login (like filling the login form)
    print("\n2. Logging in...")
    login_data = {
        'username': 'admin',
        'password': '@Xx123456789xX@'
    }
    
    login_response = session.post(f"{base_url}/auth/login", data=login_data, allow_redirects=True)
    print(f"   Status: {login_response.status_code}")
    print(f"   Final URL: {login_response.url}")
    
    # Step 3: Access chatbot page (like clicking on chatbot menu)
    print("\n3. Accessing chatbot page...")
    chatbot_response = session.get(f"{base_url}/chatbot/")
    print(f"   Status: {chatbot_response.status_code}")
    
    if chatbot_response.status_code == 200:
        print("   ✅ Chatbot page accessible")
    else:
        print(f"   ❌ Chatbot page failed: {chatbot_response.status_code}")
        return
    
    # Step 4: Test configuration endpoints (like opening settings modal)
    print("\n4. Testing configuration GET (like opening settings)...")
    config_get_response = session.get(f"{base_url}/chatbot/config")
    print(f"   Status: {config_get_response.status_code}")
    
    if config_get_response.status_code == 200:
        try:
            config_data = config_get_response.json()
            print("   ✅ Configuration GET successful")
            print(f"   Provider: {config_data.get('provider')}")
            print(f"   Enabled: {config_data.get('enabled')}")
        except:
            print("   ❌ Response not JSON")
    else:
        print(f"   ❌ Configuration GET failed: {config_get_response.status_code}")
    
    # Step 5: Test configuration POST (like saving settings)
    print("\n5. Testing configuration POST (like saving settings)...")
    config_data = {
        'provider': 'openrouter',
        'openrouter_api_key': '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',
        'model': 'deepseek/deepseek-r1:nitro',
        'max_tokens': '1000',
        'temperature': '0.7',
        'enabled': 'on'
    }
    
    # Simulate form submission (like the frontend does)
    config_post_response = session.post(f"{base_url}/chatbot/config", data=config_data)
    print(f"   Status: {config_post_response.status_code}")
    
    if config_post_response.status_code == 200:
        try:
            response_data = config_post_response.json()
            print(f"   ✅ Configuration POST successful")
            print(f"   Success: {response_data.get('success')}")
            print(f"   Message: {response_data.get('message')}")
        except:
            print("   ❌ Response not JSON")
    else:
        print(f"   ❌ Configuration POST failed: {config_post_response.status_code}")
    
    # Step 6: Test chatbot query (like sending a message)
    print("\n6. Testing chatbot query (like sending a message)...")
    query_data = {
        'query': 'Hello, can you help me with pharmacy data?'
    }
    
    # Simulate AJAX request (like the frontend does)
    query_response = session.post(
        f"{base_url}/chatbot/query", 
        json=query_data,
        headers={'Content-Type': 'application/json'}
    )
    print(f"   Status: {query_response.status_code}")
    
    if query_response.status_code == 200:
        try:
            response_data = query_response.json()
            print("   ✅ Query successful")
            print(f"   Status: {response_data.get('status')}")
            print(f"   Agent Handled: {response_data.get('agent_handled')}")
            print(f"   LLM Handled: {response_data.get('llm_handled')}")
            print(f"   Enhanced Fallback: {response_data.get('enhanced_fallback')}")
            response_preview = response_data.get('response', '')[:150]
            print(f"   Response: {response_preview}...")
        except Exception as e:
            print(f"   ❌ Response parsing failed: {e}")
            print(f"   Raw response: {query_response.text[:200]}...")
    else:
        print(f"   ❌ Query failed: {query_response.status_code}")
        print(f"   Response: {query_response.text[:200]}...")
    
    # Step 7: Test a comprehensive analysis query
    print("\n7. Testing comprehensive analysis query...")
    analysis_query = {
        'query': 'Show me complete overview of all database tables with detailed analysis'
    }
    
    analysis_response = session.post(
        f"{base_url}/chatbot/query", 
        json=analysis_query,
        headers={'Content-Type': 'application/json'}
    )
    print(f"   Status: {analysis_response.status_code}")
    
    if analysis_response.status_code == 200:
        try:
            response_data = analysis_response.json()
            print("   ✅ Analysis query successful")
            print(f"   Agent Handled: {response_data.get('agent_handled')}")
            print(f"   LLM Handled: {response_data.get('llm_handled')}")
            response_preview = response_data.get('response', '')[:150]
            print(f"   Response: {response_preview}...")
        except Exception as e:
            print(f"   ❌ Response parsing failed: {e}")
    else:
        print(f"   ❌ Analysis query failed: {analysis_response.status_code}")
    
    print("\n🎯 Frontend Simulation Complete!")
    
    # Summary
    print("\n📋 SUMMARY:")
    print("   - Main page access: ✅" if main_response.status_code == 200 else "   - Main page access: ❌")
    print("   - Login: ✅" if 'dashboard' in login_response.url else "   - Login: ❌")
    print("   - Chatbot page: ✅" if chatbot_response.status_code == 200 else "   - Chatbot page: ❌")
    print("   - Config GET: ✅" if config_get_response.status_code == 200 else "   - Config GET: ❌")
    print("   - Config POST: ✅" if config_post_response.status_code == 200 else "   - Config POST: ❌")
    print("   - Query: ✅" if query_response.status_code == 200 else "   - Query: ❌")
    print("   - Analysis: ✅" if analysis_response.status_code == 200 else "   - Analysis: ❌")

if __name__ == "__main__":
    simulate_frontend_workflow()
