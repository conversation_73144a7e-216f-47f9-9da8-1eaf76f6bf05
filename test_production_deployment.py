#!/usr/bin/env python3
"""
Production Deployment Investigation Script
Tests the live production website at https://alorfmedz.com to verify deployment status
and compare with locally implemented features.
"""

import asyncio
import json
import time
from datetime import datetime
from playwright.async_api import async_playwright
import os

class ProductionDeploymentTester:
    def __init__(self):
        self.production_url = "https://alorfmedz.com"
        self.test_results = {
            "timestamp": datetime.now().isoformat(),
            "production_url": self.production_url,
            "tests": {},
            "missing_features": [],
            "deployment_issues": [],
            "screenshots": []
        }
        
    async def run_comprehensive_test(self):
        """Run comprehensive production deployment testing"""
        async with async_playwright() as p:
            # Launch browser
            browser = await p.chromium.launch(headless=False, slow_mo=1000)
            context = await browser.new_context(
                viewport={'width': 1920, 'height': 1080},
                user_agent='Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
            )
            page = await context.new_page()
            
            try:
                print(f"🔍 Starting Production Deployment Investigation...")
                print(f"🌐 Testing: {self.production_url}")
                
                # Test 1: Basic Site Access
                await self.test_site_access(page)
                
                # Test 2: Site Structure Analysis (without auth)
                await self.test_site_structure(page)

                # Test 3: Authentication
                auth_success = await self.test_authentication(page)

                if auth_success:
                    # Test 4: Purchase Management Features
                    await self.test_purchase_management_features(page)

                    # Test 5: Reports Enhancements
                    await self.test_reports_enhancements(page)

                    # Test 6: Database and Backend Features
                    await self.test_backend_features(page)
                else:
                    print("⚠️ Skipping authenticated tests due to login failure")
                    self.test_results["deployment_issues"].append("Authentication failed - cannot test internal features")
                
                # Generate comprehensive report
                await self.generate_deployment_report()
                
            except Exception as e:
                print(f"❌ Critical error during testing: {str(e)}")
                self.test_results["critical_error"] = str(e)
                
            finally:
                await browser.close()
    
    async def test_site_access(self, page):
        """Test basic site access and loading"""
        print("\n📡 Testing Site Access...")
        
        try:
            # Navigate to production site
            response = await page.goto(self.production_url, wait_until='networkidle', timeout=30000)
            
            if response and response.status == 200:
                print("✅ Site accessible")
                self.test_results["tests"]["site_access"] = {
                    "status": "PASS",
                    "response_code": response.status,
                    "url": response.url
                }
            else:
                print(f"❌ Site access failed: {response.status if response else 'No response'}")
                self.test_results["tests"]["site_access"] = {
                    "status": "FAIL",
                    "error": f"HTTP {response.status if response else 'No response'}"
                }
                
            # Take screenshot of homepage
            await page.screenshot(path="production_homepage.png", full_page=True)
            self.test_results["screenshots"].append("production_homepage.png")
            
            # Check page title
            title = await page.title()
            print(f"📄 Page Title: {title}")
            self.test_results["tests"]["site_access"]["title"] = title
            
        except Exception as e:
            print(f"❌ Site access error: {str(e)}")
            self.test_results["tests"]["site_access"] = {
                "status": "ERROR",
                "error": str(e)
            }

    async def test_site_structure(self, page):
        """Test site structure and available endpoints without authentication"""
        print("\n🏗️ Testing Site Structure...")

        try:
            # Get page source to analyze structure
            page_content = await page.content()

            # Check for key application indicators
            structure_indicators = {
                "flask_app": "Flask" in page_content or "Werkzeug" in page_content,
                "bootstrap": "bootstrap" in page_content.lower(),
                "jquery": "jquery" in page_content.lower(),
                "login_form": "login" in page_content.lower() and "password" in page_content.lower(),
                "hospital_branding": "alorf" in page_content.lower() or "hospital" in page_content.lower(),
                "pharmacy_system": "pharmacy" in page_content.lower() or "medicine" in page_content.lower()
            }

            # Test direct endpoint access (should redirect to login)
            endpoints_to_test = [
                "/dashboard/",
                "/purchases/",
                "/reports/",
                "/medicines/",
                "/patients/",
                "/suppliers/"
            ]

            endpoint_results = {}
            for endpoint in endpoints_to_test:
                try:
                    response = await page.goto(f"{self.production_url}{endpoint}", wait_until='networkidle', timeout=10000)
                    endpoint_results[endpoint] = {
                        "status_code": response.status if response else "No response",
                        "final_url": page.url,
                        "redirected_to_login": "login" in page.url.lower()
                    }
                    print(f"📍 {endpoint}: {response.status if response else 'No response'} -> {page.url}")
                except Exception as e:
                    endpoint_results[endpoint] = {"error": str(e)}
                    print(f"📍 {endpoint}: Error - {str(e)}")

            self.test_results["tests"]["site_structure"] = {
                "status": "PASS",
                "structure_indicators": structure_indicators,
                "endpoint_results": endpoint_results
            }

            # Analyze deployment status based on structure
            if not any(structure_indicators.values()):
                self.test_results["deployment_issues"].append("Site structure indicates possible deployment issues")
                print("⚠️ Site structure analysis suggests deployment problems")
            else:
                print("✅ Site structure appears normal")

        except Exception as e:
            print(f"❌ Site structure test error: {str(e)}")
            self.test_results["tests"]["site_structure"] = {
                "status": "ERROR",
                "error": str(e)
            }
    
    async def test_authentication(self, page):
        """Test authentication and login functionality"""
        print("\n🔐 Testing Authentication...")

        try:
            # Look for login elements
            login_form = await page.query_selector('form')
            if login_form:
                print("✅ Login form found")

                # Try multiple credential combinations
                credentials = [
                    ('admin', 'admin123'),
                    ('admin', 'admin'),
                    ('waleed', 'waleed123'),
                    ('waleed', 'waleed')
                ]

                for username, password in credentials:
                    print(f"🔑 Trying credentials: {username}/{password}")

                    # Clear and fill username
                    username_field = await page.query_selector('input[name="username"], input[type="text"], input[placeholder*="username"], input[placeholder*="Username"]')
                    password_field = await page.query_selector('input[name="password"], input[type="password"], input[placeholder*="password"], input[placeholder*="Password"]')

                    if username_field and password_field:
                        # Clear fields using different method
                        await username_field.click()
                        await page.keyboard.press('Control+a')
                        await page.keyboard.press('Delete')
                        await username_field.type(username)

                        await password_field.click()
                        await page.keyboard.press('Control+a')
                        await page.keyboard.press('Delete')
                        await password_field.type(password)

                        # Submit form
                        submit_button = await page.query_selector('button[type="submit"], input[type="submit"], button:has-text("Login"), button:has-text("Sign In")')
                        if submit_button:
                            await submit_button.click()
                            await page.wait_for_timeout(5000)

                            # Check if login was successful
                            current_url = page.url
                            page_content = await page.content()

                            if ('dashboard' in current_url.lower() or
                                'admin' in current_url.lower() or
                                'medicines' in page_content.lower() or
                                'purchases' in page_content.lower() or
                                'logout' in page_content.lower()):
                                print(f"✅ Authentication successful with {username}/{password}")
                                self.test_results["tests"]["authentication"] = {
                                    "status": "PASS",
                                    "credentials": f"{username}/{password}",
                                    "redirect_url": current_url
                                }
                                return True
                            else:
                                print(f"❌ Authentication failed with {username}/{password}")
                                # Go back to login page for next attempt
                                await page.goto(self.production_url, wait_until='networkidle')
                                await page.wait_for_timeout(2000)

                # If all credentials failed
                print("❌ All authentication attempts failed")
                self.test_results["tests"]["authentication"] = {
                    "status": "FAIL",
                    "error": "All credential combinations failed"
                }
                return False

            else:
                print("❌ No login form found")
                self.test_results["tests"]["authentication"] = {
                    "status": "FAIL",
                    "error": "No login form found"
                }
                return False

        except Exception as e:
            print(f"❌ Authentication error: {str(e)}")
            self.test_results["tests"]["authentication"] = {
                "status": "ERROR",
                "error": str(e)
            }
            return False
    
    async def test_purchase_management_features(self, page):
        """Test purchase management enhancements"""
        print("\n🛒 Testing Purchase Management Features...")
        
        try:
            # Navigate to purchases page
            purchases_link = await page.query_selector('a[href*="purchase"], a:has-text("Purchase")')
            if purchases_link:
                await purchases_link.click()
                await page.wait_for_timeout(3000)
                
                # Take screenshot of purchases page
                await page.screenshot(path="production_purchases.png", full_page=True)
                self.test_results["screenshots"].append("production_purchases.png")
                
                # Check for enhanced columns
                enhanced_features = {
                    "status_column": await page.query_selector('th:has-text("Status"), td:has-text("Complete"), td:has-text("Pending"), td:has-text("Delivered")'),
                    "delivery_date_column": await page.query_selector('th:has-text("Delivery Date")'),
                    "received_by_column": await page.query_selector('th:has-text("Received By")'),
                    "notes_column": await page.query_selector('th:has-text("Notes")'),
                    "status_filter": await page.query_selector('select option:has-text("Complete"), select option:has-text("Pending"), select option:has-text("Delivered")'),
                    "purchaser_filter": await page.query_selector('select option:has-text("Waleed"), select option:has-text("Marlen"), select option:has-text("Charlotte")')
                }
                
                missing_features = []
                for feature, element in enhanced_features.items():
                    if element:
                        print(f"✅ {feature.replace('_', ' ').title()} found")
                    else:
                        print(f"❌ {feature.replace('_', ' ').title()} MISSING")
                        missing_features.append(feature)
                        
                self.test_results["tests"]["purchase_management"] = {
                    "status": "PARTIAL" if missing_features else "PASS",
                    "missing_features": missing_features,
                    "found_features": [f for f in enhanced_features.keys() if f not in missing_features]
                }
                
                if missing_features:
                    self.test_results["missing_features"].extend([f"Purchase Management: {f}" for f in missing_features])
                    
            else:
                print("❌ Purchases page not accessible")
                self.test_results["tests"]["purchase_management"] = {
                    "status": "FAIL",
                    "error": "Purchases page not found"
                }
                
        except Exception as e:
            print(f"❌ Purchase management test error: {str(e)}")
            self.test_results["tests"]["purchase_management"] = {
                "status": "ERROR",
                "error": str(e)
            }
    
    async def test_reports_enhancements(self, page):
        """Test reports enhancements"""
        print("\n📊 Testing Reports Enhancements...")
        
        try:
            # Navigate to reports page
            reports_link = await page.query_selector('a[href*="report"], a:has-text("Report")')
            if reports_link:
                await reports_link.click()
                await page.wait_for_timeout(3000)
                
                # Take screenshot of reports page
                await page.screenshot(path="production_reports.png", full_page=True)
                self.test_results["screenshots"].append("production_reports.png")
                
                # Test specific report enhancements
                report_features = {
                    "row_numbering": await page.query_selector('th:has-text("#"), th:has-text("Row"), td:has-text("1"), td:has-text("2")'),
                    "consumption_totals": await page.query_selector('tr:has-text("Total"), td:has-text("Total Quantity")'),
                    "enhanced_filtering": await page.query_selector('select, input[type="date"]'),
                    "medicine_names": await page.query_selector('td:has-text("Paracetamol"), td:has-text("Aspirin"), td:has-text("Ibuprofen")')
                }
                
                missing_report_features = []
                for feature, element in report_features.items():
                    if element:
                        print(f"✅ Reports {feature.replace('_', ' ').title()} found")
                    else:
                        print(f"❌ Reports {feature.replace('_', ' ').title()} MISSING")
                        missing_report_features.append(feature)
                        
                self.test_results["tests"]["reports_enhancements"] = {
                    "status": "PARTIAL" if missing_report_features else "PASS",
                    "missing_features": missing_report_features,
                    "found_features": [f for f in report_features.keys() if f not in missing_report_features]
                }
                
                if missing_report_features:
                    self.test_results["missing_features"].extend([f"Reports: {f}" for f in missing_report_features])
                    
            else:
                print("❌ Reports page not accessible")
                self.test_results["tests"]["reports_enhancements"] = {
                    "status": "FAIL",
                    "error": "Reports page not found"
                }
                
        except Exception as e:
            print(f"❌ Reports test error: {str(e)}")
            self.test_results["tests"]["reports_enhancements"] = {
                "status": "ERROR",
                "error": str(e)
            }
    
    async def test_backend_features(self, page):
        """Test backend and database features"""
        print("\n🗄️ Testing Backend Features...")
        
        try:
            # Check for any JavaScript errors in console
            console_errors = []
            page.on('console', lambda msg: console_errors.append(msg.text) if msg.type == 'error' else None)
            
            # Check for network errors
            network_errors = []
            page.on('response', lambda response: network_errors.append(f"{response.status}: {response.url}") if response.status >= 400 else None)
            
            # Wait and collect errors
            await page.wait_for_timeout(5000)
            
            self.test_results["tests"]["backend_features"] = {
                "console_errors": console_errors,
                "network_errors": network_errors,
                "status": "PASS" if not console_errors and not network_errors else "ISSUES_FOUND"
            }
            
            if console_errors:
                print(f"⚠️ Console errors found: {len(console_errors)}")
                for error in console_errors[:5]:  # Show first 5 errors
                    print(f"   - {error}")
                    
            if network_errors:
                print(f"⚠️ Network errors found: {len(network_errors)}")
                for error in network_errors[:5]:  # Show first 5 errors
                    print(f"   - {error}")
                    
        except Exception as e:
            print(f"❌ Backend test error: {str(e)}")
            self.test_results["tests"]["backend_features"] = {
                "status": "ERROR",
                "error": str(e)
            }
    
    async def generate_deployment_report(self):
        """Generate comprehensive deployment analysis report"""
        print("\n📋 Generating Deployment Analysis Report...")
        
        # Save test results to JSON
        with open("production_deployment_analysis.json", "w") as f:
            json.dump(self.test_results, f, indent=2)
            
        print("✅ Test results saved to production_deployment_analysis.json")
        print("✅ Screenshots saved for visual comparison")
        
        # Print summary
        print(f"\n📊 DEPLOYMENT ANALYSIS SUMMARY:")
        print(f"🌐 Production URL: {self.production_url}")
        print(f"⏰ Test Time: {self.test_results['timestamp']}")
        print(f"📸 Screenshots: {len(self.test_results['screenshots'])} captured")
        print(f"❌ Missing Features: {len(self.test_results['missing_features'])}")
        
        if self.test_results['missing_features']:
            print("\n🚨 MISSING FEATURES DETECTED:")
            for feature in self.test_results['missing_features']:
                print(f"   - {feature}")
        else:
            print("\n✅ All features appear to be deployed correctly!")

async def main():
    """Main execution function"""
    tester = ProductionDeploymentTester()
    await tester.run_comprehensive_test()

if __name__ == "__main__":
    asyncio.run(main())
