# Complete Multi-Container Deployment & Network Troubleshooting Guide

**Date:** July 28, 2025  
**Server:** srv889400 (************)  
**Issue:** Complete system failure - Multiple Docker containers and auto-deployment issues  
**Status:** ✅ **FULLY RESOLVED**  
**Total Resolution Time:** ~4 hours systematic troubleshooting

---

## 📋 **EXECUTIVE SUMMARY**

This document chronicles a complex multi-issue troubleshooting session involving:
1. **Auto-deployment pipeline failure** for pharmacy management system
2. **Complete multi-container system failure** affecting 5+ production websites
3. **Docker network isolation** preventing nginx reverse proxy communication
4. **File permission issues** causing chatbot system failures

**All issues were systematically resolved through methodical diagnosis and network architecture fixes.**

---

## 🏗️ **SYSTEM ARCHITECTURE**

### **Multi-Domain Production Environment:**
- **alorfmedz.com** - Hospital Pharmacy Management System (Python/Flask)
- **alorfbiomed.com** - Hospital Equipment System (Python/Flask) 
- **sarafly.online** - Sarafly Management System (Web App)
- **abusoliman.online** - POS System (Web App)
- **n8n-waleed.shop** - N8N Workflow Automation (Node.js)
- **FileBrowser** - File Management System (Port 8090)

### **Docker Infrastructure:**
- **7 Total Containers** running on isolated Docker networks
- **Nginx Reverse Proxy** handling SSL termination and routing
- **Let's Encrypt SSL** certificates for all domains
- **Docker Compose** orchestration for each application

---

## 🚨 **PROBLEM 1: AUTO-DEPLOYMENT PIPELINE FAILURE**

### **Initial Symptoms:**
- ✅ GitHub Actions reported "successful" deployment
- ✅ Code successfully committed to repository (commit `e750266`)
- ❌ **ZERO enhancements** visible on production website
- ❌ Missing 6 major pharmacy management features

### **Root Cause Discovery:**
**Git merge conflicts** were preventing deployment updates:

```bash
err: error: Your local changes to the following files would be overwritten by merge:
err: 	data/chat_history.json
err: 	data/chat_sessions.json
err: Please commit your changes or stash them before you merge.
err: Aborting
```

### **The Deployment Deception:**
1. ✅ GitHub Actions triggered deployment script
2. ❌ **Git pull FAILED** silently due to file conflicts  
3. ✅ Docker containers restarted with **OLD CODE**
4. ✅ Health check passed (with outdated version)
5. ✅ GitHub Actions marked as "successful"

### **Resolution Process:**

#### **Step 1: Git Conflict Resolution**
```bash
cd /opt/alorfmedz

# Backup problematic files
mkdir -p temp_backup
cp data/chat_history.json temp_backup/ 2>/dev/null
cp data/chat_sessions.json temp_backup/ 2>/dev/null

# Add problematic files to .gitignore
echo "data/chat_history.json" >> .gitignore
echo "data/chat_sessions.json" >> .gitignore
echo "CHATBOT_PRODUCTION_ISSUE_SUMMARY.md" >> .gitignore
echo "PRODUCTION_CHATBOT_DEBUG_REPORT.md" >> .gitignore
echo "test_chatbot_imports.py" >> .gitignore
echo "backups/" >> .gitignore

# Remove untracked conflicting files
rm -f CHATBOT_PRODUCTION_ISSUE_SUMMARY.md
rm -f PRODUCTION_CHATBOT_DEBUG_REPORT.md  
rm -f test_chatbot_imports.py
```

#### **Step 2: Force Update to Latest Code**
```bash
# Commit .gitignore changes to prevent future conflicts
git add .gitignore
git commit -m "Add .gitignore to prevent deployment conflicts"

# Stash remaining local changes
git stash push -m "Backup remaining local changes"

# Force pull latest code with enhancements
git fetch origin
git reset --hard origin/main

# Verify correct commit deployed
git log --oneline -1
# Result: e750266 feat: Comprehensive Hospital Pharmacy Management System Enhancements
```

#### **Step 3: Deploy Updated Code**
```bash
# Rebuild containers with latest code
docker-compose down
docker-compose up -d --build

# Verify deployment success
docker-compose ps
```

**✅ Result:** All pharmacy management enhancements successfully deployed

---

## 🚨 **PROBLEM 2: COMPLETE MULTI-CONTAINER SYSTEM FAILURE**

### **Symptoms:**
- **ALL production websites** returning errors
- Multiple containers being forcibly killed (exit status 137)
- Docker logs showing repeated container restart failures
- 504 Gateway Timeout errors across all domains

### **Diagnosis Process:**

#### **Step 1: Container Status Assessment**
```bash
docker ps -a
docker stats --no-stream
systemctl status docker
```

**Findings:**
- ✅ Docker daemon running normally
- ❌ Containers being killed repeatedly (SIGKILL - exit status 137)
- ❌ "restart canceled" errors preventing auto-restart

#### **Step 2: Resource Analysis**
```bash
free -h
df -h
docker system df
```

**Critical Discovery:** Docker was consuming excessive disk space causing container kills

#### **Step 3: Emergency Docker Cleanup**
```bash
# Clean up unused Docker images
docker image prune -f

# Remove all unused containers, networks, images, and build cache  
docker system prune -a -f
```

**✅ Result:** Freed 1.622GB of disk space, containers stabilized

---

## 🚨 **PROBLEM 3: DOCKER NETWORK ISOLATION ISSUE**

### **Symptoms After Container Recovery:**
- ✅ All containers running and healthy
- ✅ Individual container logs showing normal operation
- ❌ **ALL websites returning 504 Gateway Timeout**
- ✅ Direct container access working (curl localhost:port)

### **Root Cause Discovery:**
**Docker network isolation** preventing nginx reverse proxy from reaching backend containers.

#### **Network Architecture Analysis:**
```bash
# Check container networks
docker network ls
docker inspect alorfmedz-nginx | grep -A 10 "Networks"
docker inspect hospital-equipment-system | grep -A 10 "Networks"
docker inspect sarafly-system | grep -A 10 "Networks"
docker inspect n8n | grep -A 10 "Networks"
```

**Critical Finding:** Each application was isolated on separate Docker networks:
- **alorfmedz-nginx**: `alorfmedz_alorfmedz-network`
- **hospital-equipment-system**: `hospital_hospital_network` 
- **sarafly-system**: `sarafly-network`
- **abusoliman-pos**: `abusoliman-network`
- **n8n**: `root_default`

#### **Network Communication Testing:**
```bash
# Test nginx → backend connectivity
docker exec alorfmedz-nginx curl -I http://**********:5000  # Hospital system
docker exec alorfmedz-nginx curl -I http://**********:80   # Sarafly system  
docker exec alorfmedz-nginx curl -I http://**********:80   # Abu Soliman POS
docker exec alorfmedz-nginx curl -I http://**********:5678 # N8N system
```

**Result:** All connections **timed out** - nginx isolated from backend networks

### **Resolution: Cross-Network Bridge Configuration**

#### **Step 1: Connect Nginx to All Backend Networks**
```bash
# Connect nginx container to all other Docker networks
docker network connect hospital_hospital_network alorfmedz-nginx
docker network connect sarafly-network alorfmedz-nginx  
docker network connect abusoliman-network alorfmedz-nginx
docker network connect root_default alorfmedz-nginx
```

#### **Step 2: Restart and Verify**
```bash
# Restart nginx to apply network changes
docker restart alorfmedz-nginx

# Test connectivity again
docker exec alorfmedz-nginx curl -I http://**********:5000
docker exec alorfmedz-nginx curl -I http://**********:80
docker exec alorfmedz-nginx curl -I http://**********:80
docker exec alorfmedz-nginx curl -I http://**********:5678
```

**✅ Result:** All network connections successful

#### **Step 3: Final Website Testing**
```bash
curl -I https://alorfbiomed.com     # HTTP 200 ✅
curl -I https://sarafly.online      # HTTP 200 ✅  
curl -I https://abusoliman.online   # HTTP 200 ✅
curl -I https://n8n-waleed.shop     # HTTP 200 ✅
curl -I https://alorfmedz.com       # HTTP 302 ✅ (redirect to login)
```

---

## 🚨 **PROBLEM 4: CHATBOT PERMISSION ISSUES**

### **Issue:**
After Docker rebuild, chatbot returned 500 Internal Server Error due to file permission conflicts.

### **Root Cause:**
Docker container runs as `appuser` (UID 1000) but chatbot data files owned by `root` (UID 0).

### **Resolution:**
```bash
# Fix file ownership to match container user
chown 1000:1000 data/chat_history.json
chown 1000:1000 data/chat_sessions.json  
chown 1000:1000 data/chatbot_config.json

# Restart containers
docker-compose restart
```

### **Permanent Fix - Dockerfile Update:**
```dockerfile
# Add to Dockerfile after COPY command
RUN chown -R appuser:appuser /app/data/*.json 2>/dev/null || true
```

```bash
# Commit the permanent fix
git add Dockerfile
git commit -m "Fix chatbot file permissions in Dockerfile for auto-deployment"
git push origin main
```

---

## 📊 **FILEBROWSER SYSTEM STATUS**

### **Current Configuration:**
- **Container:** `filebrowser` (healthy)
- **Port:** 8090 (0.0.0.0:8090->80/tcp)
- **Access:** http://************:8090
- **Location:** `/opt/filebrowser/`

### **Volume Mappings:**
- `/opt` → `/srv/applications` (All Docker projects)
- `/root` → `/srv/root-home` (Root directory)
- `/var/log` → `/srv/logs` (System logs)
- `/home` → `/srv/home` (User directories)

### **Docker Compose Configuration:**
```yaml
version: '3.8'
services:
  filebrowser:
    image: filebrowser/filebrowser:v2-s6
    container_name: filebrowser
    restart: unless-stopped
    environment:
      - PUID=0
      - PGID=0
      - TZ=Asia/Kuwait
    volumes:
      - /opt:/srv/applications
      - /root:/srv/root-home
      - /var/log:/srv/logs
      - /home:/srv/home
      - ./filebrowser.db:/database/filebrowser.db
    ports:
      - "8090:80"
    networks:
      - filebrowser-network
```

### **Access Issue:**
FileBrowser returns **404 Not Found** on default route. This is normal - requires login at specific path.

**Correct Access:** http://************:8090/files (after authentication)

---

## 🛠️ **NGINX REVERSE PROXY CONFIGURATION**

### **Complete nginx.conf:**
```nginx
events {
    worker_connections 1024;
}

http {
    include /etc/nginx/mime.types;
    default_type application/octet-stream;
    
    # Upstream for alorfmedz app
    upstream alorfmedz_app {
        server alorfmedz-app:5001;
    }
    
    # ALORF Medical Platform (alorfmedz.com)
    server {
        listen 443 ssl http2;
        server_name alorfmedz.com www.alorfmedz.com;
        
        ssl_certificate /etc/letsencrypt/live/alorfmedz.com/fullchain.pem;
        ssl_certificate_key /etc/letsencrypt/live/alorfmedz.com/privkey.pem;
        
        location / {
            proxy_pass http://alorfmedz_app;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }
    }
    
    # ALORF BIOMED Hospital System (alorfbiomed.com)
    server {
        listen 443 ssl http2;
        server_name alorfbiomed.com;
        
        ssl_certificate /etc/letsencrypt/live/alorfbiomed.com/fullchain.pem;
        ssl_certificate_key /etc/letsencrypt/live/alorfbiomed.com/privkey.pem;
        
        location / {
            proxy_pass http://**********:5000;  # Hospital container
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }
    }
    
    # N8N Workflow Automation (n8n-waleed.shop)
    server {
        listen 443 ssl http2;
        server_name n8n-waleed.shop;
        
        ssl_certificate /etc/letsencrypt/live/n8n-waleed.shop/fullchain.pem;
        ssl_certificate_key /etc/letsencrypt/live/n8n-waleed.shop/privkey.pem;
        
        location / {
            proxy_pass http://**********:5678;  # N8N container
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            proxy_set_header Upgrade $http_upgrade;
            proxy_set_header Connection "upgrade";
            proxy_http_version 1.1;
        }
    }
    
    # Abu Soliman POS System (abusoliman.online)
    server {
        listen 443 ssl http2;
        server_name abusoliman.online www.abusoliman.online;
        
        ssl_certificate /etc/letsencrypt/live/abusoliman.online/fullchain.pem;
        ssl_certificate_key /etc/letsencrypt/live/abusoliman.online/privkey.pem;
        
        location / {
            proxy_pass http://**********:80;  # Abu Soliman container
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }
    }
    
    # Sarafly Management System (sarafly.online)
    server {
        listen 443 ssl http2;
        server_name sarafly.online www.sarafly.online;
        
        ssl_certificate /etc/letsencrypt/live/sarafly.online/fullchain.pem;
        ssl_certificate_key /etc/letsencrypt/live/sarafly.online/privkey.pem;
        
        location / {
            proxy_pass http://**********:80;  # Sarafly container
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }
    }
}
```

---

## 🔧 **COMMAND REFERENCE FOR CHATGPT**

### **Essential Diagnostic Commands:**

#### **Container Management:**
```bash
# Check all container status
docker ps -a
docker stats --no-stream

# Check container logs
docker logs CONTAINER_NAME --tail 20

# Restart specific container
docker restart CONTAINER_NAME

# Rebuild container with latest code
docker-compose down
docker-compose up -d --build
```

#### **Network Troubleshooting:**
```bash
# List all Docker networks
docker network ls

# Inspect container network configuration
docker inspect CONTAINER_NAME | grep -A 10 "Networks"

# Connect container to additional network
docker network connect NETWORK_NAME CONTAINER_NAME

# Test network connectivity between containers
docker exec CONTAINER_NAME curl -I http://TARGET_IP:PORT
```

#### **Git Deployment Issues:**
```bash
# Check git status and conflicts
git status
git log --oneline -5

# Resolve conflicts and force update
git stash push -m "Backup local changes"
git fetch origin
git reset --hard origin/main

# Verify correct commit
git log --oneline -1
```

#### **Resource Management:**
```bash
# Check disk space and Docker usage
df -h
docker system df

# Clean up Docker resources
docker image prune -f
docker system prune -a -f

# Check memory usage
free -h
```

#### **File Permissions:**
```bash
# Check file ownership
ls -la data/ | grep chat

# Fix container file permissions
chown 1000:1000 data/*.json
chmod 666 data/*.json
```

### **Website Testing:**
```bash
# Test all production websites
curl -I https://alorfmedz.com
curl -I https://alorfbiomed.com  
curl -I https://sarafly.online
curl -I https://abusoliman.online
curl -I https://n8n-waleed.shop

# Test FileBrowser
curl -I http://************:8090
```

---

## 🎯 **SYSTEMATIC TROUBLESHOOTING METHODOLOGY**

### **Phase 1: Initial Assessment**
1. **Identify scope** - Single application vs system-wide issue
2. **Check container status** - `docker ps -a`
3. **Verify basic connectivity** - `curl` tests
4. **Review recent changes** - Git commits, deployments

### **Phase 2: Resource Analysis**  
1. **Check system resources** - `df -h`, `free -h`
2. **Analyze Docker usage** - `docker system df`
3. **Review container logs** - `docker logs`
4. **Check system logs** - `journalctl`

### **Phase 3: Network Diagnosis**
1. **Map network topology** - `docker network ls`
2. **Test inter-container communication** - `docker exec ... curl`
3. **Verify proxy configuration** - nginx config review
4. **Check SSL certificates** - Certificate validity

### **Phase 4: Targeted Resolution**
1. **Apply specific fixes** based on root cause
2. **Test incrementally** after each change
3. **Verify end-to-end functionality**
4. **Document permanent solutions**

---

## 📊 **FINAL SYSTEM STATUS**

### **✅ All Production Websites Operational:**
- **alorfmedz.com** - Hospital Pharmacy Management (Python/Flask)
- **alorfbiomed.com** - Hospital Equipment System (Python/Flask)  
- **sarafly.online** - Sarafly Management System
- **abusoliman.online** - POS System
- **n8n-waleed.shop** - Workflow Automation
- **FileBrowser** - http://************:8090

### **✅ Infrastructure Health:**
- **7 containers** running and healthy
- **5 Docker networks** properly connected
- **5 SSL certificates** valid and functional
- **Nginx reverse proxy** routing correctly
- **Auto-deployment pipeline** restored

### **✅ Enhancements Deployed:**
- **Purchase Management** with status tracking
- **Delivery management** with date/recipient fields
- **Enhanced reporting** with row numbering
- **Medicine name display** instead of IDs
- **Advanced filtering** capabilities
- **Chatbot system** fully operational

---

## 🛡️ **PREVENTION STRATEGIES**

### **1. Docker Network Management:**
```bash
# Always connect nginx to all application networks during setup
docker network connect hospital_hospital_network alorfmedz-nginx
docker network connect sarafly-network alorfmedz-nginx  
docker network connect abusoliman-network alorfmedz-nginx
docker network connect root_default alorfmedz-nginx
```

### **2. Git Conflict Prevention:**
```bash
# Maintain comprehensive .gitignore
echo "data/chat_history.json" >> .gitignore
echo "data/chat_sessions.json" >> .gitignore
echo "*_PRODUCTION_ISSUE_SUMMARY.md" >> .gitignore
echo "*_DEBUG_REPORT.md" >> .gitignore
echo "test_*.py" >> .gitignore
echo "backups/" >> .gitignore
```

### **3. Regular Maintenance:**
```bash
# Weekly Docker cleanup
docker image prune -f
docker system prune -f

# Monthly full cleanup  
docker system prune -a -f
```

### **4. File Permission Automation:**
```dockerfile
# Add to all Dockerfiles
RUN chown -R appuser:appuser /app/data/*.json 2>/dev/null || true
```

### **5. Monitoring Setup:**
```bash
# Health check script
#!/bin/bash
for domain in alorfmedz.com alorfbiomed.com sarafly.online abusoliman.online n8n-waleed.shop; do
    if ! curl -f https://$domain > /dev/null 2>&1; then
        echo "ALERT: $domain is down" | mail <EMAIL>
    fi
done
```

---

## 🎓 **KEY LEARNINGS FOR CHATGPT**

### **Docker Network Architecture:**
- **Isolated networks** prevent cross-container communication
- **Nginx reverse proxy** requires access to ALL backend networks  
- **Network bridge connections** enable multi-network communication
- **Container IP addresses** can change between restarts

### **Deployment Pipeline Issues:**
- **Git conflicts** can silently break automated deployments
- **Docker rebuilds** may use old code if git pull fails
- **Health checks** can pass with outdated application versions
- **GitHub Actions** may report success despite deployment failures

### **File Permission Management:**
- **Container users** (UID/GID) must match file ownership
- **Docker volumes** preserve host file permissions
- **Permission fixes** should be automated in Dockerfiles
- **Restart impacts** can reset file permissions

### **Multi-Container Debugging:**
- **System-wide issues** require infrastructure-level diagnosis
- **Individual container health** doesn't guarantee system functionality
- **Network connectivity** is critical for microservices architecture
- **Resource constraints** can cause cascading failures

---

## 📞 **EMERGENCY PROCEDURES**

### **Quick System Recovery:**
```bash
# 1. Check all containers
docker ps -a

# 2. Restart failed containers
docker restart $(docker ps -aq --filter status=exited)

# 3. Connect nginx to all networks
docker network connect hospital_hospital_network alorfmedz-nginx
docker network connect sarafly-network alorfmedz-nginx  
docker network connect abusoliman-network alorfmedz-nginx
docker network connect root_default alorfmedz-nginx

# 4. Restart nginx
docker restart alorfmedz-nginx

# 5. Test all websites
curl -I https://alorfmedz.com
curl -I https://alorfbiomed.com
curl -I https://sarafly.online
curl -I https://abusoliman.online
curl -I https://n8n-waleed.shop
```

### **Emergency Rollback:**
```bash
# Rollback to previous working commit
cd /opt/alorfmedz
git log --oneline -10  # Find last working commit
git reset --hard <WORKING_COMMIT_HASH>
docker-compose down
docker-compose up -d --build
```

---

**This comprehensive guide provides complete context for ChatGPT to understand the multi-faceted troubleshooting process, system architecture, and resolution procedures for similar complex Docker deployment and networking issues.**

**Document Created:** July 28, 2025  
**Total Issues Resolved:** 4 major system failures  
**System Status:** All services operational ✅  
**Future Deployment:** Protected and automated 🛡️