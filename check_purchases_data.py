#!/usr/bin/env python3
"""
Script to check purchases data for missing purchaser fields
"""

import json
import random

def check_purchases_data():
    """Check purchases data for missing or empty purchaser fields"""
    
    # Load purchases data
    with open('data/purchases.json', 'r') as f:
        purchases = json.load(f)
    
    print(f"Total purchases: {len(purchases)}")
    
    missing_purchaser = []
    empty_purchaser = []
    valid_purchaser = []
    
    sample_names = ['Waleed', 'Marlen', 'Charlotte']
    
    for purchase in purchases:
        purchase_id = purchase.get('id', 'Unknown')
        purchaser = purchase.get('purchaser_name', None)
        
        if purchaser is None:
            missing_purchaser.append(purchase_id)
        elif purchaser == "" or purchaser.strip() == "":
            empty_purchaser.append(purchase_id)
        else:
            valid_purchaser.append((purchase_id, purchaser))
    
    print(f"\nMissing purchaser_name field: {len(missing_purchaser)}")
    if missing_purchaser:
        print(f"IDs: {missing_purchaser[:10]}...")  # Show first 10
    
    print(f"\nEmpty purchaser_name field: {len(empty_purchaser)}")
    if empty_purchaser:
        print(f"IDs: {empty_purchaser[:10]}...")  # Show first 10
    
    print(f"\nValid purchaser_name field: {len(valid_purchaser)}")
    if valid_purchaser:
        print("Sample valid entries:")
        for i, (pid, name) in enumerate(valid_purchaser[:5]):
            print(f"  ID {pid}: {name}")
    
    # Check if we need to fix any records
    records_to_fix = missing_purchaser + empty_purchaser
    
    if records_to_fix:
        print(f"\n🔧 Need to fix {len(records_to_fix)} records")
        
        # Update records with random sample names
        updated_count = 0
        for purchase in purchases:
            if purchase.get('id') in records_to_fix:
                purchase['purchaser_name'] = random.choice(sample_names)
                updated_count += 1
        
        # Save updated data
        with open('data/purchases.json', 'w') as f:
            json.dump(purchases, f, indent=2)
        
        print(f"✅ Updated {updated_count} purchase records with purchaser names")
        print(f"Used sample names: {sample_names}")
        
    else:
        print("\n✅ All purchase records already have valid purchaser names")

if __name__ == "__main__":
    check_purchases_data()
