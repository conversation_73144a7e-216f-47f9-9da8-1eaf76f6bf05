[{"date": "2025-07-25", "patient_id": "03", "medicines": [{"medicine_id": "01", "quantity": 1}], "prescribed_by": "dr waleed", "notes": "take care", "department_id": "02", "id": "01", "created_at": "2025-07-25T12:02:51.263725"}, {"id": "02", "patient_id": "18", "date": "2025-06-24", "medicines": [{"medicine_id": "92", "quantity": 4}], "prescribed_by": "Dr. <PERSON>", "notes": "Symptom management medication for <PERSON>", "created_at": "2025-07-27T01:14:50.604238", "department_id": "04"}, {"id": "03", "patient_id": "13", "date": "2025-05-17", "medicines": [{"medicine_id": "44", "quantity": 3}, {"medicine_id": "32", "quantity": 10}], "prescribed_by": "Dr. <PERSON>", "notes": "Preventive medication for <PERSON>", "created_at": "2025-07-27T01:14:50.604531", "department_id": "06"}, {"id": "04", "patient_id": "22", "date": "2025-04-28", "medicines": [{"medicine_id": "87", "quantity": 9}], "prescribed_by": "Dr. <PERSON>", "notes": "Pain management medication for <PERSON>", "created_at": "2025-07-27T01:14:50.604753", "department_id": "05"}, {"id": "05", "patient_id": "57", "date": "2025-06-22", "medicines": [{"medicine_id": "81", "quantity": 4}, {"medicine_id": "92", "quantity": 1}], "prescribed_by": "Dr. <PERSON>", "notes": "Follow-up medication for <PERSON>", "created_at": "2025-07-27T01:14:50.604963", "department_id": "06"}, {"id": "06", "patient_id": "41", "date": "2025-05-14", "medicines": [{"medicine_id": "41", "quantity": 8}], "prescribed_by": "Dr. <PERSON>", "notes": "Post-operative medication for <PERSON>", "created_at": "2025-07-27T01:14:50.605177", "department_id": "05"}, {"id": "07", "patient_id": "66", "date": "2025-06-13", "medicines": [{"medicine_id": "32", "quantity": 10}, {"medicine_id": "39", "quantity": 3}, {"medicine_id": "68", "quantity": 4}], "prescribed_by": "Dr. <PERSON>", "notes": "Emergency medication dispensed to <PERSON>", "created_at": "2025-07-27T01:14:50.605386", "department_id": "04"}, {"id": "08", "patient_id": "58", "date": "2025-07-17", "medicines": [{"medicine_id": "56", "quantity": 4}, {"medicine_id": "41", "quantity": 2}], "prescribed_by": "Dr. <PERSON>", "notes": "Routine prescription refill for <PERSON>", "created_at": "2025-07-27T01:14:50.605604", "department_id": "07"}, {"id": "09", "patient_id": "04", "date": "2025-07-23", "medicines": [{"medicine_id": "45", "quantity": 6}, {"medicine_id": "97", "quantity": 8}, {"medicine_id": "03", "quantity": 4}], "prescribed_by": "Dr. <PERSON>", "notes": "Chronic condition management for <PERSON>", "created_at": "2025-07-27T01:14:50.605808", "department_id": "08"}, {"id": "10", "patient_id": "54", "date": "2025-06-08", "medicines": [{"medicine_id": "101", "quantity": 2}, {"medicine_id": "84", "quantity": 10}], "prescribed_by": "Dr. <PERSON>", "notes": "Preventive medication for <PERSON>", "created_at": "2025-07-27T01:14:50.606023", "department_id": "10"}, {"id": "11", "patient_id": "41", "date": "2025-06-17", "medicines": [{"medicine_id": "81", "quantity": 4}, {"medicine_id": "39", "quantity": 8}], "prescribed_by": "Dr. <PERSON>", "notes": "Therapeutic medication for <PERSON>", "created_at": "2025-07-27T01:14:50.606504", "department_id": "05"}, {"id": "12", "patient_id": "42", "date": "2025-05-04", "medicines": [{"medicine_id": "06", "quantity": 1}, {"medicine_id": "14", "quantity": 4}], "prescribed_by": "Dr. <PERSON>", "notes": "Pain management medication for <PERSON>", "created_at": "2025-07-27T01:14:50.606786", "department_id": "06"}, {"id": "13", "patient_id": "26", "date": "2025-06-22", "medicines": [{"medicine_id": "86", "quantity": 4}], "prescribed_by": "Dr. <PERSON>", "notes": "Regular medication for <PERSON>", "created_at": "2025-07-27T01:14:50.607067", "department_id": "05"}, {"id": "14", "patient_id": "61", "date": "2025-07-02", "medicines": [{"medicine_id": "63", "quantity": 9}, {"medicine_id": "79", "quantity": 3}], "prescribed_by": "Dr. <PERSON>", "notes": "Acute treatment medication for <PERSON>", "created_at": "2025-07-27T01:14:50.607349", "department_id": "07"}, {"id": "15", "patient_id": "65", "date": "2025-07-22", "medicines": [{"medicine_id": "71", "quantity": 4}], "prescribed_by": "Dr. <PERSON>", "notes": "Post-operative medication for <PERSON>", "created_at": "2025-07-27T01:14:50.607602", "department_id": "07"}, {"id": "16", "patient_id": "29", "date": "2025-05-01", "medicines": [{"medicine_id": "39", "quantity": 9}, {"medicine_id": "74", "quantity": 2}, {"medicine_id": "05", "quantity": 10}], "prescribed_by": "Dr. <PERSON>", "notes": "Regular medication for <PERSON>", "created_at": "2025-07-27T01:14:50.607851", "department_id": "04"}, {"id": "17", "patient_id": "52", "date": "2025-05-30", "medicines": [{"medicine_id": "70", "quantity": 1}], "prescribed_by": "Dr. <PERSON>", "notes": "Pain management medication for <PERSON>", "created_at": "2025-07-27T01:14:50.608079", "department_id": "06"}, {"id": "18", "patient_id": "06", "date": "2025-06-22", "medicines": [{"medicine_id": "24", "quantity": 9}, {"medicine_id": "21", "quantity": 3}], "prescribed_by": "Dr. <PERSON>", "notes": "Pain management medication for <PERSON>", "created_at": "2025-07-27T01:14:50.608305", "department_id": "08"}, {"id": "19", "patient_id": "26", "date": "2025-06-16", "medicines": [{"medicine_id": "52", "quantity": 3}, {"medicine_id": "32", "quantity": 10}], "prescribed_by": "Dr. <PERSON>", "notes": "Chronic condition management for <PERSON>", "created_at": "2025-07-27T01:14:50.608517", "department_id": "05"}, {"id": "20", "patient_id": "26", "date": "2025-06-19", "medicines": [{"medicine_id": "42", "quantity": 9}], "prescribed_by": "Dr. <PERSON>", "notes": "Pain management medication for <PERSON>", "created_at": "2025-07-27T01:14:50.608741", "department_id": "05"}, {"id": "21", "patient_id": "31", "date": "2025-07-04", "medicines": [{"medicine_id": "101", "quantity": 10}, {"medicine_id": "91", "quantity": 5}, {"medicine_id": "49", "quantity": 7}], "prescribed_by": "Dr. <PERSON>", "notes": "Acute treatment medication for <PERSON>", "created_at": "2025-07-27T01:14:50.609416", "department_id": "04"}, {"id": "22", "patient_id": "38", "date": "2025-07-21", "medicines": [{"medicine_id": "20", "quantity": 8}], "prescribed_by": "Dr. <PERSON>", "notes": "Chronic condition management for <PERSON>", "created_at": "2025-07-27T01:14:50.609627", "department_id": "06"}, {"id": "23", "patient_id": "37", "date": "2025-06-01", "medicines": [{"medicine_id": "86", "quantity": 8}, {"medicine_id": "103", "quantity": 8}, {"medicine_id": "95", "quantity": 7}], "prescribed_by": "Dr. <PERSON>", "notes": "Prescribed medication dispensed to <PERSON>", "created_at": "2025-07-27T01:14:50.609832", "department_id": "05"}, {"id": "24", "patient_id": "18", "date": "2025-05-02", "medicines": [{"medicine_id": "29", "quantity": 10}, {"medicine_id": "86", "quantity": 4}, {"medicine_id": "58", "quantity": 8}], "prescribed_by": "Dr. <PERSON>", "notes": "Regular medication for <PERSON>", "created_at": "2025-07-27T01:14:50.610042", "department_id": "04"}, {"id": "25", "patient_id": "16", "date": "2025-07-10", "medicines": [{"medicine_id": "52", "quantity": 3}, {"medicine_id": "79", "quantity": 8}, {"medicine_id": "98", "quantity": 3}], "prescribed_by": "Dr. <PERSON>", "notes": "Symptom management medication for John Test Patient", "created_at": "2025-07-27T01:14:50.610248", "department_id": "05"}, {"id": "26", "patient_id": "09", "date": "2025-07-05", "medicines": [{"medicine_id": "32", "quantity": 6}, {"medicine_id": "03", "quantity": 5}], "prescribed_by": "Dr. <PERSON>", "notes": "Regular medication for <PERSON>", "created_at": "2025-07-27T01:14:50.610466", "department_id": "06"}, {"id": "27", "patient_id": "08", "date": "2025-06-17", "medicines": [{"medicine_id": "36", "quantity": 4}, {"medicine_id": "52", "quantity": 9}, {"medicine_id": "72", "quantity": 10}], "prescribed_by": "Dr. <PERSON>", "notes": "Prescribed medication dispensed to <PERSON>", "created_at": "2025-07-27T01:14:50.610918", "department_id": "07"}, {"id": "28", "patient_id": "43", "date": "2025-05-15", "medicines": [{"medicine_id": "19", "quantity": 5}], "prescribed_by": "Dr. <PERSON>", "notes": "Regular medication for <PERSON>", "created_at": "2025-07-27T01:14:50.611128", "department_id": "06"}, {"id": "29", "patient_id": "66", "date": "2025-05-12", "medicines": [{"medicine_id": "99", "quantity": 5}, {"medicine_id": "63", "quantity": 7}], "prescribed_by": "Dr. <PERSON>", "notes": "Chronic condition management for <PERSON>", "created_at": "2025-07-27T01:14:50.611343", "department_id": "04"}, {"id": "30", "patient_id": "13", "date": "2025-07-06", "medicines": [{"medicine_id": "65", "quantity": 5}], "prescribed_by": "Dr. <PERSON>", "notes": "Regular medication for <PERSON>", "created_at": "2025-07-27T01:14:50.611547", "department_id": "06"}, {"id": "31", "patient_id": "07", "date": "2025-06-09", "medicines": [{"medicine_id": "26", "quantity": 3}, {"medicine_id": "15", "quantity": 3}], "prescribed_by": "Dr. <PERSON>", "notes": "Maintenance therapy for <PERSON>", "created_at": "2025-07-27T01:14:50.611748", "department_id": "02"}, {"id": "32", "patient_id": "40", "date": "2025-05-17", "medicines": [{"medicine_id": "32", "quantity": 5}, {"medicine_id": "50", "quantity": 7}], "prescribed_by": "Dr. <PERSON>", "notes": "Follow-up medication for <PERSON>", "created_at": "2025-07-27T01:14:50.611958", "department_id": "10"}, {"id": "33", "patient_id": "11", "date": "2025-07-07", "medicines": [{"medicine_id": "11", "quantity": 9}], "prescribed_by": "Dr. <PERSON>", "notes": "Preventive medication for <PERSON>", "created_at": "2025-07-27T01:14:50.612566", "department_id": "06"}, {"id": "34", "patient_id": "17", "date": "2025-06-12", "medicines": [{"medicine_id": "99", "quantity": 1}], "prescribed_by": "Dr. <PERSON>", "notes": "Recovery medication for <PERSON>", "created_at": "2025-07-27T01:14:50.612773", "department_id": "08"}, {"id": "35", "patient_id": "35", "date": "2025-05-01", "medicines": [{"medicine_id": "32", "quantity": 3}], "prescribed_by": "Dr. <PERSON>", "notes": "Routine prescription refill for <PERSON>", "created_at": "2025-07-27T01:14:50.612971", "department_id": "04"}, {"id": "36", "patient_id": "08", "date": "2025-07-11", "medicines": [{"medicine_id": "54", "quantity": 3}], "prescribed_by": "Dr. <PERSON>", "notes": "Acute treatment medication for <PERSON>", "created_at": "2025-07-27T01:14:50.613174", "department_id": "07"}, {"id": "37", "patient_id": "15", "date": "2025-05-16", "medicines": [{"medicine_id": "44", "quantity": 1}], "prescribed_by": "Dr. <PERSON>", "notes": "Pain management medication for <PERSON>", "created_at": "2025-07-27T01:14:50.613375", "department_id": "02"}, {"id": "38", "patient_id": "62", "date": "2025-06-05", "medicines": [{"medicine_id": "96", "quantity": 10}, {"medicine_id": "101", "quantity": 10}], "prescribed_by": "Dr. <PERSON>", "notes": "Maintenance therapy for <PERSON>", "created_at": "2025-07-27T01:14:50.613586", "department_id": "05"}, {"id": "39", "patient_id": "11", "date": "2025-05-06", "medicines": [{"medicine_id": "56", "quantity": 9}, {"medicine_id": "28", "quantity": 7}], "prescribed_by": "Dr. <PERSON>", "notes": "Pain management medication for <PERSON>", "created_at": "2025-07-27T01:14:50.613784", "department_id": "06"}, {"id": "40", "patient_id": "58", "date": "2025-05-17", "medicines": [{"medicine_id": "86", "quantity": 7}], "prescribed_by": "Dr. <PERSON>", "notes": "Maintenance therapy for <PERSON>", "created_at": "2025-07-27T01:14:50.613984", "department_id": "07"}, {"id": "41", "patient_id": "54", "date": "2025-07-12", "medicines": [{"medicine_id": "48", "quantity": 4}], "prescribed_by": "Dr. <PERSON>", "notes": "Recovery medication for <PERSON>", "created_at": "2025-07-27T01:14:50.614194", "department_id": "10"}, {"id": "42", "patient_id": "41", "date": "2025-07-16", "medicines": [{"medicine_id": "53", "quantity": 2}], "prescribed_by": "Dr. <PERSON>", "notes": "Chronic condition management for <PERSON>", "created_at": "2025-07-27T01:14:50.614602", "department_id": "05"}, {"id": "43", "patient_id": "66", "date": "2025-05-13", "medicines": [{"medicine_id": "58", "quantity": 9}, {"medicine_id": "43", "quantity": 6}, {"medicine_id": "63", "quantity": 9}], "prescribed_by": "Dr. <PERSON>", "notes": "Specialist-recommended medication for <PERSON>", "created_at": "2025-07-27T01:14:50.615361", "department_id": "04"}, {"id": "44", "patient_id": "31", "date": "2025-05-26", "medicines": [{"medicine_id": "40", "quantity": 1}, {"medicine_id": "52", "quantity": 6}], "prescribed_by": "Dr. <PERSON>", "notes": "Pain management medication for <PERSON>", "created_at": "2025-07-27T01:14:50.616201", "department_id": "04"}, {"id": "45", "patient_id": "23", "date": "2025-05-02", "medicines": [{"medicine_id": "40", "quantity": 6}, {"medicine_id": "18", "quantity": 2}], "prescribed_by": "Dr. <PERSON>", "notes": "Regular medication for <PERSON>", "created_at": "2025-07-27T01:14:50.616397", "department_id": "06"}, {"id": "46", "patient_id": "63", "date": "2025-06-09", "medicines": [{"medicine_id": "27", "quantity": 2}], "prescribed_by": "Dr. <PERSON>", "notes": "Specialist-recommended medication for <PERSON>", "created_at": "2025-07-27T01:14:50.616805", "department_id": "06"}, {"id": "47", "patient_id": "07", "date": "2025-06-25", "medicines": [{"medicine_id": "48", "quantity": 10}, {"medicine_id": "55", "quantity": 1}, {"medicine_id": "46", "quantity": 7}], "prescribed_by": "Dr. <PERSON>", "notes": "Emergency medication dispensed to <PERSON>", "created_at": "2025-07-27T01:14:50.617207", "department_id": "02"}, {"id": "48", "patient_id": "49", "date": "2025-07-15", "medicines": [{"medicine_id": "102", "quantity": 7}, {"medicine_id": "49", "quantity": 10}, {"medicine_id": "01", "quantity": 7}], "prescribed_by": "Dr. <PERSON>", "notes": "Chronic condition management for <PERSON>", "created_at": "2025-07-27T01:14:50.617432", "department_id": "07"}, {"id": "49", "patient_id": "39", "date": "2025-06-27", "medicines": [{"medicine_id": "14", "quantity": 8}, {"medicine_id": "91", "quantity": 2}], "prescribed_by": "Dr. <PERSON>", "notes": "Emergency medication dispensed to <PERSON>", "created_at": "2025-07-27T01:14:50.618060", "department_id": "08"}, {"id": "50", "patient_id": "37", "date": "2025-07-13", "medicines": [{"medicine_id": "102", "quantity": 1}], "prescribed_by": "Dr. <PERSON>", "notes": "Emergency medication dispensed to <PERSON>", "created_at": "2025-07-27T01:14:50.618611", "department_id": "05"}, {"id": "51", "patient_id": "16", "date": "2025-07-08", "medicines": [{"medicine_id": "54", "quantity": 2}, {"medicine_id": "35", "quantity": 4}, {"medicine_id": "05", "quantity": 9}], "prescribed_by": "Dr. <PERSON>", "notes": "Prescribed medication dispensed to John Test Patient", "created_at": "2025-07-27T01:14:50.618899", "department_id": "05"}, {"id": "52", "patient_id": "41", "date": "2025-05-04", "medicines": [{"medicine_id": "32", "quantity": 7}], "prescribed_by": "Dr. <PERSON>", "notes": "Pain management medication for <PERSON>", "created_at": "2025-07-27T01:14:50.619218", "department_id": "05"}, {"id": "53", "patient_id": "28", "date": "2025-06-06", "medicines": [{"medicine_id": "24", "quantity": 7}, {"medicine_id": "86", "quantity": 4}], "prescribed_by": "Dr. <PERSON>", "notes": "Pain management medication for <PERSON>", "created_at": "2025-07-27T01:14:50.619500", "department_id": "07"}, {"id": "54", "patient_id": "54", "date": "2025-06-14", "medicines": [{"medicine_id": "22", "quantity": 6}], "prescribed_by": "Dr. <PERSON>", "notes": "Follow-up medication for <PERSON>", "created_at": "2025-07-27T01:14:50.619757", "department_id": "10"}, {"id": "55", "patient_id": "55", "date": "2025-07-13", "medicines": [{"medicine_id": "91", "quantity": 5}], "prescribed_by": "Dr. <PERSON>", "notes": "Follow-up medication for <PERSON>", "created_at": "2025-07-27T01:14:50.620243", "department_id": "06"}, {"id": "56", "patient_id": "58", "date": "2025-05-08", "medicines": [{"medicine_id": "61", "quantity": 8}], "prescribed_by": "Dr. <PERSON>", "notes": "Recovery medication for <PERSON>", "created_at": "2025-07-27T01:14:50.620537", "department_id": "07"}, {"id": "57", "patient_id": "41", "date": "2025-06-06", "medicines": [{"medicine_id": "26", "quantity": 10}, {"medicine_id": "60", "quantity": 8}], "prescribed_by": "Dr. <PERSON>", "notes": "Emergency medication dispensed to <PERSON>", "created_at": "2025-07-27T01:14:50.620811", "department_id": "05"}, {"id": "58", "patient_id": "06", "date": "2025-05-12", "medicines": [{"medicine_id": "15", "quantity": 1}, {"medicine_id": "98", "quantity": 7}], "prescribed_by": "Dr. <PERSON>", "notes": "Symptom management medication for <PERSON>", "created_at": "2025-07-27T01:14:50.621035", "department_id": "08"}, {"id": "59", "patient_id": "37", "date": "2025-06-10", "medicines": [{"medicine_id": "95", "quantity": 9}, {"medicine_id": "17", "quantity": 9}, {"medicine_id": "72", "quantity": 9}], "prescribed_by": "Dr. <PERSON>", "notes": "Regular medication for <PERSON>", "created_at": "2025-07-27T01:14:50.621241", "department_id": "05"}, {"id": "60", "patient_id": "53", "date": "2025-06-30", "medicines": [{"medicine_id": "14", "quantity": 6}], "prescribed_by": "Dr. <PERSON>", "notes": "Pain management medication for <PERSON>", "created_at": "2025-07-27T01:14:50.621677", "department_id": "05"}, {"id": "61", "patient_id": "11", "date": "2025-07-26", "medicines": [{"medicine_id": "71", "quantity": 9}, {"medicine_id": "96", "quantity": 3}], "prescribed_by": "Dr. <PERSON>", "notes": "Pain management medication for <PERSON>", "created_at": "2025-07-27T01:14:50.621941", "department_id": "06"}, {"id": "62", "patient_id": "58", "date": "2025-07-09", "medicines": [{"medicine_id": "20", "quantity": 7}], "prescribed_by": "Dr. <PERSON>", "notes": "Routine prescription refill for <PERSON>", "created_at": "2025-07-27T01:14:50.622157", "department_id": "07"}, {"id": "63", "patient_id": "62", "date": "2025-05-25", "medicines": [{"medicine_id": "10", "quantity": 6}, {"medicine_id": "103", "quantity": 4}], "prescribed_by": "Dr. <PERSON>", "notes": "Acute treatment medication for <PERSON>", "created_at": "2025-07-27T01:14:50.622386", "department_id": "05"}, {"id": "64", "patient_id": "23", "date": "2025-07-23", "medicines": [{"medicine_id": "03", "quantity": 4}, {"medicine_id": "62", "quantity": 2}, {"medicine_id": "102", "quantity": 10}], "prescribed_by": "Dr. <PERSON>", "notes": "Prescribed medication dispensed to <PERSON>", "created_at": "2025-07-27T01:14:50.622686", "department_id": "06"}, {"id": "65", "patient_id": "29", "date": "2025-05-27", "medicines": [{"medicine_id": "43", "quantity": 3}, {"medicine_id": "58", "quantity": 4}], "prescribed_by": "Dr. <PERSON>", "notes": "Pain management medication for <PERSON>", "created_at": "2025-07-27T01:14:50.622980", "department_id": "04"}, {"id": "66", "patient_id": "56", "date": "2025-06-27", "medicines": [{"medicine_id": "36", "quantity": 1}, {"medicine_id": "13", "quantity": 9}], "prescribed_by": "Dr. <PERSON>", "notes": "Pain management medication for <PERSON>", "created_at": "2025-07-27T01:14:50.623216", "department_id": "06"}, {"id": "67", "patient_id": "39", "date": "2025-04-29", "medicines": [{"medicine_id": "52", "quantity": 9}], "prescribed_by": "Dr. <PERSON>", "notes": "Acute treatment medication for <PERSON>", "created_at": "2025-07-27T01:14:50.623444", "department_id": "08"}, {"id": "68", "patient_id": "38", "date": "2025-05-12", "medicines": [{"medicine_id": "90", "quantity": 3}, {"medicine_id": "32", "quantity": 6}, {"medicine_id": "25", "quantity": 6}], "prescribed_by": "Dr. <PERSON>", "notes": "Chronic condition management for <PERSON>", "created_at": "2025-07-27T01:14:50.623711", "department_id": "06"}, {"id": "69", "patient_id": "09", "date": "2025-04-28", "medicines": [{"medicine_id": "74", "quantity": 3}, {"medicine_id": "03", "quantity": 3}], "prescribed_by": "Dr. <PERSON>", "notes": "Follow-up medication for <PERSON>", "created_at": "2025-07-27T01:14:50.623946", "department_id": "06"}, {"id": "70", "patient_id": "52", "date": "2025-06-07", "medicines": [{"medicine_id": "08", "quantity": 9}, {"medicine_id": "11", "quantity": 2}, {"medicine_id": "73", "quantity": 4}], "prescribed_by": "Dr. <PERSON>", "notes": "Preventive medication for <PERSON>", "created_at": "2025-07-27T01:14:50.624199", "department_id": "06"}, {"id": "71", "patient_id": "52", "date": "2025-06-27", "medicines": [{"medicine_id": "25", "quantity": 3}], "prescribed_by": "Dr. <PERSON>", "notes": "Routine prescription refill for <PERSON>", "created_at": "2025-07-27T01:14:50.624504", "department_id": "06"}, {"id": "72", "patient_id": "09", "date": "2025-05-06", "medicines": [{"medicine_id": "50", "quantity": 6}, {"medicine_id": "55", "quantity": 1}], "prescribed_by": "Dr. <PERSON>", "notes": "Preventive medication for <PERSON>", "created_at": "2025-07-27T01:14:50.624818", "department_id": "06"}, {"id": "73", "patient_id": "15", "date": "2025-06-06", "medicines": [{"medicine_id": "08", "quantity": 2}, {"medicine_id": "74", "quantity": 1}], "prescribed_by": "Dr. <PERSON>", "notes": "Regular medication for <PERSON>", "created_at": "2025-07-27T01:14:50.625056", "department_id": "02"}, {"id": "74", "patient_id": "64", "date": "2025-05-25", "medicines": [{"medicine_id": "99", "quantity": 6}, {"medicine_id": "68", "quantity": 4}, {"medicine_id": "98", "quantity": 5}], "prescribed_by": "Dr. <PERSON>", "notes": "Pain management medication for <PERSON>", "created_at": "2025-07-27T01:14:50.625284", "department_id": "05"}, {"id": "75", "patient_id": "19", "date": "2025-07-02", "medicines": [{"medicine_id": "100", "quantity": 7}, {"medicine_id": "14", "quantity": 8}], "prescribed_by": "Dr. <PERSON>", "notes": "Acute treatment medication for <PERSON>", "created_at": "2025-07-27T01:14:50.625490", "department_id": "10"}, {"id": "76", "patient_id": "60", "date": "2025-07-26", "medicines": [{"medicine_id": "96", "quantity": 5}], "prescribed_by": "Dr. <PERSON>", "notes": "Maintenance therapy for <PERSON>", "created_at": "2025-07-27T01:14:50.625729", "department_id": "04"}, {"id": "77", "patient_id": "29", "date": "2025-05-14", "medicines": [{"medicine_id": "87", "quantity": 5}], "prescribed_by": "Dr. <PERSON>", "notes": "Maintenance therapy for <PERSON>", "created_at": "2025-07-27T01:14:50.625941", "department_id": "04"}, {"id": "78", "patient_id": "13", "date": "2025-06-24", "medicines": [{"medicine_id": "44", "quantity": 4}, {"medicine_id": "05", "quantity": 3}], "prescribed_by": "Dr. <PERSON>", "notes": "Regular medication for <PERSON>", "created_at": "2025-07-27T01:14:50.626155", "department_id": "06"}, {"id": "79", "patient_id": "40", "date": "2025-06-27", "medicines": [{"medicine_id": "52", "quantity": 1}, {"medicine_id": "70", "quantity": 7}], "prescribed_by": "Dr. <PERSON>", "notes": "Prescribed medication dispensed to <PERSON>", "created_at": "2025-07-27T01:14:50.626360", "department_id": "10"}, {"id": "80", "patient_id": "32", "date": "2025-06-02", "medicines": [{"medicine_id": "54", "quantity": 9}, {"medicine_id": "18", "quantity": 4}, {"medicine_id": "81", "quantity": 8}], "prescribed_by": "Dr. <PERSON>", "notes": "Therapeutic medication for <PERSON>", "created_at": "2025-07-27T01:14:50.626781", "department_id": "07"}, {"id": "81", "patient_id": "41", "date": "2025-06-12", "medicines": [{"medicine_id": "89", "quantity": 9}], "prescribed_by": "Dr. <PERSON>", "notes": "Preventive medication for <PERSON>", "created_at": "2025-07-27T01:14:50.626991", "department_id": "05"}, {"id": "82", "patient_id": "37", "date": "2025-06-29", "medicines": [{"medicine_id": "99", "quantity": 6}, {"medicine_id": "09", "quantity": 4}, {"medicine_id": "14", "quantity": 7}], "prescribed_by": "Dr. <PERSON>", "notes": "Acute treatment medication for <PERSON>", "created_at": "2025-07-27T01:14:50.627219", "department_id": "05"}, {"id": "83", "patient_id": "15", "date": "2025-06-10", "medicines": [{"medicine_id": "29", "quantity": 6}, {"medicine_id": "03", "quantity": 10}], "prescribed_by": "Dr. <PERSON>", "notes": "Acute treatment medication for <PERSON>", "created_at": "2025-07-27T01:14:50.627621", "department_id": "02"}, {"id": "84", "patient_id": "22", "date": "2025-05-19", "medicines": [{"medicine_id": "05", "quantity": 3}, {"medicine_id": "84", "quantity": 2}, {"medicine_id": "30", "quantity": 3}], "prescribed_by": "Dr. <PERSON>", "notes": "Symptom management medication for <PERSON>", "created_at": "2025-07-27T01:14:50.628250", "department_id": "05"}, {"id": "85", "patient_id": "45", "date": "2025-05-13", "medicines": [{"medicine_id": "48", "quantity": 8}, {"medicine_id": "52", "quantity": 5}], "prescribed_by": "Dr. <PERSON>", "notes": "Specialist-recommended medication for <PERSON>", "created_at": "2025-07-27T01:14:50.628561", "department_id": "06"}, {"id": "86", "patient_id": "08", "date": "2025-06-08", "medicines": [{"medicine_id": "31", "quantity": 6}, {"medicine_id": "09", "quantity": 1}], "prescribed_by": "Dr. <PERSON>", "notes": "Recovery medication for <PERSON>", "created_at": "2025-07-27T01:14:50.628848", "department_id": "07"}, {"id": "87", "patient_id": "26", "date": "2025-05-31", "medicines": [{"medicine_id": "41", "quantity": 4}, {"medicine_id": "17", "quantity": 4}, {"medicine_id": "59", "quantity": 7}], "prescribed_by": "Dr. <PERSON>", "notes": "Chronic condition management for <PERSON>", "created_at": "2025-07-27T01:14:50.629163", "department_id": "05"}, {"id": "88", "patient_id": "52", "date": "2025-06-03", "medicines": [{"medicine_id": "10", "quantity": 9}], "prescribed_by": "Dr. <PERSON>", "notes": "Prescribed medication dispensed to <PERSON>", "created_at": "2025-07-27T01:14:50.629482", "department_id": "06"}, {"id": "89", "patient_id": "35", "date": "2025-07-20", "medicines": [{"medicine_id": "42", "quantity": 6}, {"medicine_id": "82", "quantity": 1}, {"medicine_id": "75", "quantity": 10}], "prescribed_by": "Dr. <PERSON>", "notes": "Recovery medication for <PERSON>", "created_at": "2025-07-27T01:14:50.629785", "department_id": "04"}, {"id": "90", "patient_id": "60", "date": "2025-06-01", "medicines": [{"medicine_id": "89", "quantity": 6}], "prescribed_by": "Dr. <PERSON>", "notes": "Pain management medication for <PERSON>", "created_at": "2025-07-27T01:14:50.630160", "department_id": "04"}, {"id": "91", "patient_id": "47", "date": "2025-07-11", "medicines": [{"medicine_id": "99", "quantity": 10}, {"medicine_id": "68", "quantity": 8}], "prescribed_by": "Dr. <PERSON>", "notes": "Chronic condition management for <PERSON>", "created_at": "2025-07-27T01:14:50.630535", "department_id": "05"}, {"id": "92", "patient_id": "57", "date": "2025-06-07", "medicines": [{"medicine_id": "51", "quantity": 5}], "prescribed_by": "Dr. <PERSON>", "notes": "Pain management medication for <PERSON>", "created_at": "2025-07-27T01:14:50.630879", "department_id": "06"}, {"id": "93", "patient_id": "41", "date": "2025-07-10", "medicines": [{"medicine_id": "38", "quantity": 1}], "prescribed_by": "Dr. <PERSON>", "notes": "Chronic condition management for <PERSON>", "created_at": "2025-07-27T01:14:50.631181", "department_id": "05"}, {"id": "94", "patient_id": "33", "date": "2025-05-04", "medicines": [{"medicine_id": "08", "quantity": 3}], "prescribed_by": "Dr. <PERSON>", "notes": "Prescribed medication dispensed to <PERSON>", "created_at": "2025-07-27T01:14:50.632074", "department_id": "06"}, {"id": "95", "patient_id": "18", "date": "2025-05-26", "medicines": [{"medicine_id": "13", "quantity": 5}], "prescribed_by": "Dr. <PERSON>", "notes": "Acute treatment medication for <PERSON>", "created_at": "2025-07-27T01:14:50.632342", "department_id": "04"}, {"id": "96", "patient_id": "41", "date": "2025-07-20", "medicines": [{"medicine_id": "69", "quantity": 9}, {"medicine_id": "83", "quantity": 4}], "prescribed_by": "Dr. <PERSON>", "notes": "Emergency medication dispensed to <PERSON>", "created_at": "2025-07-27T01:14:50.632645", "department_id": "05"}, {"id": "97", "patient_id": "28", "date": "2025-06-08", "medicines": [{"medicine_id": "22", "quantity": 4}, {"medicine_id": "82", "quantity": 8}, {"medicine_id": "76", "quantity": 1}], "prescribed_by": "Dr. <PERSON>", "notes": "Post-operative medication for <PERSON>", "created_at": "2025-07-27T01:14:50.632947", "department_id": "07"}, {"id": "98", "patient_id": "64", "date": "2025-05-02", "medicines": [{"medicine_id": "31", "quantity": 7}, {"medicine_id": "58", "quantity": 7}, {"medicine_id": "39", "quantity": 10}], "prescribed_by": "Dr. <PERSON>", "notes": "Preventive medication for <PERSON>", "created_at": "2025-07-27T01:14:50.633238", "department_id": "05"}, {"id": "99", "patient_id": "21", "date": "2025-07-16", "medicines": [{"medicine_id": "35", "quantity": 8}], "prescribed_by": "Dr. <PERSON>", "notes": "Emergency medication dispensed to <PERSON>", "created_at": "2025-07-27T01:14:50.633532", "department_id": "08"}, {"id": "100", "patient_id": "25", "date": "2025-07-18", "medicines": [{"medicine_id": "12", "quantity": 7}, {"medicine_id": "20", "quantity": 3}], "prescribed_by": "Dr. <PERSON>", "notes": "Symptom management medication for <PERSON>", "created_at": "2025-07-27T01:14:50.633841", "department_id": "05"}, {"id": "101", "patient_id": "55", "date": "2025-05-03", "medicines": [{"medicine_id": "64", "quantity": 5}], "prescribed_by": "Dr. <PERSON>", "notes": "Maintenance therapy for <PERSON>", "created_at": "2025-07-27T01:14:50.634520", "department_id": "06"}, {"id": "102", "patient_id": "32", "date": "2025-07-11", "medicines": [{"medicine_id": "28", "quantity": 5}], "prescribed_by": "Dr. <PERSON>", "notes": "Acute treatment medication for <PERSON>", "created_at": "2025-07-27T01:14:50.634783", "department_id": "07"}, {"id": "103", "patient_id": "62", "date": "2025-07-26", "medicines": [{"medicine_id": "51", "quantity": 6}], "prescribed_by": "Dr. <PERSON>", "notes": "Routine prescription refill for <PERSON>", "created_at": "2025-07-27T01:14:50.635031", "department_id": "05"}, {"id": "104", "patient_id": "49", "date": "2025-04-28", "medicines": [{"medicine_id": "44", "quantity": 9}, {"medicine_id": "56", "quantity": 8}], "prescribed_by": "Dr. <PERSON>", "notes": "Pain management medication for <PERSON>", "created_at": "2025-07-27T01:14:50.635273", "department_id": "07"}, {"id": "105", "patient_id": "47", "date": "2025-06-13", "medicines": [{"medicine_id": "92", "quantity": 5}, {"medicine_id": "97", "quantity": 4}, {"medicine_id": "94", "quantity": 4}], "prescribed_by": "Dr. <PERSON>", "notes": "Therapeutic medication for <PERSON>", "created_at": "2025-07-27T01:14:50.635534", "department_id": "05"}, {"id": "106", "patient_id": "35", "date": "2025-05-19", "medicines": [{"medicine_id": "57", "quantity": 10}, {"medicine_id": "103", "quantity": 5}], "prescribed_by": "Dr. <PERSON>", "notes": "Acute treatment medication for <PERSON>", "created_at": "2025-07-27T01:14:50.636370", "department_id": "04"}, {"id": "107", "patient_id": "56", "date": "2025-07-21", "medicines": [{"medicine_id": "26", "quantity": 5}], "prescribed_by": "Dr. <PERSON>", "notes": "Therapeutic medication for <PERSON>", "created_at": "2025-07-27T01:14:50.636690", "department_id": "06"}, {"id": "108", "patient_id": "40", "date": "2025-07-18", "medicines": [{"medicine_id": "62", "quantity": 8}, {"medicine_id": "06", "quantity": 2}], "prescribed_by": "Dr. <PERSON>", "notes": "Emergency medication dispensed to <PERSON>", "created_at": "2025-07-27T01:14:50.636942", "department_id": "10"}, {"id": "109", "patient_id": "64", "date": "2025-06-07", "medicines": [{"medicine_id": "32", "quantity": 5}, {"medicine_id": "03", "quantity": 4}, {"medicine_id": "26", "quantity": 8}], "prescribed_by": "Dr. <PERSON>", "notes": "Maintenance therapy for <PERSON>", "created_at": "2025-07-27T01:14:50.637192", "department_id": "05"}, {"id": "110", "patient_id": "37", "date": "2025-07-20", "medicines": [{"medicine_id": "31", "quantity": 6}], "prescribed_by": "Dr. <PERSON>", "notes": "Maintenance therapy for <PERSON>", "created_at": "2025-07-27T01:14:50.637417", "department_id": "05"}, {"id": "111", "patient_id": "07", "date": "2025-05-23", "medicines": [{"medicine_id": "89", "quantity": 9}, {"medicine_id": "72", "quantity": 2}, {"medicine_id": "53", "quantity": 10}], "prescribed_by": "Dr. <PERSON>", "notes": "Recovery medication for <PERSON>", "created_at": "2025-07-27T01:14:50.637806", "department_id": "02"}, {"id": "112", "patient_id": "49", "date": "2025-05-07", "medicines": [{"medicine_id": "09", "quantity": 7}, {"medicine_id": "70", "quantity": 4}, {"medicine_id": "80", "quantity": 4}], "prescribed_by": "Dr. <PERSON>", "notes": "Preventive medication for <PERSON>", "created_at": "2025-07-27T01:14:50.638218", "department_id": "07"}, {"id": "113", "patient_id": "64", "date": "2025-07-23", "medicines": [{"medicine_id": "56", "quantity": 9}], "prescribed_by": "Dr. <PERSON>", "notes": "Therapeutic medication for <PERSON>", "created_at": "2025-07-27T01:14:50.638575", "department_id": "05"}, {"id": "114", "patient_id": "61", "date": "2025-05-07", "medicines": [{"medicine_id": "01", "quantity": 5}, {"medicine_id": "73", "quantity": 7}], "prescribed_by": "Dr. <PERSON>", "notes": "Prescribed medication dispensed to <PERSON>", "created_at": "2025-07-27T01:14:50.638899", "department_id": "07"}, {"id": "115", "patient_id": "06", "date": "2025-04-29", "medicines": [{"medicine_id": "17", "quantity": 1}], "prescribed_by": "Dr. <PERSON>", "notes": "Recovery medication for <PERSON>", "created_at": "2025-07-27T01:14:50.639201", "department_id": "08"}, {"id": "116", "patient_id": "18", "date": "2025-06-15", "medicines": [{"medicine_id": "55", "quantity": 3}, {"medicine_id": "15", "quantity": 4}], "prescribed_by": "Dr. <PERSON>", "notes": "Emergency medication dispensed to <PERSON>", "created_at": "2025-07-27T01:14:50.639481", "department_id": "04"}, {"id": "117", "patient_id": "09", "date": "2025-06-03", "medicines": [{"medicine_id": "78", "quantity": 9}], "prescribed_by": "Dr. <PERSON>", "notes": "Follow-up medication for <PERSON>", "created_at": "2025-07-27T01:14:50.639771", "department_id": "06"}, {"id": "118", "patient_id": "12", "date": "2025-05-20", "medicines": [{"medicine_id": "24", "quantity": 3}, {"medicine_id": "36", "quantity": 4}, {"medicine_id": "91", "quantity": 5}], "prescribed_by": "Dr. <PERSON>", "notes": "Specialist-recommended medication for <PERSON>", "created_at": "2025-07-27T01:14:50.640136", "department_id": "10"}, {"id": "119", "patient_id": "58", "date": "2025-06-29", "medicines": [{"medicine_id": "09", "quantity": 2}, {"medicine_id": "01", "quantity": 1}, {"medicine_id": "82", "quantity": 10}], "prescribed_by": "Dr. <PERSON>", "notes": "Preventive medication for <PERSON>", "created_at": "2025-07-27T01:14:50.640421", "department_id": "07"}, {"id": "120", "patient_id": "28", "date": "2025-07-09", "medicines": [{"medicine_id": "06", "quantity": 7}, {"medicine_id": "50", "quantity": 7}], "prescribed_by": "Dr. <PERSON>", "notes": "Acute treatment medication for <PERSON>", "created_at": "2025-07-27T01:14:50.640883", "department_id": "07"}, {"id": "121", "patient_id": "32", "date": "2025-07-20", "medicines": [{"medicine_id": "102", "quantity": 6}, {"medicine_id": "34", "quantity": 3}], "prescribed_by": "Dr. <PERSON>", "notes": "Preventive medication for <PERSON>", "created_at": "2025-07-27T01:14:50.641107", "department_id": "07"}, {"id": "122", "patient_id": "61", "date": "2025-06-25", "medicines": [{"medicine_id": "56", "quantity": 6}], "prescribed_by": "Dr. <PERSON>", "notes": "Prescribed medication dispensed to <PERSON>", "created_at": "2025-07-27T01:14:50.641319", "department_id": "07"}, {"id": "123", "patient_id": "02", "date": "2025-07-26", "medicines": [{"medicine_id": "35", "quantity": 5}, {"medicine_id": "16", "quantity": 5}, {"medicine_id": "70", "quantity": 6}], "prescribed_by": "Dr. <PERSON>", "notes": "Prescribed medication dispensed to <PERSON>", "created_at": "2025-07-27T01:14:50.641596", "department_id": "02"}, {"id": "124", "patient_id": "06", "date": "2025-06-29", "medicines": [{"medicine_id": "03", "quantity": 9}], "prescribed_by": "Dr. <PERSON>", "notes": "Therapeutic medication for <PERSON>", "created_at": "2025-07-27T01:14:50.641896", "department_id": "08"}, {"id": "125", "patient_id": "25", "date": "2025-06-16", "medicines": [{"medicine_id": "69", "quantity": 6}, {"medicine_id": "86", "quantity": 4}, {"medicine_id": "90", "quantity": 7}], "prescribed_by": "Dr. <PERSON>", "notes": "Maintenance therapy for <PERSON>", "created_at": "2025-07-27T01:14:50.642169", "department_id": "05"}, {"id": "126", "patient_id": "15", "date": "2025-06-28", "medicines": [{"medicine_id": "87", "quantity": 5}], "prescribed_by": "Dr. <PERSON>", "notes": "Chronic condition management for <PERSON>", "created_at": "2025-07-27T01:14:50.642715", "department_id": "02"}, {"id": "127", "patient_id": "18", "date": "2025-05-19", "medicines": [{"medicine_id": "14", "quantity": 8}], "prescribed_by": "Dr. <PERSON>", "notes": "Emergency medication dispensed to <PERSON>", "created_at": "2025-07-27T01:14:50.643117", "department_id": "04"}, {"id": "128", "patient_id": "52", "date": "2025-05-17", "medicines": [{"medicine_id": "72", "quantity": 2}, {"medicine_id": "16", "quantity": 10}, {"medicine_id": "39", "quantity": 1}], "prescribed_by": "Dr. <PERSON>", "notes": "Recovery medication for <PERSON>", "created_at": "2025-07-27T01:14:50.643420", "department_id": "06"}, {"id": "129", "patient_id": "53", "date": "2025-06-17", "medicines": [{"medicine_id": "86", "quantity": 1}], "prescribed_by": "Dr. <PERSON>", "notes": "Chronic condition management for <PERSON>", "created_at": "2025-07-27T01:14:50.643658", "department_id": "05"}, {"id": "130", "patient_id": "26", "date": "2025-06-01", "medicines": [{"medicine_id": "88", "quantity": 6}], "prescribed_by": "Dr. <PERSON>", "notes": "Specialist-recommended medication for <PERSON>", "created_at": "2025-07-27T01:14:50.643862", "department_id": "05"}, {"id": "131", "patient_id": "55", "date": "2025-06-17", "medicines": [{"medicine_id": "92", "quantity": 10}], "prescribed_by": "Dr. <PERSON>", "notes": "Pain management medication for <PERSON>", "created_at": "2025-07-27T01:14:50.644061", "department_id": "06"}, {"id": "132", "patient_id": "47", "date": "2025-05-11", "medicines": [{"medicine_id": "56", "quantity": 9}], "prescribed_by": "Dr. <PERSON>", "notes": "Preventive medication for <PERSON>", "created_at": "2025-07-27T01:14:50.644457", "department_id": "05"}, {"id": "133", "patient_id": "16", "date": "2025-05-05", "medicines": [{"medicine_id": "24", "quantity": 7}], "prescribed_by": "Dr. <PERSON>", "notes": "Emergency medication dispensed to John Test Patient", "created_at": "2025-07-27T01:14:50.644658", "department_id": "05"}, {"id": "134", "patient_id": "29", "date": "2025-06-21", "medicines": [{"medicine_id": "65", "quantity": 10}], "prescribed_by": "Dr. <PERSON>", "notes": "Follow-up medication for <PERSON>", "created_at": "2025-07-27T01:14:50.644854", "department_id": "04"}, {"id": "135", "patient_id": "63", "date": "2025-06-05", "medicines": [{"medicine_id": "24", "quantity": 9}, {"medicine_id": "52", "quantity": 9}], "prescribed_by": "Dr. <PERSON>", "notes": "Acute treatment medication for <PERSON>", "created_at": "2025-07-27T01:14:50.645054", "department_id": "06"}, {"id": "136", "patient_id": "53", "date": "2025-05-22", "medicines": [{"medicine_id": "39", "quantity": 2}], "prescribed_by": "Dr. <PERSON>", "notes": "Symptom management medication for <PERSON>", "created_at": "2025-07-27T01:14:50.645252", "department_id": "05"}, {"id": "137", "patient_id": "33", "date": "2025-05-05", "medicines": [{"medicine_id": "65", "quantity": 6}], "prescribed_by": "Dr. <PERSON>", "notes": "Regular medication for <PERSON>", "created_at": "2025-07-27T01:14:50.645447", "department_id": "06"}, {"id": "138", "patient_id": "24", "date": "2025-05-25", "medicines": [{"medicine_id": "97", "quantity": 2}, {"medicine_id": "10", "quantity": 6}, {"medicine_id": "103", "quantity": 2}], "prescribed_by": "Dr. <PERSON>", "notes": "Preventive medication for <PERSON>", "created_at": "2025-07-27T01:14:50.645643", "department_id": "08"}, {"id": "139", "patient_id": "48", "date": "2025-06-17", "medicines": [{"medicine_id": "34", "quantity": 8}], "prescribed_by": "Dr. <PERSON>", "notes": "Therapeutic medication for <PERSON>", "created_at": "2025-07-27T01:14:50.645866", "department_id": "05"}, {"id": "140", "patient_id": "42", "date": "2025-06-28", "medicines": [{"medicine_id": "34", "quantity": 6}, {"medicine_id": "32", "quantity": 2}, {"medicine_id": "17", "quantity": 1}], "prescribed_by": "Dr. <PERSON>", "notes": "Post-operative medication for <PERSON>", "created_at": "2025-07-27T01:14:50.646066", "department_id": "06"}, {"id": "141", "patient_id": "24", "date": "2025-07-03", "medicines": [{"medicine_id": "65", "quantity": 5}, {"medicine_id": "97", "quantity": 5}], "prescribed_by": "Dr. <PERSON>", "notes": "Therapeutic medication for <PERSON>", "created_at": "2025-07-27T01:14:50.646289", "department_id": "08"}, {"id": "142", "patient_id": "35", "date": "2025-06-13", "medicines": [{"medicine_id": "30", "quantity": 9}, {"medicine_id": "14", "quantity": 9}], "prescribed_by": "Dr. <PERSON>", "notes": "Recovery medication for <PERSON>", "created_at": "2025-07-27T01:14:50.646493", "department_id": "04"}, {"id": "143", "patient_id": "54", "date": "2025-06-27", "medicines": [{"medicine_id": "39", "quantity": 1}, {"medicine_id": "82", "quantity": 9}], "prescribed_by": "Dr. <PERSON>", "notes": "Symptom management medication for <PERSON>", "created_at": "2025-07-27T01:14:50.646700", "department_id": "10"}, {"id": "144", "patient_id": "47", "date": "2025-06-13", "medicines": [{"medicine_id": "31", "quantity": 9}], "prescribed_by": "Dr. <PERSON>", "notes": "Acute treatment medication for <PERSON>", "created_at": "2025-07-27T01:14:50.646896", "department_id": "05"}, {"id": "145", "patient_id": "62", "date": "2025-07-24", "medicines": [{"medicine_id": "81", "quantity": 7}, {"medicine_id": "88", "quantity": 10}, {"medicine_id": "62", "quantity": 8}], "prescribed_by": "Dr. <PERSON>", "notes": "Prescribed medication dispensed to <PERSON>", "created_at": "2025-07-27T01:14:50.647102", "department_id": "05"}, {"id": "146", "patient_id": "39", "date": "2025-06-08", "medicines": [{"medicine_id": "02", "quantity": 10}], "prescribed_by": "Dr. <PERSON>", "notes": "Regular medication for <PERSON>", "created_at": "2025-07-27T01:14:50.647299", "department_id": "08"}, {"id": "147", "patient_id": "41", "date": "2025-05-26", "medicines": [{"medicine_id": "82", "quantity": 5}], "prescribed_by": "Dr. <PERSON>", "notes": "Acute treatment medication for <PERSON>", "created_at": "2025-07-27T01:14:50.647698", "department_id": "05"}, {"id": "148", "patient_id": "56", "date": "2025-05-15", "medicines": [{"medicine_id": "32", "quantity": 4}, {"medicine_id": "38", "quantity": 5}], "prescribed_by": "Dr. <PERSON>", "notes": "Symptom management medication for <PERSON>", "created_at": "2025-07-27T01:14:50.647897", "department_id": "06"}, {"id": "149", "patient_id": "17", "date": "2025-06-26", "medicines": [{"medicine_id": "74", "quantity": 6}], "prescribed_by": "Dr. <PERSON>", "notes": "Follow-up medication for <PERSON>", "created_at": "2025-07-27T01:14:50.648097", "department_id": "08"}, {"id": "150", "patient_id": "53", "date": "2025-06-25", "medicines": [{"medicine_id": "23", "quantity": 10}, {"medicine_id": "32", "quantity": 10}], "prescribed_by": "Dr. <PERSON>", "notes": "Specialist-recommended medication for <PERSON>", "created_at": "2025-07-27T01:14:50.648294", "department_id": "05"}, {"id": "151", "patient_id": "37", "date": "2025-06-27", "medicines": [{"medicine_id": "66", "quantity": 1}, {"medicine_id": "56", "quantity": 7}], "prescribed_by": "Dr. <PERSON>", "notes": "Recovery medication for <PERSON>", "created_at": "2025-07-27T01:14:50.648529", "department_id": "05"}, {"id": "152", "patient_id": "27", "date": "2025-07-06", "medicines": [{"medicine_id": "21", "quantity": 4}, {"medicine_id": "78", "quantity": 2}, {"medicine_id": "59", "quantity": 4}], "prescribed_by": "Dr. <PERSON>", "notes": "Chronic condition management for <PERSON>", "created_at": "2025-07-27T01:14:50.648742", "department_id": "02"}, {"id": "153", "patient_id": "60", "date": "2025-06-13", "medicines": [{"medicine_id": "95", "quantity": 2}], "prescribed_by": "Dr. <PERSON>", "notes": "Emergency medication dispensed to <PERSON>", "created_at": "2025-07-27T01:14:50.648947", "department_id": "04"}, {"id": "154", "patient_id": "17", "date": "2025-06-28", "medicines": [{"medicine_id": "70", "quantity": 4}, {"medicine_id": "47", "quantity": 5}, {"medicine_id": "35", "quantity": 2}], "prescribed_by": "Dr. <PERSON>", "notes": "Chronic condition management for <PERSON>", "created_at": "2025-07-27T01:14:50.649143", "department_id": "08"}, {"id": "155", "patient_id": "23", "date": "2025-07-07", "medicines": [{"medicine_id": "24", "quantity": 5}], "prescribed_by": "Dr. <PERSON>", "notes": "Emergency medication dispensed to <PERSON>", "created_at": "2025-07-27T01:14:50.649403", "department_id": "06"}, {"id": "156", "patient_id": "52", "date": "2025-06-05", "medicines": [{"medicine_id": "04", "quantity": 5}, {"medicine_id": "28", "quantity": 9}], "prescribed_by": "Dr. <PERSON>", "notes": "Preventive medication for <PERSON>", "created_at": "2025-07-27T01:14:50.649640", "department_id": "06"}, {"id": "157", "patient_id": "37", "date": "2025-06-30", "medicines": [{"medicine_id": "43", "quantity": 1}], "prescribed_by": "Dr. <PERSON>", "notes": "Routine prescription refill for <PERSON>", "created_at": "2025-07-27T01:14:50.649896", "department_id": "05"}, {"id": "158", "patient_id": "42", "date": "2025-07-11", "medicines": [{"medicine_id": "78", "quantity": 9}], "prescribed_by": "Dr. <PERSON>", "notes": "Prescribed medication dispensed to <PERSON>", "created_at": "2025-07-27T01:14:50.650092", "department_id": "06"}, {"id": "159", "patient_id": "11", "date": "2025-05-09", "medicines": [{"medicine_id": "37", "quantity": 9}, {"medicine_id": "48", "quantity": 7}], "prescribed_by": "Dr. <PERSON>", "notes": "Therapeutic medication for <PERSON>", "created_at": "2025-07-27T01:14:50.650289", "department_id": "06"}, {"id": "160", "patient_id": "13", "date": "2025-06-30", "medicines": [{"medicine_id": "58", "quantity": 4}, {"medicine_id": "97", "quantity": 7}, {"medicine_id": "16", "quantity": 10}], "prescribed_by": "Dr. <PERSON>", "notes": "Specialist-recommended medication for <PERSON>", "created_at": "2025-07-27T01:14:50.650690", "department_id": "06"}, {"id": "161", "patient_id": "26", "date": "2025-05-18", "medicines": [{"medicine_id": "70", "quantity": 9}, {"medicine_id": "30", "quantity": 4}], "prescribed_by": "Dr. <PERSON>", "notes": "Preventive medication for <PERSON>", "created_at": "2025-07-27T01:14:50.650977", "department_id": "05"}, {"id": "162", "patient_id": "02", "date": "2025-05-01", "medicines": [{"medicine_id": "96", "quantity": 9}], "prescribed_by": "Dr. <PERSON>", "notes": "Post-operative medication for <PERSON>", "created_at": "2025-07-27T01:14:50.651248", "department_id": "02"}, {"id": "163", "patient_id": "26", "date": "2025-05-21", "medicines": [{"medicine_id": "91", "quantity": 4}], "prescribed_by": "Dr. <PERSON>", "notes": "Emergency medication dispensed to <PERSON>", "created_at": "2025-07-27T01:14:50.651502", "department_id": "05"}, {"id": "164", "patient_id": "48", "date": "2025-06-06", "medicines": [{"medicine_id": "74", "quantity": 4}, {"medicine_id": "21", "quantity": 10}, {"medicine_id": "63", "quantity": 1}], "prescribed_by": "Dr. <PERSON>", "notes": "Pain management medication for <PERSON>", "created_at": "2025-07-27T01:14:50.651725", "department_id": "05"}, {"id": "165", "patient_id": "37", "date": "2025-06-26", "medicines": [{"medicine_id": "55", "quantity": 7}], "prescribed_by": "Dr. <PERSON>", "notes": "Specialist-recommended medication for <PERSON>", "created_at": "2025-07-27T01:14:50.652848", "department_id": "05"}, {"id": "166", "patient_id": "13", "date": "2025-05-04", "medicines": [{"medicine_id": "29", "quantity": 10}], "prescribed_by": "Dr. <PERSON>", "notes": "Symptom management medication for <PERSON>", "created_at": "2025-07-27T01:14:50.653099", "department_id": "06"}, {"id": "167", "patient_id": "39", "date": "2025-05-10", "medicines": [{"medicine_id": "88", "quantity": 2}, {"medicine_id": "53", "quantity": 1}], "prescribed_by": "Dr. <PERSON>", "notes": "Routine prescription refill for <PERSON>", "created_at": "2025-07-27T01:14:50.653445", "department_id": "08"}, {"id": "168", "patient_id": "15", "date": "2025-05-24", "medicines": [{"medicine_id": "55", "quantity": 6}, {"medicine_id": "14", "quantity": 3}, {"medicine_id": "74", "quantity": 3}], "prescribed_by": "Dr. <PERSON>", "notes": "Pain management medication for <PERSON>", "created_at": "2025-07-27T01:14:50.653779", "department_id": "02"}, {"id": "169", "patient_id": "58", "date": "2025-07-21", "medicines": [{"medicine_id": "25", "quantity": 4}, {"medicine_id": "53", "quantity": 5}, {"medicine_id": "39", "quantity": 5}], "prescribed_by": "Dr. <PERSON>", "notes": "Regular medication for <PERSON>", "created_at": "2025-07-27T01:14:50.654371", "department_id": "07"}, {"id": "170", "patient_id": "19", "date": "2025-05-31", "medicines": [{"medicine_id": "57", "quantity": 2}, {"medicine_id": "50", "quantity": 10}, {"medicine_id": "40", "quantity": 5}], "prescribed_by": "Dr. <PERSON>", "notes": "Chronic condition management for <PERSON>", "created_at": "2025-07-27T01:14:50.654585", "department_id": "10"}, {"id": "171", "patient_id": "49", "date": "2025-07-04", "medicines": [{"medicine_id": "54", "quantity": 3}, {"medicine_id": "36", "quantity": 8}], "prescribed_by": "Dr. <PERSON>", "notes": "Specialist-recommended medication for <PERSON>", "created_at": "2025-07-27T01:14:50.654809", "department_id": "07"}, {"id": "172", "patient_id": "09", "date": "2025-06-21", "medicines": [{"medicine_id": "34", "quantity": 8}, {"medicine_id": "07", "quantity": 1}], "prescribed_by": "Dr. <PERSON>", "notes": "Emergency medication dispensed to <PERSON>", "created_at": "2025-07-27T01:14:50.655014", "department_id": "06"}, {"id": "173", "patient_id": "52", "date": "2025-06-11", "medicines": [{"medicine_id": "101", "quantity": 9}, {"medicine_id": "94", "quantity": 6}, {"medicine_id": "50", "quantity": 1}], "prescribed_by": "Dr. <PERSON>", "notes": "Pain management medication for <PERSON>", "created_at": "2025-07-27T01:14:50.655217", "department_id": "06"}, {"id": "174", "patient_id": "35", "date": "2025-07-23", "medicines": [{"medicine_id": "12", "quantity": 2}], "prescribed_by": "Dr. <PERSON>", "notes": "Routine prescription refill for <PERSON>", "created_at": "2025-07-27T01:14:50.655611", "department_id": "04"}, {"id": "175", "patient_id": "23", "date": "2025-06-18", "medicines": [{"medicine_id": "71", "quantity": 2}, {"medicine_id": "02", "quantity": 1}, {"medicine_id": "37", "quantity": 5}], "prescribed_by": "Dr. <PERSON>", "notes": "Preventive medication for <PERSON>", "created_at": "2025-07-27T01:14:50.656223", "department_id": "06"}, {"id": "176", "patient_id": "24", "date": "2025-05-02", "medicines": [{"medicine_id": "02", "quantity": 5}], "prescribed_by": "Dr. <PERSON>", "notes": "Preventive medication for <PERSON>", "created_at": "2025-07-27T01:14:50.656643", "department_id": "08"}, {"id": "177", "patient_id": "56", "date": "2025-05-28", "medicines": [{"medicine_id": "21", "quantity": 1}, {"medicine_id": "45", "quantity": 1}, {"medicine_id": "50", "quantity": 3}], "prescribed_by": "Dr. <PERSON>", "notes": "Routine prescription refill for <PERSON>", "created_at": "2025-07-27T01:14:50.656849", "department_id": "06"}, {"id": "178", "patient_id": "63", "date": "2025-06-28", "medicines": [{"medicine_id": "58", "quantity": 8}, {"medicine_id": "73", "quantity": 3}], "prescribed_by": "Dr. <PERSON>", "notes": "Prescribed medication dispensed to <PERSON>", "created_at": "2025-07-27T01:14:50.657441", "department_id": "06"}, {"id": "179", "patient_id": "48", "date": "2025-07-08", "medicines": [{"medicine_id": "22", "quantity": 2}], "prescribed_by": "Dr. <PERSON>", "notes": "Therapeutic medication for <PERSON>", "created_at": "2025-07-27T01:14:50.657798", "department_id": "05"}, {"id": "180", "patient_id": "44", "date": "2025-04-28", "medicines": [{"medicine_id": "63", "quantity": 2}, {"medicine_id": "62", "quantity": 7}], "prescribed_by": "Dr. <PERSON>", "notes": "Post-operative medication for <PERSON>", "created_at": "2025-07-27T01:14:50.658151", "department_id": "10"}, {"id": "181", "patient_id": "61", "date": "2025-07-21", "medicines": [{"medicine_id": "50", "quantity": 9}, {"medicine_id": "76", "quantity": 7}, {"medicine_id": "29", "quantity": 4}], "prescribed_by": "Dr. <PERSON>", "notes": "Therapeutic medication for <PERSON>", "created_at": "2025-07-27T01:14:50.658466", "department_id": "07"}, {"id": "182", "patient_id": "43", "date": "2025-06-27", "medicines": [{"medicine_id": "03", "quantity": 10}, {"medicine_id": "37", "quantity": 4}, {"medicine_id": "35", "quantity": 4}], "prescribed_by": "Dr. <PERSON>", "notes": "Prescribed medication dispensed to <PERSON>", "created_at": "2025-07-27T01:14:50.658991", "department_id": "06"}, {"id": "183", "patient_id": "16", "date": "2025-06-23", "medicines": [{"medicine_id": "54", "quantity": 5}], "prescribed_by": "Dr. <PERSON>", "notes": "Routine prescription refill for John Test Patient", "created_at": "2025-07-27T01:14:50.659191", "department_id": "05"}, {"id": "184", "patient_id": "48", "date": "2025-07-15", "medicines": [{"medicine_id": "44", "quantity": 3}], "prescribed_by": "Dr. <PERSON>", "notes": "Routine prescription refill for <PERSON>", "created_at": "2025-07-27T01:14:50.659807", "department_id": "05"}, {"id": "185", "patient_id": "35", "date": "2025-06-25", "medicines": [{"medicine_id": "71", "quantity": 4}, {"medicine_id": "38", "quantity": 1}], "prescribed_by": "Dr. <PERSON>", "notes": "Acute treatment medication for <PERSON>", "created_at": "2025-07-27T01:14:50.660012", "department_id": "04"}, {"id": "186", "patient_id": "44", "date": "2025-06-18", "medicines": [{"medicine_id": "99", "quantity": 8}, {"medicine_id": "21", "quantity": 2}, {"medicine_id": "31", "quantity": 5}], "prescribed_by": "Dr. <PERSON>", "notes": "Regular medication for <PERSON>", "created_at": "2025-07-27T01:14:50.660211", "department_id": "10"}, {"id": "187", "patient_id": "48", "date": "2025-06-27", "medicines": [{"medicine_id": "60", "quantity": 9}, {"medicine_id": "89", "quantity": 7}, {"medicine_id": "13", "quantity": 8}], "prescribed_by": "Dr. <PERSON>", "notes": "Prescribed medication dispensed to <PERSON>", "created_at": "2025-07-27T01:14:50.660421", "department_id": "05"}, {"id": "188", "patient_id": "24", "date": "2025-05-26", "medicines": [{"medicine_id": "23", "quantity": 1}, {"medicine_id": "38", "quantity": 4}], "prescribed_by": "Dr. <PERSON>", "notes": "Symptom management medication for <PERSON>", "created_at": "2025-07-27T01:14:50.660617", "department_id": "08"}, {"id": "189", "patient_id": "66", "date": "2025-05-08", "medicines": [{"medicine_id": "23", "quantity": 5}, {"medicine_id": "100", "quantity": 10}], "prescribed_by": "Dr. <PERSON>", "notes": "Recovery medication for <PERSON>", "created_at": "2025-07-27T01:14:50.660824", "department_id": "04"}, {"id": "190", "patient_id": "23", "date": "2025-06-20", "medicines": [{"medicine_id": "35", "quantity": 4}, {"medicine_id": "52", "quantity": 1}], "prescribed_by": "Dr. <PERSON>", "notes": "Symptom management medication for <PERSON>", "created_at": "2025-07-27T01:14:50.661020", "department_id": "06"}, {"id": "191", "patient_id": "29", "date": "2025-06-12", "medicines": [{"medicine_id": "17", "quantity": 3}], "prescribed_by": "Dr. <PERSON>", "notes": "Therapeutic medication for <PERSON>", "created_at": "2025-07-27T01:14:50.661242", "department_id": "04"}, {"id": "192", "patient_id": "19", "date": "2025-06-10", "medicines": [{"medicine_id": "12", "quantity": 2}, {"medicine_id": "37", "quantity": 1}], "prescribed_by": "Dr. <PERSON>", "notes": "Recovery medication for <PERSON>", "created_at": "2025-07-27T01:14:50.661508", "department_id": "10"}, {"id": "193", "patient_id": "66", "date": "2025-07-19", "medicines": [{"medicine_id": "40", "quantity": 10}, {"medicine_id": "68", "quantity": 4}], "prescribed_by": "Dr. <PERSON>", "notes": "Maintenance therapy for <PERSON>", "created_at": "2025-07-27T01:14:50.661799", "department_id": "04"}, {"id": "194", "patient_id": "28", "date": "2025-06-22", "medicines": [{"medicine_id": "18", "quantity": 6}], "prescribed_by": "Dr. <PERSON>", "notes": "Regular medication for <PERSON>", "created_at": "2025-07-27T01:14:50.662073", "department_id": "07"}, {"id": "195", "patient_id": "13", "date": "2025-07-26", "medicines": [{"medicine_id": "23", "quantity": 10}, {"medicine_id": "43", "quantity": 1}, {"medicine_id": "37", "quantity": 8}], "prescribed_by": "Dr. <PERSON>", "notes": "Specialist-recommended medication for <PERSON>", "created_at": "2025-07-27T01:14:50.662686", "department_id": "06"}, {"id": "196", "patient_id": "24", "date": "2025-07-10", "medicines": [{"medicine_id": "23", "quantity": 10}], "prescribed_by": "Dr. <PERSON>", "notes": "Maintenance therapy for <PERSON>", "created_at": "2025-07-27T01:14:50.662892", "department_id": "08"}, {"id": "197", "patient_id": "44", "date": "2025-06-21", "medicines": [{"medicine_id": "23", "quantity": 3}, {"medicine_id": "03", "quantity": 5}, {"medicine_id": "30", "quantity": 9}], "prescribed_by": "Dr. <PERSON>", "notes": "Preventive medication for <PERSON>", "created_at": "2025-07-27T01:14:50.663094", "department_id": "10"}, {"id": "198", "patient_id": "58", "date": "2025-04-30", "medicines": [{"medicine_id": "77", "quantity": 5}], "prescribed_by": "Dr. <PERSON>", "notes": "Routine prescription refill for <PERSON>", "created_at": "2025-07-27T01:14:50.663303", "department_id": "07"}, {"id": "199", "patient_id": "09", "date": "2025-04-29", "medicines": [{"medicine_id": "31", "quantity": 9}, {"medicine_id": "78", "quantity": 5}], "prescribed_by": "Dr. <PERSON>", "notes": "Maintenance therapy for <PERSON>", "created_at": "2025-07-27T01:14:50.663500", "department_id": "06"}, {"id": "200", "patient_id": "38", "date": "2025-05-17", "medicines": [{"medicine_id": "52", "quantity": 6}, {"medicine_id": "69", "quantity": 1}, {"medicine_id": "73", "quantity": 9}], "prescribed_by": "Dr. <PERSON>", "notes": "Prescribed medication dispensed to <PERSON>", "created_at": "2025-07-27T01:14:50.663705", "department_id": "06"}, {"id": "201", "patient_id": "15", "date": "2025-06-24", "medicines": [{"medicine_id": "73", "quantity": 8}, {"medicine_id": "12", "quantity": 2}], "prescribed_by": "Dr. <PERSON>", "notes": "Recovery medication for <PERSON>", "created_at": "2025-07-27T01:14:50.663899", "department_id": "02"}, {"id": "202", "patient_id": "19", "date": "2025-05-15", "medicines": [{"medicine_id": "52", "quantity": 10}], "prescribed_by": "Dr. <PERSON>", "notes": "Emergency medication dispensed to <PERSON>", "created_at": "2025-07-27T01:14:50.664100", "department_id": "10"}, {"id": "203", "patient_id": "03", "date": "2025-06-19", "medicines": [{"medicine_id": "73", "quantity": 10}, {"medicine_id": "101", "quantity": 9}], "prescribed_by": "Dr. <PERSON>", "notes": "Symptom management medication for <PERSON>", "created_at": "2025-07-27T01:14:50.664296", "department_id": "06"}, {"id": "204", "patient_id": "28", "date": "2025-06-10", "medicines": [{"medicine_id": "19", "quantity": 8}, {"medicine_id": "100", "quantity": 10}, {"medicine_id": "10", "quantity": 9}], "prescribed_by": "Dr. <PERSON>", "notes": "Specialist-recommended medication for <PERSON>", "created_at": "2025-07-27T01:14:50.664500", "department_id": "07"}, {"id": "205", "patient_id": "03", "date": "2025-06-24", "medicines": [{"medicine_id": "74", "quantity": 7}, {"medicine_id": "98", "quantity": 7}, {"medicine_id": "63", "quantity": 8}], "prescribed_by": "Dr. <PERSON>", "notes": "Regular medication for <PERSON>", "created_at": "2025-07-27T01:14:50.664697", "department_id": "06"}, {"id": "206", "patient_id": "22", "date": "2025-07-17", "medicines": [{"medicine_id": "92", "quantity": 8}, {"medicine_id": "83", "quantity": 7}], "prescribed_by": "Dr. <PERSON>", "notes": "Post-operative medication for <PERSON>", "created_at": "2025-07-27T01:14:50.665100", "department_id": "05"}, {"id": "207", "patient_id": "60", "date": "2025-07-08", "medicines": [{"medicine_id": "29", "quantity": 10}], "prescribed_by": "Dr. <PERSON>", "notes": "Chronic condition management for <PERSON>", "created_at": "2025-07-27T01:14:50.665294", "department_id": "04"}, {"id": "208", "patient_id": "19", "date": "2025-07-17", "medicines": [{"medicine_id": "32", "quantity": 5}, {"medicine_id": "17", "quantity": 3}], "prescribed_by": "Dr. <PERSON>", "notes": "Pain management medication for <PERSON>", "created_at": "2025-07-27T01:14:50.665495", "department_id": "10"}, {"id": "209", "patient_id": "65", "date": "2025-07-01", "medicines": [{"medicine_id": "83", "quantity": 1}, {"medicine_id": "34", "quantity": 5}, {"medicine_id": "70", "quantity": 6}], "prescribed_by": "Dr. <PERSON>", "notes": "Routine prescription refill for <PERSON>", "created_at": "2025-07-27T01:14:50.665866", "department_id": "07"}, {"id": "210", "patient_id": "22", "date": "2025-05-17", "medicines": [{"medicine_id": "17", "quantity": 6}], "prescribed_by": "Dr. <PERSON>", "notes": "Preventive medication for <PERSON>", "created_at": "2025-07-27T01:14:50.666328", "department_id": "05"}, {"id": "211", "patient_id": "24", "date": "2025-05-04", "medicines": [{"medicine_id": "93", "quantity": 6}, {"medicine_id": "41", "quantity": 6}], "prescribed_by": "Dr. <PERSON>", "notes": "Therapeutic medication for <PERSON>", "created_at": "2025-07-27T01:14:50.666563", "department_id": "08"}, {"id": "212", "patient_id": "38", "date": "2025-04-28", "medicines": [{"medicine_id": "33", "quantity": 4}, {"medicine_id": "24", "quantity": 10}], "prescribed_by": "Dr. <PERSON>", "notes": "Preventive medication for <PERSON>", "created_at": "2025-07-27T01:14:50.666961", "department_id": "06"}, {"id": "213", "patient_id": "17", "date": "2025-05-05", "medicines": [{"medicine_id": "70", "quantity": 3}, {"medicine_id": "52", "quantity": 8}, {"medicine_id": "24", "quantity": 7}], "prescribed_by": "Dr. <PERSON>", "notes": "Prescribed medication dispensed to <PERSON>", "created_at": "2025-07-27T01:14:50.667156", "department_id": "08"}, {"id": "214", "patient_id": "47", "date": "2025-07-05", "medicines": [{"medicine_id": "13", "quantity": 10}], "prescribed_by": "Dr. <PERSON>", "notes": "Routine prescription refill for <PERSON>", "created_at": "2025-07-27T01:14:50.667357", "department_id": "05"}, {"id": "215", "patient_id": "13", "date": "2025-05-19", "medicines": [{"medicine_id": "05", "quantity": 2}], "prescribed_by": "Dr. <PERSON>", "notes": "Therapeutic medication for <PERSON>", "created_at": "2025-07-27T01:14:50.667620", "department_id": "06"}, {"id": "216", "patient_id": "04", "date": "2025-06-12", "medicines": [{"medicine_id": "100", "quantity": 1}, {"medicine_id": "43", "quantity": 3}], "prescribed_by": "Dr. <PERSON>", "notes": "Prescribed medication dispensed to <PERSON>", "created_at": "2025-07-27T01:14:50.667981", "department_id": "08"}, {"id": "217", "patient_id": "38", "date": "2025-05-12", "medicines": [{"medicine_id": "74", "quantity": 4}], "prescribed_by": "Dr. <PERSON>", "notes": "Pain management medication for <PERSON>", "created_at": "2025-07-27T01:14:50.668304", "department_id": "06"}, {"id": "218", "patient_id": "27", "date": "2025-05-19", "medicines": [{"medicine_id": "06", "quantity": 9}], "prescribed_by": "Dr. <PERSON>", "notes": "Specialist-recommended medication for <PERSON>", "created_at": "2025-07-27T01:14:50.668953", "department_id": "02"}, {"id": "219", "patient_id": "40", "date": "2025-05-26", "medicines": [{"medicine_id": "83", "quantity": 10}, {"medicine_id": "51", "quantity": 4}], "prescribed_by": "Dr. <PERSON>", "notes": "Therapeutic medication for <PERSON>", "created_at": "2025-07-27T01:14:50.669385", "department_id": "10"}, {"id": "220", "patient_id": "26", "date": "2025-07-16", "medicines": [{"medicine_id": "44", "quantity": 4}], "prescribed_by": "Dr. <PERSON>", "notes": "Emergency medication dispensed to <PERSON>", "created_at": "2025-07-27T01:14:50.669762", "department_id": "05"}, {"id": "221", "patient_id": "11", "date": "2025-05-11", "medicines": [{"medicine_id": "12", "quantity": 10}], "prescribed_by": "Dr. <PERSON>", "notes": "Symptom management medication for <PERSON>", "created_at": "2025-07-27T01:14:50.670111", "department_id": "06"}, {"id": "222", "patient_id": "45", "date": "2025-06-25", "medicines": [{"medicine_id": "93", "quantity": 4}, {"medicine_id": "09", "quantity": 8}], "prescribed_by": "Dr. <PERSON>", "notes": "Chronic condition management for <PERSON>", "created_at": "2025-07-27T01:14:50.670370", "department_id": "06"}, {"id": "223", "patient_id": "42", "date": "2025-05-11", "medicines": [{"medicine_id": "24", "quantity": 9}, {"medicine_id": "69", "quantity": 10}], "prescribed_by": "Dr. <PERSON>", "notes": "Preventive medication for <PERSON>", "created_at": "2025-07-27T01:14:50.670679", "department_id": "06"}, {"id": "224", "patient_id": "17", "date": "2025-06-29", "medicines": [{"medicine_id": "32", "quantity": 5}], "prescribed_by": "Dr. <PERSON>", "notes": "Recovery medication for <PERSON>", "created_at": "2025-07-27T01:14:50.671149", "department_id": "08"}, {"id": "225", "patient_id": "63", "date": "2025-05-16", "medicines": [{"medicine_id": "71", "quantity": 5}, {"medicine_id": "45", "quantity": 9}], "prescribed_by": "Dr. <PERSON>", "notes": "Recovery medication for <PERSON>", "created_at": "2025-07-27T01:14:50.671392", "department_id": "06"}, {"id": "226", "patient_id": "54", "date": "2025-07-22", "medicines": [{"medicine_id": "35", "quantity": 7}, {"medicine_id": "78", "quantity": 10}, {"medicine_id": "97", "quantity": 5}], "prescribed_by": "Dr. <PERSON>", "notes": "Acute treatment medication for <PERSON>", "created_at": "2025-07-27T01:14:50.671632", "department_id": "10"}, {"id": "227", "patient_id": "19", "date": "2025-07-17", "medicines": [{"medicine_id": "71", "quantity": 2}, {"medicine_id": "22", "quantity": 10}], "prescribed_by": "Dr. <PERSON>", "notes": "Follow-up medication for <PERSON>", "created_at": "2025-07-27T01:14:50.672072", "department_id": "10"}, {"id": "228", "patient_id": "43", "date": "2025-05-27", "medicines": [{"medicine_id": "74", "quantity": 4}, {"medicine_id": "96", "quantity": 2}], "prescribed_by": "Dr. <PERSON>", "notes": "Symptom management medication for <PERSON>", "created_at": "2025-07-27T01:14:50.672277", "department_id": "06"}, {"id": "229", "patient_id": "04", "date": "2025-05-01", "medicines": [{"medicine_id": "98", "quantity": 10}, {"medicine_id": "01", "quantity": 2}, {"medicine_id": "12", "quantity": 2}], "prescribed_by": "Dr. <PERSON>", "notes": "Maintenance therapy for <PERSON>", "created_at": "2025-07-27T01:14:50.672491", "department_id": "08"}, {"id": "230", "patient_id": "25", "date": "2025-05-13", "medicines": [{"medicine_id": "19", "quantity": 8}, {"medicine_id": "20", "quantity": 7}], "prescribed_by": "Dr. <PERSON>", "notes": "Specialist-recommended medication for <PERSON>", "created_at": "2025-07-27T01:14:50.672696", "department_id": "05"}, {"id": "231", "patient_id": "31", "date": "2025-05-09", "medicines": [{"medicine_id": "73", "quantity": 1}, {"medicine_id": "17", "quantity": 9}, {"medicine_id": "21", "quantity": 10}], "prescribed_by": "Dr. <PERSON>", "notes": "Therapeutic medication for <PERSON>", "created_at": "2025-07-27T01:14:50.672914", "department_id": "04"}, {"id": "232", "patient_id": "64", "date": "2025-04-30", "medicines": [{"medicine_id": "99", "quantity": 9}], "prescribed_by": "Dr. <PERSON>", "notes": "Therapeutic medication for <PERSON>", "created_at": "2025-07-27T01:14:50.673331", "department_id": "05"}, {"id": "233", "patient_id": "23", "date": "2025-06-06", "medicines": [{"medicine_id": "54", "quantity": 1}], "prescribed_by": "Dr. <PERSON>", "notes": "Follow-up medication for <PERSON>", "created_at": "2025-07-27T01:14:50.673898", "department_id": "06"}, {"id": "234", "patient_id": "40", "date": "2025-06-13", "medicines": [{"medicine_id": "98", "quantity": 7}, {"medicine_id": "01", "quantity": 2}], "prescribed_by": "Dr. <PERSON>", "notes": "Therapeutic medication for <PERSON>", "created_at": "2025-07-27T01:14:50.674152", "department_id": "10"}, {"id": "235", "patient_id": "19", "date": "2025-07-15", "medicines": [{"medicine_id": "66", "quantity": 5}, {"medicine_id": "56", "quantity": 2}, {"medicine_id": "12", "quantity": 10}], "prescribed_by": "Dr. <PERSON>", "notes": "Follow-up medication for <PERSON>", "created_at": "2025-07-27T01:14:50.674453", "department_id": "10"}, {"id": "236", "patient_id": "49", "date": "2025-05-29", "medicines": [{"medicine_id": "02", "quantity": 6}, {"medicine_id": "05", "quantity": 3}], "prescribed_by": "Dr. <PERSON>", "notes": "Post-operative medication for <PERSON>", "created_at": "2025-07-27T01:14:50.674797", "department_id": "07"}, {"id": "237", "patient_id": "48", "date": "2025-05-01", "medicines": [{"medicine_id": "39", "quantity": 4}], "prescribed_by": "Dr. <PERSON>", "notes": "Emergency medication dispensed to <PERSON>", "created_at": "2025-07-27T01:14:50.675487", "department_id": "05"}, {"id": "238", "patient_id": "04", "date": "2025-05-21", "medicines": [{"medicine_id": "78", "quantity": 9}, {"medicine_id": "98", "quantity": 4}, {"medicine_id": "59", "quantity": 3}], "prescribed_by": "Dr. <PERSON>", "notes": "Recovery medication for <PERSON>", "created_at": "2025-07-27T01:14:50.675802", "department_id": "08"}, {"id": "239", "patient_id": "66", "date": "2025-06-12", "medicines": [{"medicine_id": "21", "quantity": 9}, {"medicine_id": "35", "quantity": 10}], "prescribed_by": "Dr. <PERSON>", "notes": "Chronic condition management for <PERSON>", "created_at": "2025-07-27T01:14:50.676062", "department_id": "04"}, {"id": "240", "patient_id": "62", "date": "2025-06-05", "medicines": [{"medicine_id": "33", "quantity": 7}, {"medicine_id": "15", "quantity": 8}, {"medicine_id": "11", "quantity": 7}], "prescribed_by": "Dr. <PERSON>", "notes": "Specialist-recommended medication for <PERSON>", "created_at": "2025-07-27T01:14:50.676302", "department_id": "05"}, {"id": "241", "patient_id": "41", "date": "2025-05-08", "medicines": [{"medicine_id": "12", "quantity": 1}, {"medicine_id": "62", "quantity": 4}], "prescribed_by": "Dr. <PERSON>", "notes": "Therapeutic medication for <PERSON>", "created_at": "2025-07-27T01:14:50.676516", "department_id": "05"}, {"id": "242", "patient_id": "38", "date": "2025-06-17", "medicines": [{"medicine_id": "54", "quantity": 7}, {"medicine_id": "18", "quantity": 10}, {"medicine_id": "21", "quantity": 3}], "prescribed_by": "Dr. <PERSON>", "notes": "Regular medication for <PERSON>", "created_at": "2025-07-27T01:14:50.676740", "department_id": "06"}, {"id": "243", "patient_id": "12", "date": "2025-07-19", "medicines": [{"medicine_id": "68", "quantity": 4}], "prescribed_by": "Dr. <PERSON>", "notes": "Pain management medication for <PERSON>", "created_at": "2025-07-27T01:14:50.676943", "department_id": "10"}, {"id": "244", "patient_id": "35", "date": "2025-05-23", "medicines": [{"medicine_id": "16", "quantity": 6}], "prescribed_by": "Dr. <PERSON>", "notes": "Post-operative medication for <PERSON>", "created_at": "2025-07-27T01:14:50.677154", "department_id": "04"}, {"id": "245", "patient_id": "60", "date": "2025-05-21", "medicines": [{"medicine_id": "65", "quantity": 3}, {"medicine_id": "91", "quantity": 6}], "prescribed_by": "Dr. <PERSON>", "notes": "Emergency medication dispensed to <PERSON>", "created_at": "2025-07-27T01:14:50.677358", "department_id": "04"}, {"id": "246", "patient_id": "53", "date": "2025-07-10", "medicines": [{"medicine_id": "99", "quantity": 6}, {"medicine_id": "72", "quantity": 10}], "prescribed_by": "Dr. <PERSON>", "notes": "Chronic condition management for <PERSON>", "created_at": "2025-07-27T01:14:50.677568", "department_id": "05"}, {"id": "247", "patient_id": "65", "date": "2025-05-04", "medicines": [{"medicine_id": "90", "quantity": 2}], "prescribed_by": "Dr. <PERSON>", "notes": "Recovery medication for <PERSON>", "created_at": "2025-07-27T01:14:50.677773", "department_id": "07"}, {"id": "248", "patient_id": "47", "date": "2025-05-24", "medicines": [{"medicine_id": "82", "quantity": 9}, {"medicine_id": "34", "quantity": 8}], "prescribed_by": "Dr. <PERSON>", "notes": "Chronic condition management for <PERSON>", "created_at": "2025-07-27T01:14:50.677985", "department_id": "05"}, {"id": "249", "patient_id": "24", "date": "2025-04-30", "medicines": [{"medicine_id": "80", "quantity": 10}, {"medicine_id": "53", "quantity": 5}, {"medicine_id": "100", "quantity": 6}], "prescribed_by": "Dr. <PERSON>", "notes": "Symptom management medication for <PERSON>", "created_at": "2025-07-27T01:14:50.678186", "department_id": "08"}, {"id": "250", "patient_id": "28", "date": "2025-07-09", "medicines": [{"medicine_id": "18", "quantity": 3}], "prescribed_by": "Dr. <PERSON>", "notes": "Follow-up medication for <PERSON>", "created_at": "2025-07-27T01:14:50.678392", "department_id": "07"}, {"id": "251", "patient_id": "19", "date": "2025-06-06", "medicines": [{"medicine_id": "30", "quantity": 3}, {"medicine_id": "23", "quantity": 2}, {"medicine_id": "29", "quantity": 1}], "prescribed_by": "Dr. <PERSON>", "notes": "Follow-up medication for ongoing treatment", "department_id": "11", "created_at": "2025-07-27T03:17:09.471034", "updated_at": "2025-07-27T03:17:09.471038"}, {"id": "252", "patient_id": "01", "date": "2025-05-30", "medicines": [{"medicine_id": "16", "quantity": 4}, {"medicine_id": "21", "quantity": 10}], "prescribed_by": "Dr. <PERSON>", "notes": "Follow-up medication for ongoing treatment", "department_id": "08", "created_at": "2025-07-27T03:17:09.471137", "updated_at": "2025-07-27T03:17:09.471139"}, {"id": "253", "patient_id": "15", "date": "2025-07-05", "medicines": [{"medicine_id": "07", "quantity": 9}, {"medicine_id": "22", "quantity": 1}], "prescribed_by": "Dr. <PERSON>", "notes": "Follow-up medication for ongoing treatment", "department_id": "04", "created_at": "2025-07-27T03:17:09.471251", "updated_at": "2025-07-27T03:17:09.471254"}, {"id": "254", "patient_id": "24", "date": "2025-07-14", "medicines": [{"medicine_id": "12", "quantity": 5}], "prescribed_by": "Dr. <PERSON>", "notes": "Emergency medication administration", "department_id": "07", "created_at": "2025-07-27T03:17:09.471339", "updated_at": "2025-07-27T03:17:09.471341"}, {"id": "255", "patient_id": "22", "date": "2025-07-25", "medicines": [{"medicine_id": "21", "quantity": 9}], "prescribed_by": "Dr. <PERSON>", "notes": "Routine medication dispensing", "department_id": "11", "created_at": "2025-07-27T03:17:09.471429", "updated_at": "2025-07-27T03:17:09.471431"}, {"id": "256", "patient_id": "23", "date": "2025-06-30", "medicines": [{"medicine_id": "25", "quantity": 6}], "prescribed_by": "Dr. <PERSON>", "notes": "Prescribed medication as per treatment plan", "department_id": "12", "created_at": "2025-07-27T03:17:09.471519", "updated_at": "2025-07-27T03:17:09.471522"}, {"id": "257", "patient_id": "36", "date": "2025-06-11", "medicines": [{"medicine_id": "24", "quantity": 3}, {"medicine_id": "12", "quantity": 8}, {"medicine_id": "09", "quantity": 3}], "prescribed_by": "Dr. <PERSON>", "notes": "Prescribed medication as per treatment plan", "department_id": "16", "created_at": "2025-07-27T03:17:09.471626", "updated_at": "2025-07-27T03:17:09.471628"}, {"id": "258", "patient_id": "17", "date": "2025-06-14", "medicines": [{"medicine_id": "07", "quantity": 9}, {"medicine_id": "01", "quantity": 9}, {"medicine_id": "28", "quantity": 3}], "prescribed_by": "Dr. <PERSON>", "notes": "Specialized treatment medication", "department_id": "11", "created_at": "2025-07-27T03:17:09.471714", "updated_at": "2025-07-27T03:17:09.471717"}, {"id": "259", "patient_id": "50", "date": "2025-06-12", "medicines": [{"medicine_id": "17", "quantity": 7}, {"medicine_id": "30", "quantity": 9}, {"medicine_id": "05", "quantity": 1}], "prescribed_by": "Dr. <PERSON>", "notes": "Routine medication dispensing", "department_id": "04", "created_at": "2025-07-27T03:17:09.471815", "updated_at": "2025-07-27T03:17:09.471817"}, {"id": "260", "patient_id": "02", "date": "2025-06-02", "medicines": [{"medicine_id": "18", "quantity": 5}, {"medicine_id": "17", "quantity": 10}], "prescribed_by": "Dr. <PERSON>", "notes": "Emergency medication administration", "department_id": "11", "created_at": "2025-07-27T03:17:09.471902", "updated_at": "2025-07-27T03:17:09.471912"}, {"id": "261", "patient_id": "18", "date": "2025-07-06", "medicines": [{"medicine_id": "17", "quantity": 2}, {"medicine_id": "02", "quantity": 9}], "prescribed_by": "Dr. <PERSON>", "notes": "Specialized treatment medication", "department_id": "15", "created_at": "2025-07-27T03:17:09.472005", "updated_at": "2025-07-27T03:17:09.472008"}, {"id": "262", "patient_id": "06", "date": "2025-06-20", "medicines": [{"medicine_id": "25", "quantity": 4}, {"medicine_id": "16", "quantity": 4}], "prescribed_by": "Dr. <PERSON>", "notes": "Emergency medication administration", "department_id": "15", "created_at": "2025-07-27T03:17:09.472096", "updated_at": "2025-07-27T03:17:09.472098"}, {"id": "263", "patient_id": "13", "date": "2025-07-02", "medicines": [{"medicine_id": "07", "quantity": 10}], "prescribed_by": "Dr. <PERSON>", "notes": "Routine medication dispensing", "department_id": "13", "created_at": "2025-07-27T03:17:09.472171", "updated_at": "2025-07-27T03:17:09.472173"}, {"id": "264", "patient_id": "38", "date": "2025-06-17", "medicines": [{"medicine_id": "05", "quantity": 5}, {"medicine_id": "12", "quantity": 3}, {"medicine_id": "17", "quantity": 2}], "prescribed_by": "Dr. <PERSON>", "notes": "Prescribed medication as per treatment plan", "department_id": "05", "created_at": "2025-07-27T03:17:09.472264", "updated_at": "2025-07-27T03:17:09.472266"}, {"id": "265", "patient_id": "38", "date": "2025-07-05", "medicines": [{"medicine_id": "07", "quantity": 2}, {"medicine_id": "28", "quantity": 3}, {"medicine_id": "15", "quantity": 9}], "prescribed_by": "Dr. <PERSON>", "notes": "Emergency medication administration", "department_id": "14", "created_at": "2025-07-27T03:17:09.472358", "updated_at": "2025-07-27T03:17:09.472361"}, {"id": "266", "patient_id": "44", "date": "2025-06-18", "medicines": [{"medicine_id": "17", "quantity": 2}, {"medicine_id": "09", "quantity": 10}], "prescribed_by": "Dr. <PERSON>", "notes": "Routine medication dispensing", "department_id": "11", "created_at": "2025-07-27T03:17:09.472455", "updated_at": "2025-07-27T03:17:09.472457"}, {"id": "267", "patient_id": "04", "date": "2025-06-07", "medicines": [{"medicine_id": "24", "quantity": 3}, {"medicine_id": "05", "quantity": 7}], "prescribed_by": "Dr. <PERSON>", "notes": "Emergency medication administration", "department_id": "11", "created_at": "2025-07-27T03:17:09.472544", "updated_at": "2025-07-27T03:17:09.472546"}, {"id": "268", "patient_id": "29", "date": "2025-06-11", "medicines": [{"medicine_id": "02", "quantity": 3}, {"medicine_id": "27", "quantity": 7}], "prescribed_by": "Dr. <PERSON>", "notes": "Prescribed medication as per treatment plan", "department_id": "07", "created_at": "2025-07-27T03:17:09.472616", "updated_at": "2025-07-27T03:17:09.472618"}, {"id": "269", "patient_id": "28", "date": "2025-06-19", "medicines": [{"medicine_id": "24", "quantity": 5}, {"medicine_id": "18", "quantity": 7}], "prescribed_by": "Dr. <PERSON>", "notes": "Specialized treatment medication", "department_id": "09", "created_at": "2025-07-27T03:17:09.472697", "updated_at": "2025-07-27T03:17:09.472699"}, {"id": "270", "patient_id": "11", "date": "2025-06-27", "medicines": [{"medicine_id": "12", "quantity": 6}, {"medicine_id": "15", "quantity": 10}, {"medicine_id": "28", "quantity": 5}], "prescribed_by": "Dr. <PERSON>", "notes": "Regular medication for patient treatment", "department_id": "08", "created_at": "2025-07-27T03:17:09.472773", "updated_at": "2025-07-27T03:17:09.472775"}, {"id": "271", "patient_id": "06", "date": "2025-07-10", "medicines": [{"medicine_id": "04", "quantity": 10}, {"medicine_id": "05", "quantity": 6}], "prescribed_by": "Dr. <PERSON>", "notes": "Specialized treatment medication", "department_id": "10", "created_at": "2025-07-27T03:17:09.472851", "updated_at": "2025-07-27T03:17:09.472853"}, {"id": "272", "patient_id": "03", "date": "2025-07-26", "medicines": [{"medicine_id": "06", "quantity": 10}, {"medicine_id": "15", "quantity": 10}, {"medicine_id": "30", "quantity": 6}], "prescribed_by": "Dr. <PERSON>", "notes": "Regular medication for patient treatment", "department_id": "15", "created_at": "2025-07-27T03:17:09.472935", "updated_at": "2025-07-27T03:17:09.472937"}, {"id": "273", "patient_id": "28", "date": "2025-06-19", "medicines": [{"medicine_id": "02", "quantity": 9}], "prescribed_by": "Dr. <PERSON>", "notes": "Regular medication for patient treatment", "department_id": "05", "created_at": "2025-07-27T03:17:09.473014", "updated_at": "2025-07-27T03:17:09.473017"}, {"id": "274", "patient_id": "22", "date": "2025-07-27", "medicines": [{"medicine_id": "09", "quantity": 10}, {"medicine_id": "01", "quantity": 6}, {"medicine_id": "11", "quantity": 5}], "prescribed_by": "Dr. <PERSON>", "notes": "Routine medication dispensing", "department_id": "11", "created_at": "2025-07-27T03:17:09.473110", "updated_at": "2025-07-27T03:17:09.473112"}, {"id": "275", "patient_id": "06", "date": "2025-06-02", "medicines": [{"medicine_id": "11", "quantity": 6}, {"medicine_id": "26", "quantity": 9}, {"medicine_id": "24", "quantity": 9}], "prescribed_by": "Dr. <PERSON>", "notes": "Routine medication dispensing", "department_id": "16", "created_at": "2025-07-27T03:17:09.473201", "updated_at": "2025-07-27T03:17:09.473203"}, {"id": "276", "patient_id": "01", "date": "2025-06-08", "medicines": [{"medicine_id": "09", "quantity": 3}, {"medicine_id": "11", "quantity": 2}, {"medicine_id": "24", "quantity": 4}], "prescribed_by": "Dr. <PERSON>", "notes": "Follow-up medication for ongoing treatment", "department_id": "12", "created_at": "2025-07-27T03:17:09.473291", "updated_at": "2025-07-27T03:17:09.473293"}, {"id": "277", "patient_id": "20", "date": "2025-07-02", "medicines": [{"medicine_id": "04", "quantity": 1}, {"medicine_id": "14", "quantity": 9}, {"medicine_id": "08", "quantity": 2}], "prescribed_by": "Dr. <PERSON>", "notes": "Emergency medication administration", "department_id": "10", "created_at": "2025-07-27T03:17:09.473373", "updated_at": "2025-07-27T03:17:09.473375"}, {"id": "278", "patient_id": "32", "date": "2025-07-27", "medicines": [{"medicine_id": "06", "quantity": 5}], "prescribed_by": "Dr. <PERSON>", "notes": "Prescribed medication as per treatment plan", "department_id": "11", "created_at": "2025-07-27T03:17:09.473464", "updated_at": "2025-07-27T03:17:09.473467"}, {"id": "279", "patient_id": "41", "date": "2025-07-18", "medicines": [{"medicine_id": "15", "quantity": 10}, {"medicine_id": "11", "quantity": 6}], "prescribed_by": "Dr. <PERSON>", "notes": "Prescribed medication as per treatment plan", "department_id": "11", "created_at": "2025-07-27T03:17:09.473577", "updated_at": "2025-07-27T03:17:09.473580"}, {"id": "280", "patient_id": "45", "date": "2025-07-18", "medicines": [{"medicine_id": "04", "quantity": 3}], "prescribed_by": "Dr. <PERSON>", "notes": "Emergency medication administration", "department_id": "08", "created_at": "2025-07-27T03:17:09.473680", "updated_at": "2025-07-27T03:17:09.473683"}, {"id": "281", "patient_id": "37", "date": "2025-06-21", "medicines": [{"medicine_id": "19", "quantity": 7}], "prescribed_by": "Dr. <PERSON>", "notes": "Specialized treatment medication", "department_id": "11", "created_at": "2025-07-27T03:17:09.473774", "updated_at": "2025-07-27T03:17:09.473777"}, {"id": "282", "patient_id": "07", "date": "2025-06-20", "medicines": [{"medicine_id": "30", "quantity": 3}, {"medicine_id": "07", "quantity": 9}], "prescribed_by": "Dr. <PERSON>", "notes": "Follow-up medication for ongoing treatment", "department_id": "08", "created_at": "2025-07-27T03:17:09.473862", "updated_at": "2025-07-27T03:17:09.473864"}, {"id": "283", "patient_id": "24", "date": "2025-06-29", "medicines": [{"medicine_id": "11", "quantity": 7}], "prescribed_by": "Dr. <PERSON>", "notes": "Regular medication for patient treatment", "department_id": "12", "created_at": "2025-07-27T03:17:09.473972", "updated_at": "2025-07-27T03:17:09.473975"}, {"id": "284", "patient_id": "41", "date": "2025-07-01", "medicines": [{"medicine_id": "16", "quantity": 1}, {"medicine_id": "05", "quantity": 10}, {"medicine_id": "22", "quantity": 6}], "prescribed_by": "Dr. <PERSON>", "notes": "Specialized treatment medication", "department_id": "15", "created_at": "2025-07-27T03:17:09.474072", "updated_at": "2025-07-27T03:17:09.474075"}, {"id": "285", "patient_id": "01", "date": "2025-06-26", "medicines": [{"medicine_id": "30", "quantity": 7}], "prescribed_by": "Dr. <PERSON>", "notes": "Regular medication for patient treatment", "department_id": "15", "created_at": "2025-07-27T03:17:09.474158", "updated_at": "2025-07-27T03:17:09.474160"}, {"id": "286", "patient_id": "37", "date": "2025-06-09", "medicines": [{"medicine_id": "11", "quantity": 4}, {"medicine_id": "28", "quantity": 3}], "prescribed_by": "Dr. <PERSON>", "notes": "Follow-up medication for ongoing treatment", "department_id": "15", "created_at": "2025-07-27T03:17:09.474241", "updated_at": "2025-07-27T03:17:09.474243"}, {"id": "287", "patient_id": "33", "date": "2025-05-28", "medicines": [{"medicine_id": "10", "quantity": 9}, {"medicine_id": "18", "quantity": 10}, {"medicine_id": "09", "quantity": 6}], "prescribed_by": "Dr. <PERSON>", "notes": "Specialized treatment medication", "department_id": "13", "created_at": "2025-07-27T03:17:09.474325", "updated_at": "2025-07-27T03:17:09.474327"}, {"id": "288", "patient_id": "37", "date": "2025-06-05", "medicines": [{"medicine_id": "05", "quantity": 2}], "prescribed_by": "Dr. <PERSON>", "notes": "Specialized treatment medication", "department_id": "11", "created_at": "2025-07-27T03:17:09.474408", "updated_at": "2025-07-27T03:17:09.474410"}, {"id": "289", "patient_id": "46", "date": "2025-06-19", "medicines": [{"medicine_id": "03", "quantity": 1}, {"medicine_id": "13", "quantity": 9}, {"medicine_id": "24", "quantity": 7}], "prescribed_by": "Dr. <PERSON>", "notes": "Routine medication dispensing", "department_id": "09", "created_at": "2025-07-27T03:17:09.474492", "updated_at": "2025-07-27T03:17:09.474494"}, {"id": "290", "patient_id": "50", "date": "2025-07-26", "medicines": [{"medicine_id": "19", "quantity": 1}, {"medicine_id": "12", "quantity": 4}, {"medicine_id": "22", "quantity": 2}], "prescribed_by": "Dr. <PERSON>", "notes": "Prescribed medication as per treatment plan", "department_id": "16", "created_at": "2025-07-27T03:17:09.474573", "updated_at": "2025-07-27T03:17:09.474575"}, {"id": "291", "patient_id": "33", "date": "2025-07-08", "medicines": [{"medicine_id": "08", "quantity": 4}], "prescribed_by": "Dr. <PERSON>", "notes": "Emergency medication administration", "department_id": "11", "created_at": "2025-07-27T03:17:09.474668", "updated_at": "2025-07-27T03:17:09.474671"}, {"id": "292", "patient_id": "09", "date": "2025-06-03", "medicines": [{"medicine_id": "09", "quantity": 1}], "prescribed_by": "Dr. <PERSON>", "notes": "Follow-up medication for ongoing treatment", "department_id": "09", "created_at": "2025-07-27T03:17:09.474755", "updated_at": "2025-07-27T03:17:09.474757"}, {"id": "293", "patient_id": "13", "date": "2025-07-03", "medicines": [{"medicine_id": "20", "quantity": 10}, {"medicine_id": "02", "quantity": 7}, {"medicine_id": "15", "quantity": 10}], "prescribed_by": "Dr. <PERSON>", "notes": "Regular medication for patient treatment", "department_id": "11", "created_at": "2025-07-27T03:17:09.474845", "updated_at": "2025-07-27T03:17:09.474847"}, {"id": "294", "patient_id": "18", "date": "2025-06-29", "medicines": [{"medicine_id": "27", "quantity": 10}, {"medicine_id": "23", "quantity": 6}, {"medicine_id": "18", "quantity": 6}], "prescribed_by": "Dr. <PERSON>", "notes": "Specialized treatment medication", "department_id": "09", "created_at": "2025-07-27T03:17:09.474937", "updated_at": "2025-07-27T03:17:09.474939"}, {"id": "295", "patient_id": "35", "date": "2025-06-01", "medicines": [{"medicine_id": "30", "quantity": 10}], "prescribed_by": "Dr. <PERSON>", "notes": "Specialized treatment medication", "department_id": "09", "created_at": "2025-07-27T03:17:09.475020", "updated_at": "2025-07-27T03:17:09.475021"}, {"id": "296", "patient_id": "04", "date": "2025-06-07", "medicines": [{"medicine_id": "27", "quantity": 6}, {"medicine_id": "07", "quantity": 4}, {"medicine_id": "15", "quantity": 1}], "prescribed_by": "Dr. <PERSON>", "notes": "Emergency medication administration", "department_id": "11", "created_at": "2025-07-27T03:17:09.475112", "updated_at": "2025-07-27T03:17:09.475115"}, {"id": "297", "patient_id": "37", "date": "2025-07-20", "medicines": [{"medicine_id": "29", "quantity": 10}, {"medicine_id": "20", "quantity": 8}, {"medicine_id": "05", "quantity": 7}], "prescribed_by": "Dr. <PERSON>", "notes": "Routine medication dispensing", "department_id": "07", "created_at": "2025-07-27T03:17:09.475257", "updated_at": "2025-07-27T03:17:09.475260"}, {"id": "298", "patient_id": "04", "date": "2025-06-14", "medicines": [{"medicine_id": "07", "quantity": 4}, {"medicine_id": "06", "quantity": 6}, {"medicine_id": "15", "quantity": 9}], "prescribed_by": "Dr. <PERSON>", "notes": "Prescribed medication as per treatment plan", "department_id": "13", "created_at": "2025-07-27T03:17:09.475337", "updated_at": "2025-07-27T03:17:09.475340"}, {"id": "299", "patient_id": "12", "date": "2025-06-19", "medicines": [{"medicine_id": "11", "quantity": 8}, {"medicine_id": "03", "quantity": 6}], "prescribed_by": "Dr. <PERSON>", "notes": "Routine medication dispensing", "department_id": "11", "created_at": "2025-07-27T03:17:09.475426", "updated_at": "2025-07-27T03:17:09.475428"}, {"id": "300", "patient_id": "18", "date": "2025-07-20", "medicines": [{"medicine_id": "28", "quantity": 10}, {"medicine_id": "01", "quantity": 7}], "prescribed_by": "Dr. <PERSON>", "notes": "Specialized treatment medication", "department_id": "08", "created_at": "2025-07-27T03:17:09.475511", "updated_at": "2025-07-27T03:17:09.475514"}, {"id": "301", "patient_id": "31", "date": "2025-06-26", "medicines": [{"medicine_id": "10", "quantity": 8}, {"medicine_id": "28", "quantity": 6}, {"medicine_id": "24", "quantity": 7}], "prescribed_by": "Dr. <PERSON>", "notes": "Regular medication for patient treatment", "department_id": "15", "created_at": "2025-07-27T03:19:23.835677", "updated_at": "2025-07-27T03:19:23.835681"}, {"id": "302", "patient_id": "20", "date": "2025-06-09", "medicines": [{"medicine_id": "02", "quantity": 2}], "prescribed_by": "Dr. <PERSON>", "notes": "Regular medication for patient treatment", "department_id": "12", "created_at": "2025-07-27T03:19:23.835781", "updated_at": "2025-07-27T03:19:23.835783"}, {"id": "303", "patient_id": "18", "date": "2025-07-07", "medicines": [{"medicine_id": "20", "quantity": 2}], "prescribed_by": "Dr. <PERSON>", "notes": "Routine medication dispensing", "department_id": "15", "created_at": "2025-07-27T03:19:23.835886", "updated_at": "2025-07-27T03:19:23.835889"}, {"id": "304", "patient_id": "33", "date": "2025-07-15", "medicines": [{"medicine_id": "24", "quantity": 1}, {"medicine_id": "03", "quantity": 3}], "prescribed_by": "Dr. <PERSON>", "notes": "Prescribed medication as per treatment plan", "department_id": "13", "created_at": "2025-07-27T03:19:23.835988", "updated_at": "2025-07-27T03:19:23.835991"}, {"id": "305", "patient_id": "38", "date": "2025-07-23", "medicines": [{"medicine_id": "01", "quantity": 6}], "prescribed_by": "Dr. <PERSON>", "notes": "Specialized treatment medication", "department_id": "11", "created_at": "2025-07-27T03:19:23.836092", "updated_at": "2025-07-27T03:19:23.836094"}, {"id": "306", "patient_id": "24", "date": "2025-06-22", "medicines": [{"medicine_id": "13", "quantity": 2}, {"medicine_id": "23", "quantity": 10}, {"medicine_id": "04", "quantity": 9}], "prescribed_by": "Dr. <PERSON>", "notes": "Emergency medication administration", "department_id": "11", "created_at": "2025-07-27T03:19:23.836201", "updated_at": "2025-07-27T03:19:23.836203"}, {"id": "307", "patient_id": "44", "date": "2025-06-24", "medicines": [{"medicine_id": "07", "quantity": 6}, {"medicine_id": "18", "quantity": 2}, {"medicine_id": "24", "quantity": 2}], "prescribed_by": "Dr. <PERSON>", "notes": "Specialized treatment medication", "department_id": "09", "created_at": "2025-07-27T03:19:23.836295", "updated_at": "2025-07-27T03:19:23.836298"}, {"id": "308", "patient_id": "19", "date": "2025-06-24", "medicines": [{"medicine_id": "09", "quantity": 2}, {"medicine_id": "20", "quantity": 4}, {"medicine_id": "12", "quantity": 1}], "prescribed_by": "Dr. <PERSON>", "notes": "Specialized treatment medication", "department_id": "14", "created_at": "2025-07-27T03:19:23.836397", "updated_at": "2025-07-27T03:19:23.836400"}, {"id": "309", "patient_id": "44", "date": "2025-07-17", "medicines": [{"medicine_id": "03", "quantity": 1}], "prescribed_by": "Dr. <PERSON>", "notes": "Emergency medication administration", "department_id": "13", "created_at": "2025-07-27T03:19:23.836493", "updated_at": "2025-07-27T03:19:23.836495"}, {"id": "310", "patient_id": "09", "date": "2025-06-17", "medicines": [{"medicine_id": "21", "quantity": 3}], "prescribed_by": "Dr. <PERSON>", "notes": "Specialized treatment medication", "department_id": "16", "created_at": "2025-07-27T03:19:23.836590", "updated_at": "2025-07-27T03:19:23.836592"}, {"id": "311", "patient_id": "15", "date": "2025-07-24", "medicines": [{"medicine_id": "23", "quantity": 2}, {"medicine_id": "30", "quantity": 6}], "prescribed_by": "Dr. <PERSON>", "notes": "Routine medication dispensing", "department_id": "15", "created_at": "2025-07-27T03:19:23.836690", "updated_at": "2025-07-27T03:19:23.836692"}, {"id": "312", "patient_id": "19", "date": "2025-06-21", "medicines": [{"medicine_id": "26", "quantity": 3}, {"medicine_id": "10", "quantity": 2}], "prescribed_by": "Dr. <PERSON>", "notes": "Regular medication for patient treatment", "department_id": "07", "created_at": "2025-07-27T03:19:23.836787", "updated_at": "2025-07-27T03:19:23.836789"}, {"id": "313", "patient_id": "46", "date": "2025-06-16", "medicines": [{"medicine_id": "02", "quantity": 9}], "prescribed_by": "Dr. <PERSON>", "notes": "Regular medication for patient treatment", "department_id": "11", "created_at": "2025-07-27T03:19:23.836888", "updated_at": "2025-07-27T03:19:23.836890"}, {"id": "314", "patient_id": "06", "date": "2025-06-27", "medicines": [{"medicine_id": "27", "quantity": 2}], "prescribed_by": "Dr. <PERSON>", "notes": "Follow-up medication for ongoing treatment", "department_id": "09", "created_at": "2025-07-27T03:19:23.836980", "updated_at": "2025-07-27T03:19:23.836983"}, {"id": "315", "patient_id": "04", "date": "2025-07-19", "medicines": [{"medicine_id": "25", "quantity": 3}, {"medicine_id": "24", "quantity": 6}, {"medicine_id": "27", "quantity": 3}], "prescribed_by": "Dr. <PERSON>", "notes": "Emergency medication administration", "department_id": "11", "created_at": "2025-07-27T03:19:23.837091", "updated_at": "2025-07-27T03:19:23.837093"}, {"id": "316", "patient_id": "26", "date": "2025-06-27", "medicines": [{"medicine_id": "29", "quantity": 6}, {"medicine_id": "17", "quantity": 1}], "prescribed_by": "Dr. <PERSON>", "notes": "Regular medication for patient treatment", "department_id": "06", "created_at": "2025-07-27T03:19:23.837185", "updated_at": "2025-07-27T03:19:23.837187"}, {"id": "317", "patient_id": "36", "date": "2025-07-23", "medicines": [{"medicine_id": "22", "quantity": 9}, {"medicine_id": "17", "quantity": 10}], "prescribed_by": "Dr. <PERSON>", "notes": "Specialized treatment medication", "department_id": "10", "created_at": "2025-07-27T03:19:23.837276", "updated_at": "2025-07-27T03:19:23.837278"}, {"id": "318", "patient_id": "29", "date": "2025-07-11", "medicines": [{"medicine_id": "20", "quantity": 1}], "prescribed_by": "Dr. <PERSON>", "notes": "Follow-up medication for ongoing treatment", "department_id": "16", "created_at": "2025-07-27T03:19:23.837365", "updated_at": "2025-07-27T03:19:23.837367"}, {"id": "319", "patient_id": "17", "date": "2025-06-04", "medicines": [{"medicine_id": "23", "quantity": 9}, {"medicine_id": "19", "quantity": 9}, {"medicine_id": "01", "quantity": 2}], "prescribed_by": "Dr. <PERSON>", "notes": "Specialized treatment medication", "department_id": "04", "created_at": "2025-07-27T03:19:23.837465", "updated_at": "2025-07-27T03:19:23.837467"}, {"id": "320", "patient_id": "09", "date": "2025-06-23", "medicines": [{"medicine_id": "29", "quantity": 4}], "prescribed_by": "Dr. <PERSON>", "notes": "Prescribed medication as per treatment plan", "department_id": "16", "created_at": "2025-07-27T03:19:23.837565", "updated_at": "2025-07-27T03:19:23.837567"}, {"id": "321", "patient_id": "34", "date": "2025-07-24", "medicines": [{"medicine_id": "11", "quantity": 1}, {"medicine_id": "08", "quantity": 2}], "prescribed_by": "Dr. <PERSON>", "notes": "Routine medication dispensing", "department_id": "08", "created_at": "2025-07-27T03:19:23.837662", "updated_at": "2025-07-27T03:19:23.837664"}, {"id": "322", "patient_id": "19", "date": "2025-06-25", "medicines": [{"medicine_id": "12", "quantity": 8}, {"medicine_id": "23", "quantity": 7}, {"medicine_id": "01", "quantity": 1}], "prescribed_by": "Dr. <PERSON>", "notes": "Specialized treatment medication", "department_id": "12", "created_at": "2025-07-27T03:19:23.837754", "updated_at": "2025-07-27T03:19:23.837756"}, {"id": "323", "patient_id": "17", "date": "2025-07-25", "medicines": [{"medicine_id": "07", "quantity": 2}, {"medicine_id": "18", "quantity": 7}], "prescribed_by": "Dr. <PERSON>", "notes": "Specialized treatment medication", "department_id": "16", "created_at": "2025-07-27T03:19:23.837846", "updated_at": "2025-07-27T03:19:23.837848"}, {"id": "324", "patient_id": "24", "date": "2025-07-25", "medicines": [{"medicine_id": "26", "quantity": 5}, {"medicine_id": "16", "quantity": 7}, {"medicine_id": "18", "quantity": 9}], "prescribed_by": "Dr. <PERSON>", "notes": "Regular medication for patient treatment", "department_id": "15", "created_at": "2025-07-27T03:19:23.837939", "updated_at": "2025-07-27T03:19:23.837941"}, {"id": "325", "patient_id": "49", "date": "2025-06-25", "medicines": [{"medicine_id": "26", "quantity": 1}], "prescribed_by": "Dr. <PERSON>", "notes": "Regular medication for patient treatment", "department_id": "11", "created_at": "2025-07-27T03:19:23.838033", "updated_at": "2025-07-27T03:19:23.838035"}, {"id": "326", "patient_id": "16", "date": "2025-07-24", "medicines": [{"medicine_id": "16", "quantity": 6}, {"medicine_id": "18", "quantity": 4}, {"medicine_id": "08", "quantity": 6}], "prescribed_by": "Dr. <PERSON>", "notes": "Follow-up medication for ongoing treatment", "department_id": "11", "created_at": "2025-07-27T03:19:23.838125", "updated_at": "2025-07-27T03:19:23.838127"}, {"id": "327", "patient_id": "06", "date": "2025-06-10", "medicines": [{"medicine_id": "24", "quantity": 9}, {"medicine_id": "22", "quantity": 1}], "prescribed_by": "Dr. <PERSON>", "notes": "Specialized treatment medication", "department_id": "11", "created_at": "2025-07-27T03:19:23.838218", "updated_at": "2025-07-27T03:19:23.838220"}, {"id": "328", "patient_id": "46", "date": "2025-07-02", "medicines": [{"medicine_id": "26", "quantity": 8}], "prescribed_by": "Dr. <PERSON>", "notes": "Routine medication dispensing", "department_id": "12", "created_at": "2025-07-27T03:19:23.838308", "updated_at": "2025-07-27T03:19:23.838310"}, {"id": "329", "patient_id": "03", "date": "2025-07-07", "medicines": [{"medicine_id": "24", "quantity": 1}], "prescribed_by": "Dr. <PERSON>", "notes": "Emergency medication administration", "department_id": "15", "created_at": "2025-07-27T03:19:23.838407", "updated_at": "2025-07-27T03:19:23.838409"}, {"id": "330", "patient_id": "06", "date": "2025-06-16", "medicines": [{"medicine_id": "19", "quantity": 5}, {"medicine_id": "24", "quantity": 4}, {"medicine_id": "17", "quantity": 8}], "prescribed_by": "Dr. <PERSON>", "notes": "Prescribed medication as per treatment plan", "department_id": "08", "created_at": "2025-07-27T03:19:23.838519", "updated_at": "2025-07-27T03:19:23.838523"}, {"id": "331", "patient_id": "11", "date": "2025-06-12", "medicines": [{"medicine_id": "22", "quantity": 10}, {"medicine_id": "18", "quantity": 8}], "prescribed_by": "Dr. <PERSON>", "notes": "Specialized treatment medication", "department_id": "11", "created_at": "2025-07-27T03:19:23.838643", "updated_at": "2025-07-27T03:19:23.838646"}, {"id": "332", "patient_id": "14", "date": "2025-07-01", "medicines": [{"medicine_id": "16", "quantity": 9}, {"medicine_id": "19", "quantity": 9}], "prescribed_by": "Dr. <PERSON>", "notes": "Prescribed medication as per treatment plan", "department_id": "11", "created_at": "2025-07-27T03:19:23.838751", "updated_at": "2025-07-27T03:19:23.838754"}, {"id": "333", "patient_id": "40", "date": "2025-06-28", "medicines": [{"medicine_id": "15", "quantity": 10}], "prescribed_by": "Dr. <PERSON>", "notes": "Specialized treatment medication", "department_id": "14", "created_at": "2025-07-27T03:19:23.838851", "updated_at": "2025-07-27T03:19:23.838854"}, {"id": "334", "patient_id": "31", "date": "2025-06-21", "medicines": [{"medicine_id": "18", "quantity": 1}], "prescribed_by": "Dr. <PERSON>", "notes": "Prescribed medication as per treatment plan", "department_id": "07", "created_at": "2025-07-27T03:19:23.838965", "updated_at": "2025-07-27T03:19:23.838968"}, {"id": "335", "patient_id": "38", "date": "2025-06-29", "medicines": [{"medicine_id": "09", "quantity": 3}], "prescribed_by": "Dr. <PERSON>", "notes": "Follow-up medication for ongoing treatment", "department_id": "08", "created_at": "2025-07-27T03:19:23.839083", "updated_at": "2025-07-27T03:19:23.839086"}, {"id": "336", "patient_id": "35", "date": "2025-06-30", "medicines": [{"medicine_id": "18", "quantity": 10}, {"medicine_id": "22", "quantity": 3}, {"medicine_id": "16", "quantity": 3}], "prescribed_by": "Dr. <PERSON>", "notes": "Prescribed medication as per treatment plan", "department_id": "09", "created_at": "2025-07-27T03:19:23.839196", "updated_at": "2025-07-27T03:19:23.839198"}, {"id": "337", "patient_id": "42", "date": "2025-07-02", "medicines": [{"medicine_id": "12", "quantity": 9}, {"medicine_id": "27", "quantity": 9}], "prescribed_by": "Dr. <PERSON>", "notes": "Follow-up medication for ongoing treatment", "department_id": "13", "created_at": "2025-07-27T03:19:23.839305", "updated_at": "2025-07-27T03:19:23.839308"}, {"id": "338", "patient_id": "46", "date": "2025-07-23", "medicines": [{"medicine_id": "10", "quantity": 10}, {"medicine_id": "09", "quantity": 2}, {"medicine_id": "26", "quantity": 1}], "prescribed_by": "Dr. <PERSON>", "notes": "Regular medication for patient treatment", "department_id": "07", "created_at": "2025-07-27T03:19:23.839414", "updated_at": "2025-07-27T03:19:23.839417"}, {"id": "339", "patient_id": "29", "date": "2025-07-14", "medicines": [{"medicine_id": "19", "quantity": 3}, {"medicine_id": "05", "quantity": 3}, {"medicine_id": "13", "quantity": 9}], "prescribed_by": "Dr. <PERSON>", "notes": "Regular medication for patient treatment", "department_id": "11", "created_at": "2025-07-27T03:19:23.839521", "updated_at": "2025-07-27T03:19:23.839524"}, {"id": "340", "patient_id": "05", "date": "2025-06-23", "medicines": [{"medicine_id": "05", "quantity": 7}], "prescribed_by": "Dr. <PERSON>", "notes": "Emergency medication administration", "department_id": "05", "created_at": "2025-07-27T03:19:23.839628", "updated_at": "2025-07-27T03:19:23.839631"}, {"id": "341", "patient_id": "21", "date": "2025-06-20", "medicines": [{"medicine_id": "02", "quantity": 10}, {"medicine_id": "14", "quantity": 7}], "prescribed_by": "Dr. <PERSON>", "notes": "Routine medication dispensing", "department_id": "06", "created_at": "2025-07-27T03:19:23.839728", "updated_at": "2025-07-27T03:19:23.839730"}, {"id": "342", "patient_id": "06", "date": "2025-06-11", "medicines": [{"medicine_id": "22", "quantity": 8}, {"medicine_id": "01", "quantity": 8}, {"medicine_id": "06", "quantity": 2}], "prescribed_by": "Dr. <PERSON>", "notes": "Prescribed medication as per treatment plan", "department_id": "11", "created_at": "2025-07-27T03:19:23.839825", "updated_at": "2025-07-27T03:19:23.839828"}, {"id": "343", "patient_id": "19", "date": "2025-06-08", "medicines": [{"medicine_id": "28", "quantity": 5}], "prescribed_by": "Dr. <PERSON>", "notes": "Prescribed medication as per treatment plan", "department_id": "11", "created_at": "2025-07-27T03:19:23.839928", "updated_at": "2025-07-27T03:19:23.839931"}, {"id": "344", "patient_id": "15", "date": "2025-05-29", "medicines": [{"medicine_id": "08", "quantity": 9}, {"medicine_id": "30", "quantity": 5}, {"medicine_id": "12", "quantity": 1}], "prescribed_by": "Dr. <PERSON>", "notes": "Routine medication dispensing", "department_id": "06", "created_at": "2025-07-27T03:19:23.840030", "updated_at": "2025-07-27T03:19:23.840033"}, {"id": "345", "patient_id": "36", "date": "2025-06-15", "medicines": [{"medicine_id": "19", "quantity": 5}], "prescribed_by": "Dr. <PERSON>", "notes": "Follow-up medication for ongoing treatment", "department_id": "06", "created_at": "2025-07-27T03:19:23.840132", "updated_at": "2025-07-27T03:19:23.840135"}, {"id": "346", "patient_id": "37", "date": "2025-06-30", "medicines": [{"medicine_id": "25", "quantity": 1}, {"medicine_id": "09", "quantity": 6}], "prescribed_by": "Dr. <PERSON>", "notes": "Follow-up medication for ongoing treatment", "department_id": "06", "created_at": "2025-07-27T03:19:23.840235", "updated_at": "2025-07-27T03:19:23.840237"}, {"id": "347", "patient_id": "24", "date": "2025-06-21", "medicines": [{"medicine_id": "26", "quantity": 3}, {"medicine_id": "12", "quantity": 9}], "prescribed_by": "Dr. <PERSON>", "notes": "Routine medication dispensing", "department_id": "11", "created_at": "2025-07-27T03:19:23.840341", "updated_at": "2025-07-27T03:19:23.840343"}, {"id": "348", "patient_id": "47", "date": "2025-06-29", "medicines": [{"medicine_id": "08", "quantity": 8}, {"medicine_id": "29", "quantity": 2}], "prescribed_by": "Dr. <PERSON>", "notes": "Emergency medication administration", "department_id": "11", "created_at": "2025-07-27T03:19:23.840446", "updated_at": "2025-07-27T03:19:23.840449"}, {"id": "349", "patient_id": "14", "date": "2025-06-10", "medicines": [{"medicine_id": "30", "quantity": 2}, {"medicine_id": "26", "quantity": 5}], "prescribed_by": "Dr. <PERSON>", "notes": "Emergency medication administration", "department_id": "11", "created_at": "2025-07-27T03:19:23.840554", "updated_at": "2025-07-27T03:19:23.840557"}, {"id": "350", "patient_id": "40", "date": "2025-06-23", "medicines": [{"medicine_id": "03", "quantity": 6}, {"medicine_id": "26", "quantity": 3}], "prescribed_by": "Dr. <PERSON>", "notes": "Regular medication for patient treatment", "department_id": "08", "created_at": "2025-07-27T03:19:23.840666", "updated_at": "2025-07-27T03:19:23.840669"}, {"id": "351", "patient_id": "40", "date": "2025-07-14", "medicines": [{"medicine_id": "12", "quantity": 5}, {"medicine_id": "14", "quantity": 8}, {"medicine_id": "11", "quantity": 8}], "prescribed_by": "Dr. <PERSON>", "notes": "Follow-up medication for ongoing treatment", "department_id": "08", "created_at": "2025-07-27T03:19:40.537036", "updated_at": "2025-07-27T03:19:40.537040"}, {"id": "352", "patient_id": "37", "date": "2025-07-08", "medicines": [{"medicine_id": "28", "quantity": 3}], "prescribed_by": "Dr. <PERSON>", "notes": "Routine medication dispensing", "department_id": "04", "created_at": "2025-07-27T03:19:40.537140", "updated_at": "2025-07-27T03:19:40.537142"}, {"id": "353", "patient_id": "43", "date": "2025-07-22", "medicines": [{"medicine_id": "15", "quantity": 6}, {"medicine_id": "02", "quantity": 6}], "prescribed_by": "Dr. <PERSON>", "notes": "Prescribed medication as per treatment plan", "department_id": "06", "created_at": "2025-07-27T03:19:40.537249", "updated_at": "2025-07-27T03:19:40.537252"}, {"id": "354", "patient_id": "01", "date": "2025-06-14", "medicines": [{"medicine_id": "01", "quantity": 2}, {"medicine_id": "20", "quantity": 3}, {"medicine_id": "16", "quantity": 6}], "prescribed_by": "Dr. <PERSON>", "notes": "Regular medication for patient treatment", "department_id": "14", "created_at": "2025-07-27T03:19:40.537348", "updated_at": "2025-07-27T03:19:40.537350"}, {"id": "355", "patient_id": "30", "date": "2025-06-06", "medicines": [{"medicine_id": "27", "quantity": 7}, {"medicine_id": "15", "quantity": 2}], "prescribed_by": "Dr. <PERSON>", "notes": "Prescribed medication as per treatment plan", "department_id": "14", "created_at": "2025-07-27T03:19:40.537444", "updated_at": "2025-07-27T03:19:40.537446"}, {"id": "356", "patient_id": "36", "date": "2025-06-28", "medicines": [{"medicine_id": "01", "quantity": 8}, {"medicine_id": "04", "quantity": 1}, {"medicine_id": "17", "quantity": 6}], "prescribed_by": "Dr. <PERSON>", "notes": "Emergency medication administration", "department_id": "14", "created_at": "2025-07-27T03:19:40.537534", "updated_at": "2025-07-27T03:19:40.537536"}, {"id": "357", "patient_id": "26", "date": "2025-07-06", "medicines": [{"medicine_id": "04", "quantity": 3}, {"medicine_id": "26", "quantity": 5}], "prescribed_by": "Dr. <PERSON>", "notes": "Emergency medication administration", "department_id": "04", "created_at": "2025-07-27T03:19:40.537632", "updated_at": "2025-07-27T03:19:40.537634"}, {"id": "358", "patient_id": "36", "date": "2025-06-20", "medicines": [{"medicine_id": "15", "quantity": 8}, {"medicine_id": "20", "quantity": 7}, {"medicine_id": "28", "quantity": 2}], "prescribed_by": "Dr. <PERSON>", "notes": "Regular medication for patient treatment", "department_id": "10", "created_at": "2025-07-27T03:19:40.537722", "updated_at": "2025-07-27T03:19:40.537725"}, {"id": "359", "patient_id": "45", "date": "2025-07-16", "medicines": [{"medicine_id": "05", "quantity": 1}], "prescribed_by": "Dr. <PERSON>", "notes": "Prescribed medication as per treatment plan", "department_id": "05", "created_at": "2025-07-27T03:19:40.537826", "updated_at": "2025-07-27T03:19:40.537828"}, {"id": "360", "patient_id": "40", "date": "2025-07-06", "medicines": [{"medicine_id": "22", "quantity": 5}, {"medicine_id": "08", "quantity": 4}], "prescribed_by": "Dr. <PERSON>", "notes": "Emergency medication administration", "department_id": "11", "created_at": "2025-07-27T03:19:40.537921", "updated_at": "2025-07-27T03:19:40.537923"}, {"id": "361", "patient_id": "36", "date": "2025-07-11", "medicines": [{"medicine_id": "06", "quantity": 2}, {"medicine_id": "27", "quantity": 2}], "prescribed_by": "Dr. <PERSON>", "notes": "Follow-up medication for ongoing treatment", "department_id": "11", "created_at": "2025-07-27T03:19:40.538014", "updated_at": "2025-07-27T03:19:40.538017"}, {"id": "362", "patient_id": "07", "date": "2025-06-30", "medicines": [{"medicine_id": "21", "quantity": 9}], "prescribed_by": "Dr. <PERSON>", "notes": "Regular medication for patient treatment", "department_id": "16", "created_at": "2025-07-27T03:19:40.538124", "updated_at": "2025-07-27T03:19:40.538126"}, {"id": "363", "patient_id": "20", "date": "2025-06-26", "medicines": [{"medicine_id": "18", "quantity": 10}], "prescribed_by": "Dr. <PERSON>", "notes": "Follow-up medication for ongoing treatment", "department_id": "11", "created_at": "2025-07-27T03:19:40.538223", "updated_at": "2025-07-27T03:19:40.538226"}, {"id": "364", "patient_id": "38", "date": "2025-07-01", "medicines": [{"medicine_id": "23", "quantity": 5}, {"medicine_id": "07", "quantity": 1}], "prescribed_by": "Dr. <PERSON>", "notes": "Regular medication for patient treatment", "department_id": "11", "created_at": "2025-07-27T03:19:40.538313", "updated_at": "2025-07-27T03:19:40.538315"}, {"id": "365", "patient_id": "16", "date": "2025-07-25", "medicines": [{"medicine_id": "22", "quantity": 7}], "prescribed_by": "Dr. <PERSON>", "notes": "Follow-up medication for ongoing treatment", "department_id": "13", "created_at": "2025-07-27T03:19:40.538407", "updated_at": "2025-07-27T03:19:40.538409"}, {"id": "366", "patient_id": "48", "date": "2025-07-22", "medicines": [{"medicine_id": "01", "quantity": 3}], "prescribed_by": "Dr. <PERSON>", "notes": "Routine medication dispensing", "department_id": "04", "created_at": "2025-07-27T03:19:40.538511", "updated_at": "2025-07-27T03:19:40.538514"}, {"id": "367", "patient_id": "21", "date": "2025-07-04", "medicines": [{"medicine_id": "10", "quantity": 7}, {"medicine_id": "08", "quantity": 3}, {"medicine_id": "14", "quantity": 10}], "prescribed_by": "Dr. <PERSON>", "notes": "Regular medication for patient treatment", "department_id": "16", "created_at": "2025-07-27T03:19:40.538638", "updated_at": "2025-07-27T03:19:40.538642"}, {"id": "368", "patient_id": "47", "date": "2025-06-21", "medicines": [{"medicine_id": "22", "quantity": 10}, {"medicine_id": "23", "quantity": 8}], "prescribed_by": "Dr. <PERSON>", "notes": "Specialized treatment medication", "department_id": "11", "created_at": "2025-07-27T03:19:40.538750", "updated_at": "2025-07-27T03:19:40.538754"}, {"id": "369", "patient_id": "34", "date": "2025-06-08", "medicines": [{"medicine_id": "09", "quantity": 4}, {"medicine_id": "16", "quantity": 7}], "prescribed_by": "Dr. <PERSON>", "notes": "Specialized treatment medication", "department_id": "05", "created_at": "2025-07-27T03:19:40.538849", "updated_at": "2025-07-27T03:19:40.538852"}, {"id": "370", "patient_id": "01", "date": "2025-07-10", "medicines": [{"medicine_id": "08", "quantity": 1}], "prescribed_by": "Dr. <PERSON>", "notes": "Prescribed medication as per treatment plan", "department_id": "11", "created_at": "2025-07-27T03:19:40.538974", "updated_at": "2025-07-27T03:19:40.538977"}, {"id": "371", "patient_id": "15", "date": "2025-07-06", "medicines": [{"medicine_id": "03", "quantity": 1}, {"medicine_id": "06", "quantity": 5}, {"medicine_id": "28", "quantity": 2}], "prescribed_by": "Dr. <PERSON>", "notes": "Routine medication dispensing", "department_id": "09", "created_at": "2025-07-27T03:19:40.539094", "updated_at": "2025-07-27T03:19:40.539097"}, {"id": "372", "patient_id": "32", "date": "2025-06-18", "medicines": [{"medicine_id": "03", "quantity": 5}, {"medicine_id": "25", "quantity": 4}], "prescribed_by": "Dr. <PERSON>", "notes": "Routine medication dispensing", "department_id": "04", "created_at": "2025-07-27T03:19:40.539183", "updated_at": "2025-07-27T03:19:40.539186"}, {"id": "373", "patient_id": "01", "date": "2025-06-22", "medicines": [{"medicine_id": "08", "quantity": 3}], "prescribed_by": "Dr. <PERSON>", "notes": "Prescribed medication as per treatment plan", "department_id": "15", "created_at": "2025-07-27T03:19:40.539284", "updated_at": "2025-07-27T03:19:40.539287"}, {"id": "374", "patient_id": "47", "date": "2025-06-25", "medicines": [{"medicine_id": "04", "quantity": 5}], "prescribed_by": "Dr. <PERSON>", "notes": "Emergency medication administration", "department_id": "04", "created_at": "2025-07-27T03:19:40.539382", "updated_at": "2025-07-27T03:19:40.539384"}, {"id": "375", "patient_id": "38", "date": "2025-06-16", "medicines": [{"medicine_id": "23", "quantity": 2}], "prescribed_by": "Dr. <PERSON>", "notes": "Regular medication for patient treatment", "department_id": "15", "created_at": "2025-07-27T03:19:40.539473", "updated_at": "2025-07-27T03:19:40.539475"}, {"id": "376", "patient_id": "45", "date": "2025-07-08", "medicines": [{"medicine_id": "24", "quantity": 5}, {"medicine_id": "25", "quantity": 9}, {"medicine_id": "21", "quantity": 8}], "prescribed_by": "Dr. <PERSON>", "notes": "Follow-up medication for ongoing treatment", "department_id": "09", "created_at": "2025-07-27T03:19:40.539582", "updated_at": "2025-07-27T03:19:40.539585"}, {"id": "377", "patient_id": "33", "date": "2025-06-02", "medicines": [{"medicine_id": "10", "quantity": 2}], "prescribed_by": "Dr. <PERSON>", "notes": "Follow-up medication for ongoing treatment", "department_id": "11", "created_at": "2025-07-27T03:19:40.539689", "updated_at": "2025-07-27T03:19:40.539692"}, {"id": "378", "patient_id": "45", "date": "2025-07-18", "medicines": [{"medicine_id": "30", "quantity": 9}], "prescribed_by": "Dr. <PERSON>", "notes": "Prescribed medication as per treatment plan", "department_id": "11", "created_at": "2025-07-27T03:19:40.539805", "updated_at": "2025-07-27T03:19:40.539808"}, {"id": "379", "patient_id": "05", "date": "2025-07-12", "medicines": [{"medicine_id": "30", "quantity": 5}, {"medicine_id": "09", "quantity": 10}], "prescribed_by": "Dr. <PERSON>", "notes": "Routine medication dispensing", "department_id": "10", "created_at": "2025-07-27T03:19:40.539937", "updated_at": "2025-07-27T03:19:40.539940"}, {"id": "380", "patient_id": "23", "date": "2025-06-29", "medicines": [{"medicine_id": "18", "quantity": 7}, {"medicine_id": "02", "quantity": 6}, {"medicine_id": "24", "quantity": 9}], "prescribed_by": "Dr. <PERSON>", "notes": "Routine medication dispensing", "department_id": "12", "created_at": "2025-07-27T03:19:40.540049", "updated_at": "2025-07-27T03:19:40.540052"}, {"id": "381", "patient_id": "17", "date": "2025-06-23", "medicines": [{"medicine_id": "14", "quantity": 9}], "prescribed_by": "Dr. <PERSON>", "notes": "Prescribed medication as per treatment plan", "department_id": "11", "created_at": "2025-07-27T03:19:40.540165", "updated_at": "2025-07-27T03:19:40.540168"}, {"id": "382", "patient_id": "02", "date": "2025-06-28", "medicines": [{"medicine_id": "08", "quantity": 6}, {"medicine_id": "06", "quantity": 6}], "prescribed_by": "Dr. <PERSON>", "notes": "Routine medication dispensing", "department_id": "10", "created_at": "2025-07-27T03:19:40.540280", "updated_at": "2025-07-27T03:19:40.540283"}, {"id": "383", "patient_id": "32", "date": "2025-07-07", "medicines": [{"medicine_id": "19", "quantity": 9}], "prescribed_by": "Dr. <PERSON>", "notes": "Routine medication dispensing", "department_id": "07", "created_at": "2025-07-27T03:19:40.540380", "updated_at": "2025-07-27T03:19:40.540382"}, {"id": "384", "patient_id": "15", "date": "2025-06-20", "medicines": [{"medicine_id": "21", "quantity": 10}], "prescribed_by": "Dr. <PERSON>", "notes": "Specialized treatment medication", "department_id": "09", "created_at": "2025-07-27T03:19:40.540474", "updated_at": "2025-07-27T03:19:40.540476"}, {"id": "385", "patient_id": "32", "date": "2025-07-11", "medicines": [{"medicine_id": "09", "quantity": 4}, {"medicine_id": "27", "quantity": 4}], "prescribed_by": "Dr. <PERSON>", "notes": "Specialized treatment medication", "department_id": "14", "created_at": "2025-07-27T03:19:40.540569", "updated_at": "2025-07-27T03:19:40.540572"}, {"id": "386", "patient_id": "48", "date": "2025-07-21", "medicines": [{"medicine_id": "14", "quantity": 1}, {"medicine_id": "07", "quantity": 5}, {"medicine_id": "04", "quantity": 10}], "prescribed_by": "Dr. <PERSON>", "notes": "Follow-up medication for ongoing treatment", "department_id": "11", "created_at": "2025-07-27T03:19:40.540671", "updated_at": "2025-07-27T03:19:40.540673"}, {"id": "387", "patient_id": "37", "date": "2025-07-18", "medicines": [{"medicine_id": "23", "quantity": 7}], "prescribed_by": "Dr. <PERSON>", "notes": "Specialized treatment medication", "department_id": "08", "created_at": "2025-07-27T03:19:40.540769", "updated_at": "2025-07-27T03:19:40.540771"}, {"id": "388", "patient_id": "03", "date": "2025-05-29", "medicines": [{"medicine_id": "20", "quantity": 3}, {"medicine_id": "27", "quantity": 8}], "prescribed_by": "Dr. <PERSON>", "notes": "Specialized treatment medication", "department_id": "09", "created_at": "2025-07-27T03:19:40.540860", "updated_at": "2025-07-27T03:19:40.540863"}, {"id": "389", "patient_id": "28", "date": "2025-07-21", "medicines": [{"medicine_id": "28", "quantity": 7}, {"medicine_id": "17", "quantity": 5}], "prescribed_by": "Dr. <PERSON>", "notes": "Routine medication dispensing", "department_id": "12", "created_at": "2025-07-27T03:19:40.540957", "updated_at": "2025-07-27T03:19:40.540959"}, {"id": "390", "patient_id": "46", "date": "2025-07-02", "medicines": [{"medicine_id": "06", "quantity": 9}, {"medicine_id": "14", "quantity": 4}], "prescribed_by": "Dr. <PERSON>", "notes": "Prescribed medication as per treatment plan", "department_id": "16", "created_at": "2025-07-27T03:19:40.541051", "updated_at": "2025-07-27T03:19:40.541053"}, {"id": "391", "patient_id": "21", "date": "2025-06-01", "medicines": [{"medicine_id": "16", "quantity": 2}], "prescribed_by": "Dr. <PERSON>", "notes": "Regular medication for patient treatment", "department_id": "04", "created_at": "2025-07-27T03:19:40.541147", "updated_at": "2025-07-27T03:19:40.541150"}, {"id": "392", "patient_id": "10", "date": "2025-06-13", "medicines": [{"medicine_id": "27", "quantity": 5}, {"medicine_id": "25", "quantity": 10}], "prescribed_by": "Dr. <PERSON>", "notes": "Routine medication dispensing", "department_id": "07", "created_at": "2025-07-27T03:19:40.541240", "updated_at": "2025-07-27T03:19:40.541242"}, {"id": "393", "patient_id": "22", "date": "2025-06-26", "medicines": [{"medicine_id": "04", "quantity": 5}], "prescribed_by": "Dr. <PERSON>", "notes": "Specialized treatment medication", "department_id": "12", "created_at": "2025-07-27T03:19:40.541350", "updated_at": "2025-07-27T03:19:40.541353"}, {"id": "394", "patient_id": "34", "date": "2025-06-08", "medicines": [{"medicine_id": "20", "quantity": 4}], "prescribed_by": "Dr. <PERSON>", "notes": "Specialized treatment medication", "department_id": "06", "created_at": "2025-07-27T03:19:40.541441", "updated_at": "2025-07-27T03:19:40.541444"}, {"id": "395", "patient_id": "06", "date": "2025-06-07", "medicines": [{"medicine_id": "04", "quantity": 5}], "prescribed_by": "Dr. <PERSON>", "notes": "Prescribed medication as per treatment plan", "department_id": "13", "created_at": "2025-07-27T03:19:40.541609", "updated_at": "2025-07-27T03:19:40.541612"}, {"id": "396", "patient_id": "31", "date": "2025-06-15", "medicines": [{"medicine_id": "24", "quantity": 5}], "prescribed_by": "Dr. <PERSON>", "notes": "Prescribed medication as per treatment plan", "department_id": "06", "created_at": "2025-07-27T03:19:40.541727", "updated_at": "2025-07-27T03:19:40.541730"}, {"id": "397", "patient_id": "06", "date": "2025-07-18", "medicines": [{"medicine_id": "22", "quantity": 10}], "prescribed_by": "Dr. <PERSON>", "notes": "Specialized treatment medication", "department_id": "08", "created_at": "2025-07-27T03:19:40.541834", "updated_at": "2025-07-27T03:19:40.541836"}, {"id": "398", "patient_id": "09", "date": "2025-06-19", "medicines": [{"medicine_id": "06", "quantity": 9}, {"medicine_id": "24", "quantity": 9}], "prescribed_by": "Dr. <PERSON>", "notes": "Follow-up medication for ongoing treatment", "department_id": "12", "created_at": "2025-07-27T03:19:40.541936", "updated_at": "2025-07-27T03:19:40.541939"}, {"id": "399", "patient_id": "09", "date": "2025-06-29", "medicines": [{"medicine_id": "04", "quantity": 4}], "prescribed_by": "Dr. <PERSON>", "notes": "Routine medication dispensing", "department_id": "11", "created_at": "2025-07-27T03:19:40.542043", "updated_at": "2025-07-27T03:19:40.542045"}, {"id": "400", "patient_id": "30", "date": "2025-06-29", "medicines": [{"medicine_id": "18", "quantity": 5}, {"medicine_id": "03", "quantity": 6}, {"medicine_id": "15", "quantity": 2}], "prescribed_by": "Dr. <PERSON>", "notes": "Routine medication dispensing", "department_id": "08", "created_at": "2025-07-27T03:19:40.542151", "updated_at": "2025-07-27T03:19:40.542153"}]