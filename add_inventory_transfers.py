#!/usr/bin/env python3
"""
Add 15 inventory transfer records from main pharmacy to department stores
Target departments: Cardiology, Oncology, Pediatrics, Surgery, Neurology, Psychiatry
"""

import json
import random
from datetime import datetime, timedelta

def load_json_file(filename):
    """Load JSON data from file"""
    try:
        with open(filename, 'r', encoding='utf-8') as f:
            return json.load(f)
    except FileNotFoundError:
        return []

def save_json_file(filename, data):
    """Save JSON data to file"""
    with open(filename, 'w', encoding='utf-8') as f:
        json.dump(data, f, indent=2, ensure_ascii=False)

def generate_transfer_date():
    """Generate a random transfer date within the last 3 months"""
    end_date = datetime.now()
    start_date = end_date - timedelta(days=90)
    random_days = random.randint(0, 90)
    transfer_date = start_date + timedelta(days=random_days)
    return transfer_date.strftime('%Y-%m-%d')

def create_transfer_record(transfer_id, destination_store_id, destination_name, medicines, transfer_date):
    """Create a single transfer record"""
    requesters = ["<PERSON><PERSON> <PERSON>", "<PERSON>", "Pharmacist <PERSON>", "Dr. <PERSON>", "Nurse <PERSON>", "Pharmacist <PERSON>"]
    approvers = ["Pharmacy Manager", "Department Head", "Chief Pharmacist", "Medical Director", "Operations Manager"]
    statuses = ["completed", "in_transit", "pending"]
    
    return {
        "id": str(transfer_id),
        "source_store_id": "01",  # Main Pharmacy Store
        "destination_store_id": destination_store_id,
        "medicines": medicines,
        "transfer_date": transfer_date,
        "notes": f"Transfer to {destination_name}",
        "status": random.choice(statuses),
        "requested_by": random.choice(requesters),
        "approved_by": random.choice(approvers),
        "created_at": datetime.now().isoformat(),
        "updated_at": datetime.now().isoformat()
    }

def main():
    print("Adding 15 inventory transfer records from main pharmacy to department stores...")
    
    # Load existing transfers
    transfers = load_json_file('data/transfers.json')
    
    # Target departments with their store IDs
    target_stores = [
        ("04", "Cardiology Store"),
        ("05", "Oncology Store"), 
        ("06", "Pediatrics Store"),
        ("07", "Surgery Store"),
        ("08", "Neurology Store"),
        ("10", "Psychiatry Store")
    ]
    
    # Medicine IDs to use in transfers (first 50 medicines)
    medicine_ids = [f"{i:02d}" for i in range(1, 51)]
    
    # Starting transfer ID (after existing highest ID 39)
    current_id = 40
    
    # Create 15 transfers (distribute across departments)
    transfers_per_dept = [3, 3, 2, 3, 2, 2]  # Total = 15
    
    for dept_idx, (store_id, store_name) in enumerate(target_stores):
        num_transfers = transfers_per_dept[dept_idx]
        
        for i in range(num_transfers):
            # Generate random medicines for this transfer (1-4 medicines)
            num_medicines = random.randint(1, 4)
            selected_medicines = random.sample(medicine_ids, num_medicines)
            
            medicines = []
            for med_id in selected_medicines:
                quantity = random.randint(5, 50)
                medicines.append({
                    "medicine_id": med_id,
                    "quantity": quantity
                })
            
            # Generate transfer date
            transfer_date = generate_transfer_date()
            
            # Create transfer record
            transfer = create_transfer_record(
                current_id, store_id, store_name, medicines, transfer_date
            )
            
            transfers.append(transfer)
            print(f"Added transfer ID {current_id}: {store_name} - {len(medicines)} medicines")
            current_id += 1
    
    # Save updated transfers
    save_json_file('data/transfers.json', transfers)
    print(f"\nSuccessfully added 15 new inventory transfer records!")
    print(f"Total transfers in database: {len(transfers)}")
    
    # Summary by department
    print("\nTransfers added by department:")
    for dept_idx, (store_id, store_name) in enumerate(target_stores):
        count = transfers_per_dept[dept_idx]
        print(f"- {store_name}: {count} transfers")

if __name__ == "__main__":
    main()
