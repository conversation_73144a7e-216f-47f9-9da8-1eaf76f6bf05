#!/usr/bin/env python3
"""
Test script to check chatbot imports and dependencies
"""

import sys
import traceback

def test_imports():
    """Test all chatbot-related imports"""
    print("🔍 Testing Chatbot Imports and Dependencies")
    print("=" * 60)
    
    # Test basic imports
    try:
        import json
        import os
        import re
        import requests
        from datetime import datetime, timedelta
        from flask import Blueprint, render_template, request, jsonify, session
        print("✅ Basic imports successful")
    except Exception as e:
        print(f"❌ Basic imports failed: {e}")
        return False
    
    # Test utils imports
    try:
        from utils.helpers import login_required, admin_required
        print("✅ utils.helpers import successful")
    except Exception as e:
        print(f"❌ utils.helpers import failed: {e}")
        traceback.print_exc()
        return False
    
    try:
        from utils.database import (
            get_medicines, get_patients, get_suppliers, get_consumption,
            get_stores, get_purchases, get_departments, log_activity,
            delete_medicine, update_store_inventory
        )
        print("✅ utils.database import successful")
    except Exception as e:
        print(f"❌ utils.database import failed: {e}")
        traceback.print_exc()
        return False
    
    try:
        from utils.chatbot_database import chatbot_db
        print("✅ utils.chatbot_database import successful")
    except Exception as e:
        print(f"❌ utils.chatbot_database import failed: {e}")
        traceback.print_exc()
        return False
    
    try:
        from utils.chatbot_agent import pharmacy_agent
        print("✅ utils.chatbot_agent import successful")
    except Exception as e:
        print(f"❌ utils.chatbot_agent import failed: {e}")
        traceback.print_exc()
        return False
    
    try:
        from utils.chat_history import chat_history_manager
        print("✅ utils.chat_history import successful")
    except Exception as e:
        print(f"❌ utils.chat_history import failed: {e}")
        traceback.print_exc()
        return False
    
    # Test chatbot blueprint import
    try:
        from blueprints.chatbot import chatbot_bp
        print("✅ blueprints.chatbot import successful")
    except Exception as e:
        print(f"❌ blueprints.chatbot import failed: {e}")
        traceback.print_exc()
        return False
    
    print("\n🎯 All imports successful!")
    return True

def test_data_files():
    """Test if required data files exist"""
    import os
    print("\n📁 Testing Data Files")
    print("=" * 60)
    
    required_files = [
        'data/chatbot_config.json',
        'data/chat_history.json',
        'data/chat_sessions.json',
        'data/medicines.json',
        'data/patients.json',
        'data/suppliers.json',
        'data/departments.json',
        'data/stores.json',
        'data/purchases.json',
        'data/consumption.json',
        'data/transfers.json',
        'data/users.json'
    ]
    
    missing_files = []
    for file_path in required_files:
        if os.path.exists(file_path):
            print(f"✅ {file_path}")
        else:
            print(f"❌ {file_path} - MISSING")
            missing_files.append(file_path)
    
    if missing_files:
        print(f"\n⚠️ Missing {len(missing_files)} required files")
        return False
    else:
        print("\n🎯 All required data files exist!")
        return True

def test_chatbot_functions():
    """Test basic chatbot functions"""
    print("\n🤖 Testing Chatbot Functions")
    print("=" * 60)
    
    try:
        from blueprints.chatbot import load_chatbot_config, get_pharmacy_context
        
        # Test config loading
        config = load_chatbot_config()
        print(f"✅ Config loaded: {type(config)}")
        
        # Test pharmacy context
        context = get_pharmacy_context()
        print(f"✅ Pharmacy context loaded: {type(context)}")
        
        return True
    except Exception as e:
        print(f"❌ Chatbot functions test failed: {e}")
        traceback.print_exc()
        return False

def test_template_files():
    """Test if template files exist"""
    import os
    print("\n📄 Testing Template Files")
    print("=" * 60)
    
    template_files = [
        'templates/chatbot/index.html',
        'templates/base.html'
    ]
    
    missing_templates = []
    for template_path in template_files:
        if os.path.exists(template_path):
            print(f"✅ {template_path}")
        else:
            print(f"❌ {template_path} - MISSING")
            missing_templates.append(template_path)
    
    if missing_templates:
        print(f"\n⚠️ Missing {len(missing_templates)} template files")
        return False
    else:
        print("\n🎯 All template files exist!")
        return True

def main():
    """Main test function"""
    print("🔍 CHATBOT DEPENDENCY TESTING SUITE")
    print("=" * 80)
    
    all_tests_passed = True
    
    # Run tests
    if not test_imports():
        all_tests_passed = False
    
    if not test_data_files():
        all_tests_passed = False
    
    if not test_template_files():
        all_tests_passed = False
    
    if not test_chatbot_functions():
        all_tests_passed = False
    
    # Final result
    print("\n" + "=" * 80)
    if all_tests_passed:
        print("✅ ALL TESTS PASSED - Chatbot dependencies are working correctly")
        print("🎯 The issue is likely in the production environment configuration")
    else:
        print("❌ SOME TESTS FAILED - Found dependency issues")
        print("🔧 Fix the failed dependencies before deploying")
    
    print("=" * 80)

if __name__ == "__main__":
    main()
