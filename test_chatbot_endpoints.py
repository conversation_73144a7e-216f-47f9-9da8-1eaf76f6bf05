#!/usr/bin/env python3
"""
Test script for chatbot endpoints with proper authentication
"""

import requests
import json

def test_chatbot_endpoints():
    """Test chatbot endpoints with authentication"""
    base_url = "http://127.0.0.1:5000"
    
    # Create a session to maintain cookies
    session = requests.Session()
    
    print("🔐 Testing Chatbot Endpoints with Authentication\n")
    
    # Step 1: Login first
    print("1. Logging in...")
    login_data = {
        'username': 'admin',
        'password': '@Xx123456789xX@'
    }
    
    login_response = session.post(f"{base_url}/auth/login", data=login_data)
    print(f"   Login Status: {login_response.status_code}")
    
    if login_response.status_code != 200:
        print("   ❌ Login failed!")
        return
    
    # Check if we were redirected to dashboard (successful login)
    if 'dashboard' in login_response.url or login_response.status_code == 200:
        print("   ✅ Login successful!")
    else:
        print("   ❌ Login may have failed - unexpected response")
        return
    
    # Step 2: Test configuration GET endpoint
    print("\n2. Testing configuration GET endpoint...")
    config_response = session.get(f"{base_url}/chatbot/config")
    print(f"   Status: {config_response.status_code}")
    
    if config_response.status_code == 200:
        try:
            config_data = config_response.json()
            print("   ✅ Configuration GET successful!")
            print(f"   Provider: {config_data.get('provider', 'Unknown')}")
            print(f"   Model: {config_data.get('model', 'Unknown')}")
            print(f"   Enabled: {config_data.get('enabled', False)}")
        except json.JSONDecodeError:
            print("   ❌ Response is not JSON - likely HTML redirect")
            print(f"   Response preview: {config_response.text[:200]}...")
    else:
        print(f"   ❌ Configuration GET failed with status {config_response.status_code}")
    
    # Step 3: Test configuration POST endpoint
    print("\n3. Testing configuration POST endpoint...")
    config_data = {
        'provider': 'openrouter',
        'openrouter_api_key': '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',
        'model': 'deepseek/deepseek-r1:nitro',
        'max_tokens': '1000',
        'temperature': '0.7',
        'enabled': 'on'
    }
    
    config_post_response = session.post(f"{base_url}/chatbot/config", data=config_data)
    print(f"   Status: {config_post_response.status_code}")
    
    if config_post_response.status_code == 200:
        try:
            response_data = config_post_response.json()
            print(f"   ✅ Configuration POST successful!")
            print(f"   Success: {response_data.get('success', False)}")
            print(f"   Message: {response_data.get('message', 'No message')}")
        except json.JSONDecodeError:
            print("   ❌ Response is not JSON")
            print(f"   Response preview: {config_post_response.text[:200]}...")
    else:
        print(f"   ❌ Configuration POST failed with status {config_post_response.status_code}")
    
    # Step 4: Test chatbot query endpoint
    print("\n4. Testing chatbot query endpoint...")
    query_data = {
        'query': 'Show me complete overview of all database tables with detailed analysis'
    }
    
    query_response = session.post(
        f"{base_url}/chatbot/query", 
        json=query_data,
        headers={'Content-Type': 'application/json'}
    )
    print(f"   Status: {query_response.status_code}")
    
    if query_response.status_code == 200:
        try:
            response_data = query_response.json()
            print("   ✅ Query successful!")
            print(f"   Status: {response_data.get('status', 'Unknown')}")
            print(f"   LLM Handled: {response_data.get('llm_handled', False)}")
            response_preview = response_data.get('response', '')[:200]
            print(f"   Response preview: {response_preview}...")
        except json.JSONDecodeError:
            print("   ❌ Response is not JSON")
            print(f"   Response preview: {query_response.text[:200]}...")
    else:
        print(f"   ❌ Query failed with status {query_response.status_code}")
        print(f"   Response: {query_response.text[:200]}...")
    
    print("\n🎯 Chatbot Endpoint Testing Complete!")

if __name__ == "__main__":
    test_chatbot_endpoints()
