{% extends "base.html" %}

{% block title %}Edit Purchase - Hospital Pharmacy Management System{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1><i class="bi bi-pencil"></i> Edit Purchase</h1>
    <a href="{{ url_for('purchases.index') }}" class="btn btn-outline-secondary">
        <i class="bi bi-arrow-left"></i> Back to Purchases
    </a>
</div>

<div class="card">
    <div class="card-body">
        <form method="POST" id="editPurchaseForm">
            <div class="row">
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="date" class="form-label">Purchase Date <span class="text-danger">*</span></label>
                        <input type="date" class="form-control" id="date" name="date" 
                               value="{{ purchase.date or purchase.purchase_date }}" required>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="invoice_number" class="form-label">Invoice Number <span class="text-danger">*</span></label>
                        <input type="text" class="form-control" id="invoice_number" name="invoice_number" 
                               value="{{ purchase.invoice_number }}" required>
                    </div>
                </div>
            </div>

            <div class="row">
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="supplier_id" class="form-label">Supplier <span class="text-danger">*</span></label>
                        <select class="form-select" id="supplier_id" name="supplier_id" required>
                            <option value="">Select Supplier</option>
                            {% for supplier in suppliers %}
                            <option value="{{ supplier.id }}"
                                    {% if supplier.id == purchase.supplier_id %}selected{% endif %}>
                                {{ supplier.name }}
                            </option>
                            {% endfor %}
                        </select>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="purchaser_name" class="form-label">Purchaser Name <span class="text-danger">*</span></label>
                        <input type="text" class="form-control" id="purchaser_name" name="purchaser_name"
                               value="{{ purchase.purchaser_name }}" required>
                    </div>
                </div>
            </div>

            <!-- Status and Delivery Information -->
            <div class="row">
                <div class="col-md-4">
                    <div class="mb-3">
                        <label for="status" class="form-label">Status <span class="text-danger">*</span></label>
                        <select class="form-select" id="status" name="status" required onchange="toggleDeliveryFields()">
                            <option value="">Select Status</option>
                            <option value="complete" {% if purchase.status == 'complete' %}selected{% endif %}>Complete</option>
                            <option value="pending" {% if purchase.status == 'pending' %}selected{% endif %}>Pending</option>
                            <option value="delivered" {% if purchase.status == 'delivered' %}selected{% endif %}>Delivered</option>
                        </select>
                        <div class="invalid-feedback">
                            Please select a status.
                        </div>
                    </div>
                </div>
                <div class="col-md-4" id="deliveryDateField" style="display: {% if purchase.status == 'delivered' %}block{% else %}none{% endif %};">
                    <div class="mb-3">
                        <label for="delivery_date" class="form-label">Delivery Date <span class="text-danger">*</span></label>
                        <input type="date" class="form-control" id="delivery_date" name="delivery_date"
                               value="{{ purchase.delivery_date or '' }}" {% if purchase.status == 'delivered' %}required{% endif %}>
                        <div class="invalid-feedback">
                            Please provide a delivery date.
                        </div>
                    </div>
                </div>
                <div class="col-md-4" id="receivedByField" style="display: {% if purchase.status == 'delivered' %}block{% else %}none{% endif %};">
                    <div class="mb-3">
                        <label for="received_by" class="form-label">Received By <span class="text-danger">*</span></label>
                        <input type="text" class="form-control" id="received_by" name="received_by"
                               value="{{ purchase.received_by or '' }}" placeholder="Name of person who received"
                               {% if purchase.status == 'delivered' %}required{% endif %}>
                        <div class="invalid-feedback">
                            Please provide the name of person who received.
                        </div>
                    </div>
                </div>
            </div>

            <!-- Medicines Section -->
            <div class="mb-4">
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <h5><i class="bi bi-capsule"></i> Medicines</h5>
                    <button type="button" class="btn btn-sm btn-outline-primary" onclick="addMedicineRow()">
                        <i class="bi bi-plus"></i> Add Medicine
                    </button>
                </div>

                <div id="medicinesContainer">
                    {% for medicine_item in purchase.medicines %}
                    <div class="medicine-row mb-3">
                        <div class="row align-items-end">
                            <div class="col-md-6">
                                <label class="form-label">Medicine</label>
                                <select class="form-select" name="medicine_id[]" required>
                                    <option value="">Select Medicine</option>
                                    {% for medicine in medicines %}
                                    <option value="{{ medicine.id }}" 
                                            {% if medicine.id == medicine_item.medicine_id %}selected{% endif %}>
                                        {{ medicine.name }} ({{ medicine.form_dosage }})
                                    </option>
                                    {% endfor %}
                                </select>
                            </div>
                            <div class="col-md-4">
                                <label class="form-label">Quantity</label>
                                <input type="number" class="form-control" name="quantity[]" 
                                       value="{{ medicine_item.quantity }}" min="1" required>
                            </div>
                            <div class="col-md-2">
                                <button type="button" class="btn btn-outline-danger" onclick="removeMedicineRow(this)">
                                    <i class="bi bi-trash"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                    
                    {% if not purchase.medicines %}
                    <div class="medicine-row mb-3">
                        <div class="row align-items-end">
                            <div class="col-md-6">
                                <label class="form-label">Medicine</label>
                                <select class="form-select" name="medicine_id[]" required>
                                    <option value="">Select Medicine</option>
                                    {% for medicine in medicines %}
                                    <option value="{{ medicine.id }}">{{ medicine.name }} ({{ medicine.form_dosage }})</option>
                                    {% endfor %}
                                </select>
                            </div>
                            <div class="col-md-4">
                                <label class="form-label">Quantity</label>
                                <input type="number" class="form-control" name="quantity[]" min="1" required>
                            </div>
                            <div class="col-md-2">
                                <button type="button" class="btn btn-outline-danger" onclick="removeMedicineRow(this)">
                                    <i class="bi bi-trash"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                    {% endif %}
                </div>
            </div>

            <div class="mb-3">
                <label for="notes" class="form-label">Notes</label>
                <textarea class="form-control" id="notes" name="notes" rows="3">{{ purchase.notes }}</textarea>
            </div>

            <div class="d-flex justify-content-end gap-2">
                <a href="{{ url_for('purchases.index') }}" class="btn btn-secondary">Cancel</a>
                <button type="submit" class="btn btn-primary">
                    <i class="bi bi-check-circle"></i> Update Purchase
                </button>
            </div>
        </form>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// Toggle delivery fields based on status selection
function toggleDeliveryFields() {
    const status = document.getElementById('status').value;
    const deliveryDateField = document.getElementById('deliveryDateField');
    const receivedByField = document.getElementById('receivedByField');
    const deliveryDateInput = document.getElementById('delivery_date');
    const receivedByInput = document.getElementById('received_by');

    if (status === 'delivered') {
        deliveryDateField.style.display = 'block';
        receivedByField.style.display = 'block';
        deliveryDateInput.required = true;
        receivedByInput.required = true;

        // Set delivery date to today if not already set
        if (!deliveryDateInput.value) {
            const today = new Date().toISOString().split('T')[0];
            deliveryDateInput.value = today;
        }
    } else {
        deliveryDateField.style.display = 'none';
        receivedByField.style.display = 'none';
        deliveryDateInput.required = false;
        receivedByInput.required = false;
        deliveryDateInput.value = '';
        receivedByInput.value = '';
    }
}

// Add new medicine row
function addMedicineRow() {
    const container = document.getElementById('medicinesContainer');
    const newRow = document.createElement('div');
    newRow.className = 'medicine-row mb-3';
    
    newRow.innerHTML = `
        <div class="row align-items-end">
            <div class="col-md-6">
                <label class="form-label">Medicine</label>
                <select class="form-select" name="medicine_id[]" required>
                    <option value="">Select Medicine</option>
                    {% for medicine in medicines %}
                    <option value="{{ medicine.id }}">{{ medicine.name }} ({{ medicine.form_dosage }})</option>
                    {% endfor %}
                </select>
            </div>
            <div class="col-md-4">
                <label class="form-label">Quantity</label>
                <input type="number" class="form-control" name="quantity[]" min="1" required>
            </div>
            <div class="col-md-2">
                <button type="button" class="btn btn-outline-danger" onclick="removeMedicineRow(this)">
                    <i class="bi bi-trash"></i>
                </button>
            </div>
        </div>
    `;
    
    container.appendChild(newRow);
    updateRemoveButtons();
}

// Remove medicine row
function removeMedicineRow(button) {
    const row = button.closest('.medicine-row');
    row.remove();
    updateRemoveButtons();
}

// Update remove buttons (disable if only one row)
function updateRemoveButtons() {
    const rows = document.querySelectorAll('.medicine-row');
    const removeButtons = document.querySelectorAll('.medicine-row button[onclick*="removeMedicineRow"]');
    
    removeButtons.forEach(button => {
        button.disabled = rows.length <= 1;
    });
}

// Initialize on page load
document.addEventListener('DOMContentLoaded', function() {
    updateRemoveButtons();
});
</script>
{% endblock %}
