"""
Patients Management Blueprint
"""

from flask import Blueprint, render_template, request, redirect, url_for, flash, Response
import csv
import io
from utils.helpers import login_required
from utils.database import get_patients, save_patient, update_patient, delete_patient, get_departments

patients_bp = Blueprint('patients', __name__)

@patients_bp.route('/', methods=['GET', 'POST'])
@login_required
def index():
    """Patients list page"""
    if request.method == 'POST':
        # Handle CSV import
        if 'csv_file' not in request.files:
            flash('No file selected!', 'error')
            return redirect(url_for('patients.index'))

        file = request.files['csv_file']
        if file.filename == '':
            flash('No file selected!', 'error')
            return redirect(url_for('patients.index'))

        if not file.filename.lower().endswith('.csv'):
            flash('Please upload a CSV file!', 'error')
            return redirect(url_for('patients.index'))

        try:
            # Read CSV file
            stream = io.StringIO(file.stream.read().decode("UTF8"), newline=None)
            csv_input = csv.DictReader(stream)

            imported_count = 0
            errors = []

            for row_num, row in enumerate(csv_input, start=2):
                # Skip comment lines
                if any(value.startswith('#') for value in row.values()):
                    continue

                # Validate required fields
                if not row.get('name', '').strip():
                    errors.append(f"Row {row_num}: Name is required")
                    continue

                if not row.get('gender', '').strip():
                    errors.append(f"Row {row_num}: Gender is required")
                    continue

                # Create patient data
                patient_data = {
                    'name': row.get('name', '').strip(),
                    'file_no': row.get('file_no', '').strip(),
                    'gender': row.get('gender', '').strip(),
                    'date_of_entry': row.get('date_of_entry', '').strip(),
                    'department_id': row.get('department_id', '').strip(),
                    'medical_history': row.get('medical_history', '').strip(),
                    'notes': row.get('notes', '').strip()
                }

                # Save patient
                save_patient(patient_data)
                imported_count += 1

            if errors:
                flash(f'Import completed with {imported_count} patients imported. Errors: {"; ".join(errors)}', 'warning')
            else:
                flash(f'Successfully imported {imported_count} patients!', 'success')

        except Exception as e:
            flash(f'Error processing CSV file: {str(e)}', 'error')

        return redirect(url_for('patients.index'))

    patients = get_patients()
    departments = get_departments()
    return render_template('patients/index.html', patients=patients, departments=departments)

@patients_bp.route('/add', methods=['GET', 'POST'])
@login_required
def add():
    """Add new patient"""
    if request.method == 'POST':
        patient_data = {
            'name': request.form.get('name'),
            'file_no': request.form.get('file_no', ''),
            'gender': request.form.get('gender'),
            'date_of_entry': request.form.get('date_of_entry'),
            'medical_history': request.form.get('medical_history', ''),
            'notes': request.form.get('notes', ''),
            'department_id': request.form.get('department_id', '')
        }

        patient_id = save_patient(patient_data)
        flash(f'Patient added successfully with ID: {patient_id}', 'success')
        return redirect(url_for('patients.index'))

    departments = get_departments()
    return render_template('patients/add.html', departments=departments)

@patients_bp.route('/edit/<patient_id>', methods=['GET', 'POST'])
@login_required
def edit(patient_id):
    """Edit patient"""
    patients = get_patients()
    patient = next((p for p in patients if p['id'] == patient_id), None)

    if not patient:
        flash('Patient not found!', 'error')
        return redirect(url_for('patients.index'))

    if request.method == 'POST':
        patient_data = {
            'name': request.form.get('name'),
            'file_no': request.form.get('file_no', ''),
            'gender': request.form.get('gender'),
            'date_of_entry': request.form.get('date_of_entry'),
            'medical_history': request.form.get('medical_history', ''),
            'notes': request.form.get('notes', ''),
            'department_id': request.form.get('department_id', '')
        }

        update_patient(patient_id, patient_data)
        flash('Patient updated successfully!', 'success')
        return redirect(url_for('patients.index'))

    departments = get_departments()
    return render_template('patients/edit.html', patient=patient, departments=departments)

@patients_bp.route('/delete/<patient_id>')
@login_required
def delete(patient_id):
    """Delete patient"""
    delete_patient(patient_id)
    flash('Patient deleted successfully!', 'success')
    return redirect(url_for('patients.index'))

@patients_bp.route('/template/download')
@login_required
def download_template():
    """Download CSV template for patients import"""
    template_headers = [
        'name',
        'file_no',
        'gender',
        'date_of_entry',
        'department_id',
        'medical_history',
        'notes'
    ]

    csv_content = ','.join(template_headers) + '\n'
    csv_content += '# Example: John Doe,P001,Male,2025-07-25,01,Diabetes,Patient notes\n'

    return Response(
        csv_content,
        mimetype='text/csv',
        headers={'Content-Disposition': 'attachment; filename=patients_template.csv'}
    )
