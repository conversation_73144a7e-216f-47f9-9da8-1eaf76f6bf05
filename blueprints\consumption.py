"""
Consumption Management Blueprint (Stock Out)
"""

from flask import Blueprint, render_template, request, redirect, url_for, flash, session
from utils.helpers import login_required
from utils.database import get_consumption, save_consumption, get_patients, get_medicines, get_stores, get_departments

consumption_bp = Blueprint('consumption', __name__)

@consumption_bp.route('/')
@login_required
def index():
    """Consumption list page"""
    consumption = get_consumption()
    patients = get_patients()
    departments = get_departments()
    medicines = get_medicines()

    # Filter by department if not admin
    if session.get('role') != 'admin':
        user_department_id = session.get('department_id')
        consumption = [c for c in consumption if c.get('department_id') == user_department_id]

    # Calculate total quantity for each consumption record
    for consumption_record in consumption:
        total_qty = 0
        for medicine_item in consumption_record.get('medicines', []):
            total_qty += medicine_item.get('quantity', 0)
        consumption_record['total_quantity'] = total_qty

    return render_template('consumption/index.html',
                         consumption=consumption,
                         patients=patients,
                         departments=departments,
                         medicines=medicines)

@consumption_bp.route('/add', methods=['GET', 'POST'])
@login_required
def add():
    """Add new consumption (Point-of-Sale style)"""
    if request.method == 'POST':
        # Handle multiple medicines in consumption
        medicines_data = []
        medicine_ids = request.form.getlist('medicine_id[]')
        quantities = request.form.getlist('quantity[]')
        
        for i, medicine_id in enumerate(medicine_ids):
            if medicine_id and i < len(quantities):
                medicines_data.append({
                    'medicine_id': medicine_id,
                    'quantity': int(quantities[i])
                })
        
        consumption_data = {
            'date': request.form.get('date'),
            'patient_id': request.form.get('patient_id'),
            'medicines': medicines_data,
            'prescribed_by': request.form.get('prescribed_by', ''),
            'notes': request.form.get('notes', ''),
            'department_id': request.form.get('department_id') or session.get('department_id', '01')
        }
        
        consumption_id = save_consumption(consumption_data)
        flash(f'Consumption recorded successfully with ID: {consumption_id}', 'success')
        return redirect(url_for('consumption.index'))
    
    patients = get_patients()
    medicines = get_medicines()
    stores = get_stores()
    departments = get_departments()

    # Filter stores by department if not admin
    if session.get('role') != 'admin':
        user_department_id = session.get('department_id')
        stores = [s for s in stores if s.get('department_id') == user_department_id]
        departments = [d for d in departments if d.get('id') == user_department_id]

    return render_template('consumption/add.html', patients=patients, medicines=medicines, stores=stores, departments=departments)

@consumption_bp.route('/edit/<consumption_id>', methods=['GET', 'POST'])
@login_required
def edit(consumption_id):
    """Edit consumption"""
    from utils.database import get_consumption, update_consumption, get_patients, get_medicines, get_stores, get_departments

    consumption_records = get_consumption()
    consumption = next((c for c in consumption_records if c['id'] == consumption_id), None)

    if not consumption:
        flash('Consumption record not found!', 'error')
        return redirect(url_for('consumption.index'))

    if request.method == 'POST':
        # Handle multiple medicines in consumption
        medicines_data = []
        medicine_ids = request.form.getlist('medicine_id[]')
        quantities = request.form.getlist('quantity[]')

        for i, medicine_id in enumerate(medicine_ids):
            if medicine_id and i < len(quantities):
                medicines_data.append({
                    'medicine_id': medicine_id,
                    'quantity': int(quantities[i])
                })

        consumption_data = {
            'patient_id': request.form.get('patient_id'),
            'date': request.form.get('date'),
            'medicines': medicines_data,
            'prescribed_by': request.form.get('prescribed_by'),
            'notes': request.form.get('notes', ''),
            'department_id': request.form.get('department_id') or session.get('department_id', '01')
        }

        update_consumption(consumption_id, consumption_data)
        flash('Consumption updated successfully!', 'success')
        return redirect(url_for('consumption.index'))

    patients = get_patients()
    medicines = get_medicines()
    stores = get_stores()
    departments = get_departments()

    # Filter stores by department if not admin
    if session.get('role') != 'admin':
        user_department_id = session.get('department_id')
        stores = [s for s in stores if s.get('department_id') == user_department_id]
        departments = [d for d in departments if d.get('id') == user_department_id]

    return render_template('consumption/edit.html',
                         consumption=consumption,
                         patients=patients,
                         medicines=medicines,
                         stores=stores,
                         departments=departments)

@consumption_bp.route('/delete/<consumption_id>')
@login_required
def delete(consumption_id):
    """Delete consumption"""
    from utils.database import get_consumption, delete_consumption

    consumption_records = get_consumption()
    consumption = next((c for c in consumption_records if c['id'] == consumption_id), None)

    if not consumption:
        flash('Consumption record not found!', 'error')
        return redirect(url_for('consumption.index'))

    delete_consumption(consumption_id)
    flash('Consumption deleted successfully!', 'success')
    return redirect(url_for('consumption.index'))
