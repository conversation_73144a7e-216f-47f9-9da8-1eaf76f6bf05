{% extends "base.html" %}

{% block title %}Patients Management - Hospital Pharmacy Management System{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1><i class="bi bi-person"></i> Patients Management</h1>
    <div>
        <a href="{{ url_for('patients.add') }}" class="btn btn-primary">
            <i class="bi bi-person-plus"></i> Add Patient
        </a>
        <div class="btn-group" role="group">
            <button type="button" class="btn btn-outline-secondary dropdown-toggle" data-bs-toggle="dropdown">
                <i class="bi bi-download"></i> Export
            </button>
            <ul class="dropdown-menu">
                <li><a class="dropdown-item" href="#" onclick="exportTableToCSV('patientsTable', 'patients')">
                    <i class="bi bi-file-earmark-spreadsheet"></i> Export CSV
                </a></li>
                <li><a class="dropdown-item" href="{{ url_for('patients.download_template') }}">
                    <i class="bi bi-file-earmark-arrow-down"></i> Download Template
                </a></li>
            </ul>
        </div>
        <button type="button" class="btn btn-outline-info" data-bs-toggle="modal" data-bs-target="#importModal">
            <i class="bi bi-upload"></i> Import
        </button>
    </div>
</div>

<!-- Search and Filter -->
<div class="row mb-3">
    <div class="col-md-6">
        <div class="input-group">
            <span class="input-group-text"><i class="bi bi-search"></i></span>
            <input type="text" class="form-control" id="searchInput" placeholder="Search patients...">
        </div>
    </div>
    <div class="col-md-3">
        <select class="form-select" id="genderFilter">
            <option value="">All Genders</option>
            <option value="Male">Male</option>
            <option value="Female">Female</option>
        </select>
    </div>
    <div class="col-md-3">
        <input type="date" class="form-control" id="dateFilter" placeholder="Filter by date">
    </div>
</div>

<!-- Patients Table -->
<div class="card">
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-hover" id="patientsTable">
                <thead>
                    <tr>
                        <th>ID</th>
                        <th>File No</th>
                        <th>Patient Name</th>
                        <th>Gender</th>
                        <th>Department</th>
                        <th>Created Date</th>
                        <th>Medical History</th>
                        <th>Notes</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    {% for patient in patients %}
                    <tr>
                        <td>{{ patient.id }}</td>
                        <td><code>{{ patient.file_no or 'N/A' }}</code></td>
                        <td><strong>{{ patient.name }}</strong></td>
                        <td>
                            <span class="badge {% if patient.gender == 'Male' %}bg-primary{% else %}bg-info{% endif %}">
                                <i class="bi {% if patient.gender == 'Male' %}bi-person{% else %}bi-person-dress{% endif %}"></i>
                                {{ patient.gender }}
                            </span>
                        </td>
                        <td>
                            {% set department = departments|selectattr('id', 'equalto', patient.department_id)|first %}
                            {% if department %}
                                <span class="badge bg-secondary">{{ department.name }}</span>
                            {% else %}
                                <span class="text-muted">N/A</span>
                            {% endif %}
                        </td>
                        <td>{{ patient.created_at[:10] }}</td>
                        <td>{{ patient.medical_history[:50] if patient.medical_history else 'N/A' }}{% if patient.medical_history and patient.medical_history|length > 50 %}...{% endif %}</td>
                        <td>{{ patient.notes[:30] if patient.notes else 'N/A' }}{% if patient.notes and patient.notes|length > 30 %}...{% endif %}</td>
                        <td class="action-buttons">
                            <a href="{{ url_for('patients.edit', patient_id=patient.id) }}" 
                               class="btn btn-sm btn-outline-primary" title="Edit">
                                <i class="bi bi-pencil"></i>
                            </a>
                            <a href="{{ url_for('patients.delete', patient_id=patient.id) }}" 
                               class="btn btn-sm btn-outline-danger" title="Delete"
                               onclick="return confirmDelete('Are you sure you want to delete this patient?')">
                                <i class="bi bi-trash"></i>
                            </a>
                        </td>
                    </tr>
                    {% else %}
                    <tr>
                        <td colspan="9" class="text-center text-muted">
                            <i class="bi bi-inbox"></i> No patients found.
                            <a href="{{ url_for('patients.add') }}">Add the first patient</a>.
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>
</div>

<!-- Import Modal -->
<div class="modal fade" id="importModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Import Patients</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="importForm" method="POST" enctype="multipart/form-data" action="{{ url_for('patients.index') }}">
                    <div class="mb-3">
                        <label for="csvFile" class="form-label">Select CSV File</label>
                        <input type="file" class="form-control" id="csvFile" name="csv_file" accept=".csv" required>
                        <div class="form-text">
                            Please use the template format. <a href="{{ url_for('patients.download_template') }}">Download template</a>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="submit" form="importForm" class="btn btn-primary">Import</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// Initialize search functionality
document.addEventListener('DOMContentLoaded', function() {
    searchTable('searchInput', 'patientsTable');
});
</script>
{% endblock %}
