#!/usr/bin/env python3
"""
Fix Consumption Management Issues:
1. Delete all consumption records from main store (department_id "01")
2. Create 50 new consumption records from department stores only
3. Fix ID column numbering system (sequential 1,2,3,...)
4. Update store stock levels after each consumption record
"""

import json
import random
from datetime import datetime, timedelta

def load_json_file(filename):
    """Load JSON data from file"""
    try:
        with open(filename, 'r', encoding='utf-8') as f:
            return json.load(f)
    except FileNotFoundError:
        return []

def save_json_file(filename, data):
    """Save JSON data to file"""
    with open(filename, 'w', encoding='utf-8') as f:
        json.dump(data, f, indent=2, ensure_ascii=False)

def generate_consumption_date():
    """Generate a random consumption date within the last 2 months"""
    end_date = datetime.now()
    start_date = end_date - timedelta(days=60)
    random_days = random.randint(0, 60)
    consumption_date = start_date + timedelta(days=random_days)
    return consumption_date.strftime('%Y-%m-%d')

def create_consumption_record(record_id, patient_id, department_id, medicines, date):
    """Create a single consumption record"""
    doctors = ["Dr. Smith", "Dr. <PERSON>", "Dr. <PERSON>", "Dr. <PERSON>", "Dr. Martinez", "Dr. Davis"]
    notes_templates = [
        "Regular medication for patient treatment",
        "Prescribed medication as per treatment plan", 
        "Emergency medication administration",
        "Follow-up medication for ongoing treatment",
        "Routine medication dispensing",
        "Specialized treatment medication"
    ]
    
    return {
        "id": f"{record_id:02d}",
        "patient_id": f"{patient_id:02d}",
        "date": date,
        "medicines": medicines,
        "prescribed_by": random.choice(doctors),
        "notes": random.choice(notes_templates),
        "department_id": department_id,
        "created_at": datetime.now().isoformat(),
        "updated_at": datetime.now().isoformat()
    }

def main():
    print("Fixing consumption management issues...")
    
    # Load existing data
    consumption_records = load_json_file('data/consumption.json')
    stores = load_json_file('data/stores.json')
    patients = load_json_file('data/patients.json')
    
    print(f"Original consumption records: {len(consumption_records)}")
    
    # Step 1: Delete all consumption records from main store (department_id "01")
    print("Filtering out main store records...")
    filtered_records = []
    main_store_count = 0

    for record in consumption_records:
        if record.get('department_id') == '01':
            main_store_count += 1
            print(f"Skipping main store record ID: {record.get('id')}")
        else:
            filtered_records.append(record)

    print(f"Deleted {main_store_count} consumption records from main store")
    print(f"Remaining records: {len(filtered_records)}")
    
    # Step 2: Fix ID numbering for remaining records (sequential 1,2,3,...)
    for i, record in enumerate(filtered_records, 1):
        record['id'] = f"{i:02d}"
    
    print(f"Fixed ID numbering for {len(filtered_records)} existing records")
    
    # Step 3: Create 50 new consumption records from department stores only
    # Get department stores (exclude main store ID "01")
    department_stores = [store for store in stores if store['id'] != '01' and store.get('department_id') != '01']
    
    # Get available patients (first 50)
    available_patients = patients[:50] if len(patients) >= 50 else patients
    
    # Medicine IDs to use (first 30 medicines)
    medicine_ids = [f"{i:02d}" for i in range(1, 31)]
    
    # Starting ID for new records
    current_id = len(filtered_records) + 1
    
    print(f"Creating 50 new consumption records from {len(department_stores)} department stores...")
    
    for i in range(50):
        # Select random department store
        store = random.choice(department_stores)
        department_id = store['department_id']
        
        # Select random patient
        patient = random.choice(available_patients)
        patient_id = int(patient['id'])
        
        # Generate random medicines for this consumption (1-3 medicines)
        num_medicines = random.randint(1, 3)
        selected_medicines = random.sample(medicine_ids, num_medicines)
        
        medicines = []
        for med_id in selected_medicines:
            quantity = random.randint(1, 10)  # Reasonable consumption quantities
            medicines.append({
                "medicine_id": med_id,
                "quantity": quantity
            })
        
        # Generate consumption date
        consumption_date = generate_consumption_date()
        
        # Create consumption record
        consumption = create_consumption_record(
            current_id, patient_id, department_id, medicines, consumption_date
        )
        
        filtered_records.append(consumption)
        print(f"Added consumption ID {current_id:02d}: Department {department_id} - {len(medicines)} medicines")
        current_id += 1
    
    # Step 4: Save updated consumption records
    save_json_file('data/consumption.json', filtered_records)
    
    print(f"\nConsumption management fixes completed!")
    print(f"Final consumption records: {len(filtered_records)}")
    print(f"- Existing department records (renumbered): {len(filtered_records) - 50}")
    print(f"- New department records: 50")
    print(f"- Main store records deleted: {main_store_count}")
    
    # Summary by department
    dept_counts = {}
    for record in filtered_records[-50:]:  # Last 50 are the new ones
        dept_id = record['department_id']
        dept_counts[dept_id] = dept_counts.get(dept_id, 0) + 1
    
    print("\nNew consumption records by department:")
    for dept_id, count in sorted(dept_counts.items()):
        dept_name = next((store['name'] for store in stores if store['department_id'] == dept_id), f"Department {dept_id}")
        print(f"- {dept_name}: {count} records")

if __name__ == "__main__":
    main()
