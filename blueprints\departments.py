"""
Departments Management Blueprint
"""

from flask import Blueprint, render_template, request, redirect, url_for, flash, Response
import csv
import io
from utils.helpers import login_required, admin_required
from utils.database import get_departments, save_department, save_department_with_user, update_department, delete_department, delete_department_and_store, create_store_for_department

departments_bp = Blueprint('departments', __name__)

@departments_bp.route('/', methods=['GET', 'POST'])
@login_required
def index():
    """Departments list page"""
    if request.method == 'POST':
        # Handle CSV import (admin only)
        from flask import session
        if session.get('role') != 'admin':
            flash('Only administrators can import departments!', 'error')
            return redirect(url_for('departments.index'))

        if 'csv_file' not in request.files:
            flash('No file selected!', 'error')
            return redirect(url_for('departments.index'))

        file = request.files['csv_file']
        if file.filename == '':
            flash('No file selected!', 'error')
            return redirect(url_for('departments.index'))

        if not file.filename.lower().endswith('.csv'):
            flash('Please upload a CSV file!', 'error')
            return redirect(url_for('departments.index'))

        try:
            # Read CSV file
            stream = io.StringIO(file.stream.read().decode("UTF8"), newline=None)
            csv_input = csv.DictReader(stream)

            imported_count = 0
            errors = []

            for row_num, row in enumerate(csv_input, start=2):
                # Skip comment lines
                if any(value.startswith('#') for value in row.values()):
                    continue

                # Validate required fields
                if not row.get('name', '').strip():
                    errors.append(f"Row {row_num}: Name is required")
                    continue

                # Create department data (map CSV fields to department fields)
                department_data = {
                    'name': row.get('name', '').strip(),
                    'responsible_person': row.get('responsible_person', '').strip(),
                    'telephone': row.get('telephone', '').strip(),
                    'notes': row.get('notes', '').strip()
                }

                # Save department with automatic user creation
                result = save_department_with_user(department_data)
                imported_count += 1

            if errors:
                flash(f'Import completed with {imported_count} departments imported. Errors: {"; ".join(errors)}', 'warning')
            else:
                flash(f'Successfully imported {imported_count} departments!', 'success')

        except Exception as e:
            flash(f'Error processing CSV file: {str(e)}', 'error')

        return redirect(url_for('departments.index'))

    departments = get_departments()
    return render_template('departments/index.html', departments=departments)

@departments_bp.route('/add', methods=['GET', 'POST'])
@admin_required
def add():
    """Add new department with automatic user creation"""
    if request.method == 'POST':
        department_data = {
            'name': request.form.get('name'),
            'responsible_person': request.form.get('responsible_person'),
            'telephone': request.form.get('telephone'),
            'notes': request.form.get('notes', '')
        }

        try:
            # Save department with automatic store and user creation
            result = save_department_with_user(department_data)

            department_id = result['department_id']
            user_info = result['user_info']

            # Show success message with user credentials
            flash(f'Department "{department_data["name"]}" added successfully!', 'success')
            flash(f'Department User Created - Username: {user_info["username"]}, Password: {user_info["password"]}', 'info')
            flash('Please save these credentials and share them with the department staff.', 'warning')

        except Exception as e:
            flash(f'Error creating department: {str(e)}', 'error')

        return redirect(url_for('departments.index'))

    return render_template('departments/add.html')

@departments_bp.route('/edit/<department_id>', methods=['GET', 'POST'])
@admin_required
def edit(department_id):
    """Edit department"""
    departments = get_departments()
    department = next((d for d in departments if d['id'] == department_id), None)
    
    if not department:
        flash('Department not found!', 'error')
        return redirect(url_for('departments.index'))
    
    if request.method == 'POST':
        department_data = {
            'name': request.form.get('name'),
            'responsible_person': request.form.get('responsible_person'),
            'telephone': request.form.get('telephone'),
            'notes': request.form.get('notes', '')
        }
        
        update_department(department_id, department_data)
        flash('Department updated successfully!', 'success')
        return redirect(url_for('departments.index'))
    
    return render_template('departments/edit.html', department=department)

@departments_bp.route('/delete/<department_id>')
@admin_required
def delete(department_id):
    """Delete department and associated store"""
    # Prevent deletion of main department
    if department_id == '01':
        flash('Cannot delete main department!', 'error')
        return redirect(url_for('departments.index'))

    success, message = delete_department_and_store(department_id)

    if success:
        flash(message, 'success')
    else:
        flash(message, 'error')

    return redirect(url_for('departments.index'))

@departments_bp.route('/template/download')
@admin_required
def download_template():
    """Download CSV template for departments import"""
    template_headers = [
        'name',
        'responsible_person',
        'telephone',
        'notes'
    ]

    csv_content = ','.join(template_headers) + '\n'
    csv_content += '# Example: Cardiology,Dr. Smith,******-0123,Heart and cardiovascular care department\n'

    return Response(
        csv_content,
        mimetype='text/csv',
        headers={'Content-Disposition': 'attachment; filename=departments_template.csv'}
    )
