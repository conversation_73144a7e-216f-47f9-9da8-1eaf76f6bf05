#!/usr/bin/env python3
"""
Cleanup script for medicines database
Removes unused fields: unit_price, strength, category
Keeps only: id, name, supplier_id, form_dosage, low_stock_limit, notes, created_at, updated_at
"""

import json
import os
from datetime import datetime

def cleanup_medicines_database():
    """Remove unused fields from medicines database"""
    
    # Load medicines data
    medicines_file = 'data/medicines.json'
    
    if not os.path.exists(medicines_file):
        print(f"Error: {medicines_file} not found")
        return
    
    # Backup original file
    backup_file = f'data/medicines_backup_{datetime.now().strftime("%Y%m%d_%H%M%S")}.json'
    
    try:
        with open(medicines_file, 'r', encoding='utf-8') as f:
            medicines = json.load(f)
        
        # Create backup
        with open(backup_file, 'w', encoding='utf-8') as f:
            json.dump(medicines, f, indent=2, ensure_ascii=False)
        print(f"Backup created: {backup_file}")
        
        # Fields to keep
        fields_to_keep = {
            'id', 'name', 'supplier_id', 'form_dosage', 
            'low_stock_limit', 'notes', 'created_at', 'updated_at'
        }
        
        # Fields to remove
        fields_to_remove = {'unit_price', 'strength', 'category'}
        
        # Clean up each medicine record
        cleaned_medicines = []
        removed_fields_count = {field: 0 for field in fields_to_remove}

        print(f"Processing {len(medicines)} medicine records...")

        for i, medicine in enumerate(medicines):
            cleaned_medicine = {}

            # Copy only the fields we want to keep
            for field in fields_to_keep:
                if field in medicine:
                    cleaned_medicine[field] = medicine[field]

            # Count removed fields
            for field in fields_to_remove:
                if field in medicine:
                    removed_fields_count[field] += 1
                    print(f"Removing {field} from medicine {medicine.get('name', 'Unknown')}")

            # Ensure required fields have default values
            if 'notes' not in cleaned_medicine:
                cleaned_medicine['notes'] = ''
            if 'form_dosage' not in cleaned_medicine:
                cleaned_medicine['form_dosage'] = ''

            cleaned_medicines.append(cleaned_medicine)

            if i < 3:  # Debug first 3 records
                print(f"Original medicine {i+1}: {list(medicine.keys())}")
                print(f"Cleaned medicine {i+1}: {list(cleaned_medicine.keys())}")
        
        # Save cleaned data
        with open(medicines_file, 'w', encoding='utf-8') as f:
            json.dump(cleaned_medicines, f, indent=2, ensure_ascii=False)
        
        print(f"Medicines database cleaned successfully!")
        print(f"Total medicines processed: {len(cleaned_medicines)}")
        print("Removed fields:")
        for field, count in removed_fields_count.items():
            if count > 0:
                print(f"  - {field}: removed from {count} records")
        
        return True
        
    except Exception as e:
        print(f"Error cleaning medicines database: {e}")
        return False

if __name__ == '__main__':
    cleanup_medicines_database()
