{"provider": "openrouter", "openai_api_key": "********************************************************************************************************************************************************************", "openrouter_api_key": "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", "google_api_key": "", "model": "deepseek/deepseek-r1:nitro", "max_tokens": 1000, "temperature": 0.7, "enabled": true, "force_llm": true, "available_models": {"openai": [{"id": "gpt-4", "name": "GPT-4", "description": "OpenAI GPT-4 (Legacy)"}, {"id": "gpt-4o", "name": "ChatGPT-4.0", "description": "OpenAI GPT-4 Omni"}, {"id": "gpt-4-turbo", "name": "ChatGPT-4.1", "description": "OpenAI GPT-4 Turbo (Latest)"}, {"id": "gpt-3.5-turbo", "name": "GPT-3.5 Turbo", "description": "OpenAI GPT-3.5 Turbo"}], "openrouter": [{"id": "deepseek/deepseek-r1:nitro", "name": "DeepSeek R1 (0528)", "description": "DeepSeek R1 reasoning model"}, {"id": "meta-llama/llama-3.2-3b-instruct:free", "name": "Llama 3.2 3B", "description": "Meta Llama 3.2 3B (Free)"}], "google": [{"id": "gemini-2.5-pro", "name": "Gemini 2.5 Pro", "description": "Google Gemini 2.5 Pro (Latest)"}, {"id": "gemini-1.5-pro", "name": "Gemini 1.5 Pro", "description": "Google Gemini 1.5 Pro"}]}}