#!/usr/bin/env python3
"""
Detailed Functionality Testing for Hospital Pharmacy Management System
Tests specific features, data validation, and edge cases
"""

import asyncio
import time
import os
import csv
import io
from playwright.async_api import async_playwright, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>rowserContext
from datetime import datetime

class DetailedFunctionalityTests:
    def __init__(self):
        self.base_url = "http://127.0.0.1:5000"
        self.admin_credentials = {"username": "admin", "password": "@Xx123456789xX@"}
        self.test_results = []
        self.browser = None
        self.context = None
        self.page = None
        
    async def setup_browser(self):
        """Setup Playwright browser and context"""
        print("🚀 Setting up browser for detailed testing...")
        playwright = await async_playwright().start()
        
        self.browser = await playwright.chromium.launch(
            headless=False,
            slow_mo=300,
            args=['--start-maximized']
        )
        
        self.context = await self.browser.new_context(
            viewport={'width': 1920, 'height': 1080}
        )
        
        self.page = await self.context.new_page()
        self.page.set_default_timeout(30000)
        
        # Login as admin
        await self.page.goto(self.base_url)
        await self.page.wait_for_load_state('networkidle')
        await self.page.fill('input[name="username"]', self.admin_credentials["username"])
        await self.page.fill('input[name="password"]', self.admin_credentials["password"])
        await self.page.click('button[type="submit"]')
        await self.page.wait_for_load_state('networkidle')
        
        print("✅ Browser setup and login complete")
        return True
        
    async def test_medicine_management(self):
        """Test detailed medicine management functionality"""
        print("\n💊 TESTING MEDICINE MANAGEMENT")
        print("=" * 50)
        
        test_result = {
            "test_name": "Medicine Management",
            "status": "PASS",
            "details": []
        }
        
        try:
            # Navigate to medicines
            await self.page.goto(f"{self.base_url}/medicines")
            await self.page.wait_for_load_state('networkidle')
            
            # Test adding a new medicine
            await self.page.click('a[href*="/medicines/add"]')
            await self.page.wait_for_load_state('networkidle')
            
            # Fill medicine form with test data
            test_medicine_name = f"Test Medicine {datetime.now().strftime('%H%M%S')}"
            await self.page.fill('input[name="name"]', test_medicine_name)
            await self.page.select_option('select[name="supplier_id"]', index=1)  # Select first supplier
            await self.page.fill('input[name="low_stock_limit"]', "10")
            await self.page.fill('input[name="form_dosage"]', "Tablet 500mg")
            await self.page.fill('textarea[name="notes"]', "Test medicine for automation")
            
            # Submit form
            await self.page.click('button[type="submit"]')
            await self.page.wait_for_load_state('networkidle')
            
            # Check for success message
            success_message = await self.page.query_selector('.alert-success')
            if success_message:
                test_result["details"].append("✅ Medicine added successfully")
            else:
                test_result["status"] = "FAIL"
                test_result["details"].append("❌ Medicine addition failed")
            
            # Test medicine search/filtering
            await self.page.goto(f"{self.base_url}/medicines")
            await self.page.wait_for_load_state('networkidle')
            
            search_input = await self.page.query_selector('input[type="search"], input[placeholder*="search"]')
            if search_input:
                await search_input.fill(test_medicine_name)
                await self.page.wait_for_timeout(1000)  # Wait for search to process
                test_result["details"].append("✅ Medicine search functionality available")
            else:
                test_result["details"].append("⚠️ Medicine search not found")
            
            # Test medicine editing
            edit_link = await self.page.query_selector('a[href*="/medicines/edit"]')
            if edit_link:
                await edit_link.click()
                await self.page.wait_for_load_state('networkidle')
                
                # Modify medicine data
                await self.page.fill('input[name="low_stock_limit"]', "15")
                await self.page.click('button[type="submit"]')
                await self.page.wait_for_load_state('networkidle')
                
                success_message = await self.page.query_selector('.alert-success')
                if success_message:
                    test_result["details"].append("✅ Medicine edited successfully")
                else:
                    test_result["details"].append("⚠️ Medicine edit not confirmed")
            else:
                test_result["details"].append("⚠️ Medicine edit functionality not found")
                
        except Exception as e:
            test_result["status"] = "FAIL"
            test_result["details"].append(f"❌ Medicine management test error: {str(e)}")
        
        self.test_results.append(test_result)
        self.print_test_result(test_result)
        
    async def test_inventory_management(self):
        """Test inventory management and stock tracking"""
        print("\n📦 TESTING INVENTORY MANAGEMENT")
        print("=" * 50)
        
        test_result = {
            "test_name": "Inventory Management",
            "status": "PASS",
            "details": []
        }
        
        try:
            # Navigate to stores/inventory
            await self.page.goto(f"{self.base_url}/stores")
            await self.page.wait_for_load_state('networkidle')
            
            # Check inventory display
            inventory_table = await self.page.query_selector('table')
            if inventory_table:
                test_result["details"].append("✅ Inventory table displayed")
                
                # Check for stock levels
                stock_cells = await self.page.query_selector_all('td')
                if stock_cells:
                    test_result["details"].append("✅ Stock levels displayed")
                else:
                    test_result["details"].append("⚠️ Stock levels not found")
            else:
                test_result["details"].append("⚠️ Inventory table not found")
            
            # Test inventory export
            export_link = await self.page.query_selector('a[href*="/stores/export"]')
            if export_link:
                test_result["details"].append("✅ Inventory export functionality available")
            else:
                test_result["details"].append("⚠️ Inventory export not found")
            
            # Test low stock alerts
            await self.page.goto(f"{self.base_url}/reports/low-stock")
            await self.page.wait_for_load_state('networkidle')
            
            low_stock_content = await self.page.query_selector('table, .alert-info')
            if low_stock_content:
                test_result["details"].append("✅ Low stock report accessible")
            else:
                test_result["details"].append("⚠️ Low stock report not found")
                
        except Exception as e:
            test_result["status"] = "FAIL"
            test_result["details"].append(f"❌ Inventory management test error: {str(e)}")
        
        self.test_results.append(test_result)
        self.print_test_result(test_result)
        
    async def test_patient_management(self):
        """Test patient management functionality"""
        print("\n👥 TESTING PATIENT MANAGEMENT")
        print("=" * 50)
        
        test_result = {
            "test_name": "Patient Management",
            "status": "PASS",
            "details": []
        }
        
        try:
            # Navigate to patients
            await self.page.goto(f"{self.base_url}/patients")
            await self.page.wait_for_load_state('networkidle')
            
            # Test adding a new patient
            await self.page.click('a[href*="/patients/add"]')
            await self.page.wait_for_load_state('networkidle')
            
            # Fill patient form
            test_patient_name = f"Test Patient {datetime.now().strftime('%H%M%S')}"
            await self.page.fill('input[name="name"]', test_patient_name)
            await self.page.fill('input[name="file_no"]', f"P{datetime.now().strftime('%H%M%S')}")
            await self.page.select_option('select[name="gender"]', "Male")
            await self.page.fill('input[name="date_of_entry"]', "2025-07-27")
            await self.page.fill('textarea[name="medical_history"]', "Test medical history")
            
            # Submit form
            await self.page.click('button[type="submit"]')
            await self.page.wait_for_load_state('networkidle')
            
            # Check for success message
            success_message = await self.page.query_selector('.alert-success')
            if success_message:
                test_result["details"].append("✅ Patient added successfully")
            else:
                test_result["status"] = "FAIL"
                test_result["details"].append("❌ Patient addition failed")
            
            # Test patient list display
            await self.page.goto(f"{self.base_url}/patients")
            await self.page.wait_for_load_state('networkidle')
            
            patient_table = await self.page.query_selector('table')
            if patient_table:
                test_result["details"].append("✅ Patient list displayed")
            else:
                test_result["details"].append("⚠️ Patient list not found")
                
        except Exception as e:
            test_result["status"] = "FAIL"
            test_result["details"].append(f"❌ Patient management test error: {str(e)}")
        
        self.test_results.append(test_result)
        self.print_test_result(test_result)
        
    async def test_consumption_tracking(self):
        """Test consumption tracking functionality"""
        print("\n📊 TESTING CONSUMPTION TRACKING")
        print("=" * 50)
        
        test_result = {
            "test_name": "Consumption Tracking",
            "status": "PASS",
            "details": []
        }
        
        try:
            # Navigate to consumption
            await self.page.goto(f"{self.base_url}/consumption")
            await self.page.wait_for_load_state('networkidle')
            
            # Test adding consumption record
            add_button = await self.page.query_selector('a[href*="/consumption/add"], button:has-text("Add")')
            if add_button:
                await add_button.click()
                await self.page.wait_for_load_state('networkidle')
                
                # Check if form is available
                form = await self.page.query_selector('form')
                if form:
                    test_result["details"].append("✅ Consumption form available")
                else:
                    test_result["details"].append("⚠️ Consumption form not found")
            else:
                test_result["details"].append("⚠️ Add consumption button not found")
            
            # Test consumption list
            await self.page.goto(f"{self.base_url}/consumption")
            await self.page.wait_for_load_state('networkidle')
            
            consumption_table = await self.page.query_selector('table')
            if consumption_table:
                test_result["details"].append("✅ Consumption list displayed")
            else:
                test_result["details"].append("⚠️ Consumption list not found")
            
            # Test consumption reports
            await self.page.goto(f"{self.base_url}/reports/consumption")
            await self.page.wait_for_load_state('networkidle')
            
            report_content = await self.page.query_selector('table, canvas')
            if report_content:
                test_result["details"].append("✅ Consumption reports available")
            else:
                test_result["details"].append("⚠️ Consumption reports not found")
                
        except Exception as e:
            test_result["status"] = "FAIL"
            test_result["details"].append(f"❌ Consumption tracking test error: {str(e)}")
        
        self.test_results.append(test_result)
        self.print_test_result(test_result)
        
    def print_test_result(self, result):
        """Print formatted test result"""
        status_icon = "✅" if result["status"] == "PASS" else "❌"
        print(f"\n{status_icon} {result['test_name']}: {result['status']}")
        for detail in result["details"]:
            print(f"   {detail}")
            
    async def cleanup(self):
        """Cleanup browser resources"""
        if self.context:
            await self.context.close()
        if self.browser:
            await self.browser.close()
        print("\n🧹 Browser cleanup complete")

async def main():
    """Main test execution function"""
    print("🔍 DETAILED FUNCTIONALITY TESTING SUITE")
    print("=" * 80)
    print(f"Testing application at: http://127.0.0.1:5000")
    print(f"Test started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 80)
    
    suite = DetailedFunctionalityTests()
    
    try:
        # Setup browser
        await suite.setup_browser()
        
        # Run detailed tests
        await suite.test_medicine_management()
        await suite.test_inventory_management()
        await suite.test_patient_management()
        await suite.test_consumption_tracking()
        
        # Print summary
        print("\n" + "=" * 80)
        print("📊 DETAILED TEST SUMMARY")
        print("=" * 80)
        
        total_tests = len(suite.test_results)
        passed_tests = len([r for r in suite.test_results if r["status"] == "PASS"])
        failed_tests = total_tests - passed_tests
        
        print(f"Total Tests: {total_tests}")
        print(f"Passed: {passed_tests}")
        print(f"Failed: {failed_tests}")
        print(f"Success Rate: {(passed_tests/total_tests)*100:.1f}%")
        
        if failed_tests > 0:
            print("\n❌ FAILED TESTS:")
            for result in suite.test_results:
                if result["status"] == "FAIL":
                    print(f"   - {result['test_name']}")
        
    except Exception as e:
        print(f"❌ Test suite error: {str(e)}")
    
    finally:
        await suite.cleanup()

if __name__ == "__main__":
    asyncio.run(main())
