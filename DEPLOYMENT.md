# Hospital Pharmacy Management System - Deployment Guide

This guide provides comprehensive instructions for deploying the Hospital Pharmacy Management System in production environments.

## 🚀 Production Deployment Options

### Option 1: Traditional Server Deployment

#### Prerequisites
- Ubuntu 20.04+ or CentOS 8+ server
- Python 3.8+
- Nginx web server
- SSL certificate (recommended)
- Domain name (optional but recommended)

#### Step 1: Server Setup
```bash
# Update system packages
sudo apt update && sudo apt upgrade -y

# Install Python and required packages
sudo apt install python3 python3-pip python3-venv nginx supervisor -y

# Create application user
sudo useradd -m -s /bin/bash pharmacy
sudo usermod -aG sudo pharmacy
```

#### Step 2: Application Setup
```bash
# Switch to application user
sudo su - pharmacy

# Clone the application
git clone <repository-url> /home/<USER>/pharmacy-app
cd /home/<USER>/pharmacy-app

# Create virtual environment
python3 -m venv venv
source venv/bin/activate

# Install dependencies
pip install -r requirements.txt

# Install production WSGI server
pip install gunicorn
```

#### Step 3: Environment Configuration
```bash
# Create environment file
cat > /home/<USER>/pharmacy-app/.env << EOF
FLASK_ENV=production
SECRET_KEY=your-super-secret-key-here-change-this
DATABASE_PATH=/home/<USER>/pharmacy-app/data
BACKUP_PATH=/home/<USER>/backups
EOF

# Set proper permissions
chmod 600 .env
```

#### Step 4: Gunicorn Configuration
```bash
# Create Gunicorn configuration
cat > /home/<USER>/pharmacy-app/gunicorn.conf.py << EOF
bind = "127.0.0.1:8000"
workers = 4
worker_class = "sync"
worker_connections = 1000
max_requests = 1000
max_requests_jitter = 100
timeout = 30
keepalive = 2
user = "pharmacy"
group = "pharmacy"
tmp_upload_dir = None
errorlog = "/home/<USER>/pharmacy-app/logs/gunicorn_error.log"
accesslog = "/home/<USER>/pharmacy-app/logs/gunicorn_access.log"
loglevel = "info"
EOF

# Create logs directory
mkdir -p /home/<USER>/pharmacy-app/logs
```

#### Step 5: Supervisor Configuration
```bash
# Create supervisor configuration (as root)
sudo cat > /etc/supervisor/conf.d/pharmacy.conf << EOF
[program:pharmacy]
command=/home/<USER>/pharmacy-app/venv/bin/gunicorn -c gunicorn.conf.py app:app
directory=/home/<USER>/pharmacy-app
user=pharmacy
autostart=true
autorestart=true
redirect_stderr=true
stdout_logfile=/home/<USER>/pharmacy-app/logs/supervisor.log
EOF

# Reload supervisor
sudo supervisorctl reread
sudo supervisorctl update
sudo supervisorctl start pharmacy
```

#### Step 6: Nginx Configuration
```bash
# Create Nginx site configuration
sudo cat > /etc/nginx/sites-available/pharmacy << EOF
server {
    listen 80;
    server_name your-domain.com;  # Replace with your domain
    
    # Redirect HTTP to HTTPS
    return 301 https://\$server_name\$request_uri;
}

server {
    listen 443 ssl http2;
    server_name your-domain.com;  # Replace with your domain
    
    # SSL Configuration (replace with your certificate paths)
    ssl_certificate /path/to/your/certificate.crt;
    ssl_certificate_key /path/to/your/private.key;
    
    # Security headers
    add_header X-Frame-Options DENY;
    add_header X-Content-Type-Options nosniff;
    add_header X-XSS-Protection "1; mode=block";
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains";
    
    # Application proxy
    location / {
        proxy_pass http://127.0.0.1:8000;
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;
        proxy_redirect off;
    }
    
    # Static files (if serving directly)
    location /static {
        alias /home/<USER>/pharmacy-app/static;
        expires 1y;
        add_header Cache-Control "public, immutable";
    }
    
    # Security: Block access to sensitive files
    location ~ /\. {
        deny all;
    }
    
    location ~ /(data|logs|venv) {
        deny all;
    }
}
EOF

# Enable the site
sudo ln -s /etc/nginx/sites-available/pharmacy /etc/nginx/sites-enabled/
sudo nginx -t
sudo systemctl reload nginx
```

### Option 2: Docker Deployment

#### Dockerfile
```dockerfile
FROM python:3.9-slim

WORKDIR /app

# Install system dependencies
RUN apt-get update && apt-get install -y \
    gcc \
    && rm -rf /var/lib/apt/lists/*

# Copy requirements and install Python dependencies
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt
RUN pip install gunicorn

# Copy application code
COPY . .

# Create non-root user
RUN useradd -m -u 1000 pharmacy && chown -R pharmacy:pharmacy /app
USER pharmacy

# Create necessary directories
RUN mkdir -p data logs

EXPOSE 8000

CMD ["gunicorn", "--bind", "0.0.0.0:8000", "--workers", "4", "app:app"]
```

#### Docker Compose
```yaml
version: '3.8'

services:
  pharmacy-app:
    build: .
    ports:
      - "8000:8000"
    volumes:
      - ./data:/app/data
      - ./logs:/app/logs
    environment:
      - FLASK_ENV=production
      - SECRET_KEY=your-super-secret-key-here
    restart: unless-stopped
    
  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
      - ./ssl:/etc/nginx/ssl
    depends_on:
      - pharmacy-app
    restart: unless-stopped
```

## 🔒 Security Considerations

### 1. Password Security
```python
# Replace simple password checking with proper hashing
from werkzeug.security import generate_password_hash, check_password_hash

# When creating users:
password_hash = generate_password_hash(password)

# When validating:
if check_password_hash(user['password_hash'], provided_password):
    # Login successful
```

### 2. Environment Variables
```bash
# Use environment variables for sensitive data
export SECRET_KEY="your-super-secret-key"
export DATABASE_ENCRYPTION_KEY="your-encryption-key"
export ADMIN_EMAIL="<EMAIL>"
```

### 3. Database Security
```python
# Consider migrating to PostgreSQL for production
DATABASE_URL = "postgresql://user:password@localhost/pharmacy_db"

# Enable database encryption for sensitive data
# Implement regular automated backups
```

### 4. Network Security
- Use HTTPS only (SSL/TLS certificates)
- Configure firewall rules
- Implement rate limiting
- Use VPN for administrative access

## 📊 Database Migration (Production)

### From JSON to PostgreSQL
```python
# migration_script.py
import json
import psycopg2
from datetime import datetime

def migrate_to_postgresql():
    # Connect to PostgreSQL
    conn = psycopg2.connect(
        host="localhost",
        database="pharmacy_db",
        user="pharmacy_user",
        password="secure_password"
    )
    
    # Create tables
    create_tables(conn)
    
    # Migrate data
    migrate_users(conn)
    migrate_medicines(conn)
    migrate_patients(conn)
    # ... migrate other entities
    
    conn.close()

def create_tables(conn):
    cursor = conn.cursor()
    
    # Users table
    cursor.execute("""
        CREATE TABLE IF NOT EXISTS users (
            id SERIAL PRIMARY KEY,
            username VARCHAR(50) UNIQUE NOT NULL,
            password_hash VARCHAR(255) NOT NULL,
            role VARCHAR(20) NOT NULL,
            department_id INTEGER,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
    """)
    
    # Add other table creation statements...
    
    conn.commit()
    cursor.close()
```

## 🔄 Backup Strategy

### Automated Backup Script
```bash
#!/bin/bash
# backup.sh

BACKUP_DIR="/home/<USER>/backups"
APP_DIR="/home/<USER>/pharmacy-app"
DATE=$(date +%Y%m%d_%H%M%S)

# Create backup directory
mkdir -p "$BACKUP_DIR/$DATE"

# Backup data files
cp -r "$APP_DIR/data" "$BACKUP_DIR/$DATE/"

# Backup application code
tar -czf "$BACKUP_DIR/$DATE/app_backup.tar.gz" -C "$APP_DIR" . \
    --exclude='venv' --exclude='logs' --exclude='__pycache__'

# Backup logs
cp -r "$APP_DIR/logs" "$BACKUP_DIR/$DATE/"

# Remove backups older than 30 days
find "$BACKUP_DIR" -type d -mtime +30 -exec rm -rf {} +

echo "Backup completed: $BACKUP_DIR/$DATE"
```

### Crontab Entry
```bash
# Add to crontab (crontab -e)
0 2 * * * /home/<USER>/scripts/backup.sh
```

## 📈 Monitoring and Logging

### Log Configuration
```python
import logging
from logging.handlers import RotatingFileHandler

if not app.debug:
    file_handler = RotatingFileHandler(
        'logs/pharmacy.log', 
        maxBytes=10240000, 
        backupCount=10
    )
    file_handler.setFormatter(logging.Formatter(
        '%(asctime)s %(levelname)s: %(message)s [in %(pathname)s:%(lineno)d]'
    ))
    file_handler.setLevel(logging.INFO)
    app.logger.addHandler(file_handler)
    app.logger.setLevel(logging.INFO)
```

### Health Check Endpoint
```python
@app.route('/health')
def health_check():
    return jsonify({
        'status': 'healthy',
        'timestamp': datetime.now().isoformat(),
        'version': '1.0.0'
    })
```

## 🚀 Performance Optimization

### 1. Caching
```python
from flask_caching import Cache

cache = Cache(app, config={'CACHE_TYPE': 'simple'})

@cache.memoize(timeout=300)
def get_cached_medicines():
    return get_medicines()
```

### 2. Database Optimization
- Implement database indexing
- Use connection pooling
- Optimize queries
- Implement pagination for large datasets

### 3. Static File Serving
- Use CDN for static assets
- Enable gzip compression
- Set proper cache headers

## 🔧 Maintenance

### Regular Tasks
1. **Daily**: Check application logs
2. **Weekly**: Review backup integrity
3. **Monthly**: Update dependencies
4. **Quarterly**: Security audit

### Update Procedure
```bash
# 1. Backup current version
./backup.sh

# 2. Pull latest changes
git pull origin main

# 3. Update dependencies
source venv/bin/activate
pip install -r requirements.txt

# 4. Restart application
sudo supervisorctl restart pharmacy

# 5. Verify deployment
curl -f http://localhost:8000/health
```

## 📞 Support and Troubleshooting

### Common Issues
1. **Application won't start**: Check logs in `/home/<USER>/pharmacy-app/logs/`
2. **Database errors**: Verify file permissions and disk space
3. **Performance issues**: Monitor system resources and optimize queries

### Log Locations
- Application logs: `/home/<USER>/pharmacy-app/logs/`
- Nginx logs: `/var/log/nginx/`
- System logs: `/var/log/syslog`

---

**Note**: This deployment guide provides a foundation for production deployment. Always customize security settings, backup strategies, and monitoring based on your specific requirements and infrastructure.
