{% extends "base.html" %}

{% block title %}Profile - Hospital Pharmacy Management System{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1><i class="bi bi-person-circle"></i> User Profile</h1>
    <a href="{{ url_for('settings.index') }}" class="btn btn-outline-secondary">
        <i class="bi bi-arrow-left"></i> Back to Settings
    </a>
</div>

<div class="row">
    <div class="col-md-4">
        <!-- Profile Picture -->
        <div class="card mb-4">
            <div class="card-body text-center">
                <div class="avatar bg-primary text-white rounded-circle d-flex align-items-center justify-content-center mx-auto mb-3" style="width: 120px; height: 120px; font-size: 3rem;">
                    <i class="bi bi-person"></i>
                </div>
                <h5>{{ session.username }}</h5>
                <p class="text-muted">
                    <span class="badge {% if session.role == 'admin' %}bg-danger{% else %}bg-primary{% endif %}">
                        {{ session.role|title }}
                    </span>
                </p>
                {% if session.role == 'admin' %}
                <button class="btn btn-outline-primary btn-sm" onclick="uploadPhoto()">
                    <i class="bi bi-camera"></i> Change Photo
                </button>
                {% endif %}
            </div>
        </div>
        
        <!-- Quick Stats -->
        <div class="card">
            <div class="card-header">
                <h6 class="card-title mb-0"><i class="bi bi-graph-up"></i> Your Activity</h6>
            </div>
            <div class="card-body">
                <div class="text-center mb-3">
                    <h4 class="text-primary">{{ user_stats.total_logins or 'N/A' }}</h4>
                    <p class="text-muted mb-0">Total Logins</p>
                </div>
                <div class="text-center mb-3">
                    <h4 class="text-success">{{ user_stats.last_login or 'Current' }}</h4>
                    <p class="text-muted mb-0">Last Login</p>
                </div>
                {% if session.role != 'admin' %}
                <div class="text-center">
                    <h4 class="text-info">{{ user_stats.department_activity or 'N/A' }}</h4>
                    <p class="text-muted mb-0">Department Activity</p>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
    
    <div class="col-md-8">
        <!-- Profile Information -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="card-title mb-0"><i class="bi bi-person-badge"></i> Profile Information</h5>
            </div>
            <div class="card-body">
                <form method="POST" novalidate>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="username" class="form-label">Username</label>
                                <input type="text" class="form-control" id="username" name="username" 
                                       value="{{ session.username }}" readonly>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="role" class="form-label">Role</label>
                                <input type="text" class="form-control" id="role" name="role" 
                                       value="{{ session.role|title }}" readonly>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="name" class="form-label">Full Name</label>
                                <input type="text" class="form-control" id="name" name="name" 
                                       value="{{ user_info.name or session.username }}" 
                                       {% if session.role != 'admin' %}readonly{% endif %}>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="email" class="form-label">Email</label>
                                <input type="email" class="form-control" id="email" name="email" 
                                       value="{{ user_info.email or '' }}" 
                                       {% if session.role != 'admin' %}readonly{% endif %}>
                            </div>
                        </div>
                    </div>
                    
                    {% if session.department_id %}
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="department" class="form-label">Department</label>
                                <input type="text" class="form-control" id="department" name="department" 
                                       value="{{ user_department.name if user_department else 'Unknown' }}" readonly>
                            </div>
                        </div>
                    </div>
                    {% endif %}
                    
                    {% if session.role == 'admin' %}
                    <div class="d-flex justify-content-end">
                        <button type="submit" class="btn btn-primary">
                            <i class="bi bi-check-circle"></i> Update Profile
                        </button>
                    </div>
                    {% endif %}
                </form>
            </div>
        </div>
        
        <!-- Change Password -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="card-title mb-0"><i class="bi bi-shield-lock"></i> Change Password</h5>
            </div>
            <div class="card-body">
                <form method="POST" action="{{ url_for('auth.change_password') }}" novalidate>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="current_password" class="form-label">Current Password</label>
                                <input type="password" class="form-control" id="current_password" name="current_password" required>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="new_password" class="form-label">New Password</label>
                                <input type="password" class="form-control" id="new_password" name="new_password" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="confirm_password" class="form-label">Confirm New Password</label>
                                <input type="password" class="form-control" id="confirm_password" name="confirm_password" required>
                            </div>
                        </div>
                    </div>
                    <div class="d-flex justify-content-end">
                        <button type="submit" class="btn btn-warning">
                            <i class="bi bi-key"></i> Change Password
                        </button>
                    </div>
                </form>
            </div>
        </div>
        
        <!-- Preferences -->
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0"><i class="bi bi-sliders"></i> Preferences</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label">Theme Preference</label>
                            <div class="d-flex gap-2">
                                <button class="btn btn-outline-secondary" onclick="setTheme('light')">
                                    <i class="bi bi-sun"></i> Light
                                </button>
                                <button class="btn btn-outline-secondary" onclick="setTheme('dark')">
                                    <i class="bi bi-moon"></i> Dark
                                </button>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label">Notifications</label>
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="emailNotifications" checked>
                                <label class="form-check-label" for="emailNotifications">
                                    Email notifications for low stock
                                </label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="systemAlerts" checked>
                                <label class="form-check-label" for="systemAlerts">
                                    System alerts and updates
                                </label>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Photo Upload Modal -->
<div class="modal fade" id="photoModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title"><i class="bi bi-camera"></i> Upload Profile Photo</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form enctype="multipart/form-data">
                    <div class="mb-3">
                        <label for="photoFile" class="form-label">Select Photo</label>
                        <input type="file" class="form-control" id="photoFile" accept="image/*">
                        <div class="form-text">Supported formats: JPG, PNG, GIF. Max size: 2MB</div>
                    </div>
                    <div class="text-center">
                        <img id="photoPreview" src="#" alt="Preview" class="img-thumbnail" style="max-width: 200px; display: none;">
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary" onclick="savePhoto()">
                    <i class="bi bi-upload"></i> Upload Photo
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
function uploadPhoto() {
    new bootstrap.Modal(document.getElementById('photoModal')).show();
}

function savePhoto() {
    // TODO: Implement photo upload
    alert('Photo upload functionality coming soon!');
}

function setTheme(theme) {
    document.documentElement.setAttribute('data-bs-theme', theme);
    localStorage.setItem('theme', theme);
    
    // Update button states
    const buttons = document.querySelectorAll('[onclick*="setTheme"]');
    buttons.forEach(button => {
        button.classList.remove('btn-secondary');
        button.classList.add('btn-outline-secondary');
    });
    
    event.target.classList.remove('btn-outline-secondary');
    event.target.classList.add('btn-secondary');
}

// Photo preview
document.getElementById('photoFile').addEventListener('change', function(e) {
    const file = e.target.files[0];
    if (file) {
        const reader = new FileReader();
        reader.onload = function(e) {
            const preview = document.getElementById('photoPreview');
            preview.src = e.target.result;
            preview.style.display = 'block';
        };
        reader.readAsDataURL(file);
    }
});

// Highlight current theme
document.addEventListener('DOMContentLoaded', function() {
    const currentTheme = localStorage.getItem('theme') || 'light';
    const buttons = document.querySelectorAll('[onclick*="setTheme"]');
    
    buttons.forEach(button => {
        if (button.onclick.toString().includes(currentTheme)) {
            button.classList.remove('btn-outline-secondary');
            button.classList.add('btn-secondary');
        }
    });
});
</script>
{% endblock %}
