<!DOCTYPE html>
<html lang="en" data-bs-theme="light">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}ALORF MEDICATION SYSTEM{% endblock %}</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.1/font/bootstrap-icons.css" rel="stylesheet">
    <!-- Custom CSS -->
    <link href="{{ url_for('static', filename='css/style.css') }}" rel="stylesheet">
    
    {% block extra_css %}{% endblock %}
</head>
<body>
    {% if session.user_id %}
    <!-- Mobile Toggle Button -->
    <button class="sidebar-toggle" id="sidebarToggle">
        <i class="bi bi-list"></i>
    </button>

    <!-- Sidebar Overlay for Mobile -->
    <div class="sidebar-overlay" id="sidebarOverlay"></div>

    <!-- Left Sidebar -->
    <nav class="sidebar" id="sidebar">
        <!-- Sidebar Header -->
        <div class="sidebar-header">
            <a href="{{ url_for('dashboard.index') }}" class="sidebar-brand">
                <i class="bi bi-hospital"></i>
                <span>ALORF HOSPITAL</span>
            </a>
        </div>

        <!-- Sidebar Navigation -->
        <ul class="sidebar-nav">
            <li class="sidebar-nav-item">
                <a class="sidebar-nav-link {% if request.endpoint == 'dashboard.index' %}active{% endif %}"
                   href="{{ url_for('dashboard.index') }}">
                    <i class="bi bi-speedometer2"></i>
                    <span>Dashboard</span>
                </a>
            </li>
            <li class="sidebar-nav-item">
                <a class="sidebar-nav-link {% if request.endpoint and 'medicines' in request.endpoint %}active{% endif %}"
                   href="{{ url_for('medicines.index') }}">
                    <i class="bi bi-capsule"></i>
                    <span>Medicines</span>
                </a>
            </li>
            <li class="sidebar-nav-item">
                <a class="sidebar-nav-link {% if request.endpoint and 'patients' in request.endpoint %}active{% endif %}"
                   href="{{ url_for('patients.index') }}">
                    <i class="bi bi-person"></i>
                    <span>Patients</span>
                </a>
            </li>
            <li class="sidebar-nav-item">
                <a class="sidebar-nav-link {% if request.endpoint and 'suppliers' in request.endpoint %}active{% endif %}"
                   href="{{ url_for('suppliers.index') }}">
                    <i class="bi bi-truck"></i>
                    <span>Suppliers</span>
                </a>
            </li>
            {% if session.role == 'admin' %}
            <li class="sidebar-nav-item">
                <a class="sidebar-nav-link {% if request.endpoint and 'departments' in request.endpoint %}active{% endif %}"
                   href="{{ url_for('departments.index') }}">
                    <i class="bi bi-building"></i>
                    <span>Departments</span>
                </a>
            </li>
            {% endif %}
            <li class="sidebar-nav-item">
                <a class="sidebar-nav-link {% if request.endpoint and 'stores' in request.endpoint %}active{% endif %}"
                   href="{{ url_for('stores.index') }}">
                    <i class="bi bi-box"></i>
                    <span>Stores</span>
                </a>
            </li>
            {% if session.role == 'admin' %}
            <li class="sidebar-nav-item">
                <a class="sidebar-nav-link {% if request.endpoint and 'purchases' in request.endpoint %}active{% endif %}"
                   href="{{ url_for('purchases.index') }}">
                    <i class="bi bi-cart-plus"></i>
                    <span>Purchases</span>
                </a>
            </li>
            {% endif %}
            <li class="sidebar-nav-item">
                <a class="sidebar-nav-link {% if request.endpoint and 'consumption' in request.endpoint %}active{% endif %}"
                   href="{{ url_for('consumption.index') }}">
                    <i class="bi bi-cart-dash"></i>
                    <span>Consumption</span>
                </a>
            </li>
            <li class="sidebar-nav-item">
                <a class="sidebar-nav-link {% if request.endpoint and 'reports' in request.endpoint %}active{% endif %}"
                   href="{{ url_for('reports.index') }}">
                    <i class="bi bi-graph-up"></i>
                    <span>Reports</span>
                </a>
            </li>
            {% if session.role == 'admin' %}
            <li class="sidebar-nav-item">
                <a class="sidebar-nav-link {% if request.endpoint and 'chatbot' in request.endpoint %}active{% endif %}"
                   href="{{ url_for('chatbot.index') }}">
                    <i class="bi bi-robot"></i>
                    <span>AI Chatbot</span>
                </a>
            </li>
            {% endif %}
        </ul>

        <!-- Sidebar Footer with User Menu -->
        <div class="sidebar-footer">
            <div class="sidebar-user">
                <div class="dropdown">
                    <button class="sidebar-user-dropdown dropdown-toggle" type="button"
                            data-bs-toggle="dropdown" aria-expanded="false">
                        <i class="bi bi-person-circle"></i>
                        <span>{{ session.username }}</span>
                    </button>
                    <ul class="dropdown-menu dropdown-menu-end">
                        <li>
                            <a class="dropdown-item" href="{{ url_for('auth.profile') }}">
                                <i class="bi bi-person"></i> Profile
                            </a>
                        </li>
                        <li>
                            <a class="dropdown-item" href="{{ url_for('settings.index') }}">
                                <i class="bi bi-gear"></i> Settings
                            </a>
                        </li>
                        <li><hr class="dropdown-divider"></li>
                        <li>
                            <a class="dropdown-item" href="#" onclick="toggleTheme()">
                                <i class="bi bi-moon"></i> <span id="theme-text">Dark Mode</span>
                            </a>
                        </li>
                        <li><hr class="dropdown-divider"></li>
                        <li>
                            <a class="dropdown-item" href="{{ url_for('auth.logout') }}">
                                <i class="bi bi-box-arrow-right"></i> Logout
                            </a>
                        </li>
                    </ul>
                </div>
            </div>
        </div>
    </nav>
    {% endif %}
    
    <!-- Main Content Area -->
    <div class="{% if session.user_id %}main-content{% endif %}">
        <div class="content-wrapper">
            <!-- Flash Messages -->
            {% with messages = get_flashed_messages(with_categories=true) %}
                {% if messages %}
                    <div class="mb-3">
                        {% for category, message in messages %}
                            <div class="alert alert-{{ 'danger' if category == 'error' else category }} alert-dismissible fade show" role="alert">
                                {{ message }}
                                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                            </div>
                        {% endfor %}
                    </div>
                {% endif %}
            {% endwith %}

            <!-- Page Content -->
            <main>
                {% block content %}{% endblock %}
            </main>
        </div>
    </div>
    
    <!-- Footer -->
    <footer class="bg-light text-center text-muted py-3 mt-5">
        <div class="container">
            <p>&copy; {{ current_year() }} ALORF MEDICATION SYSTEM.
               Developed by <strong>Waleed Mohamed</strong> | Hospital: <strong>ALORF HOSPITAL</strong></p>
        </div>
    </footer>
    
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Custom JS -->
    <script src="{{ url_for('static', filename='js/app.js') }}"></script>
    
    {% block extra_js %}{% endblock %}
</body>
</html>
