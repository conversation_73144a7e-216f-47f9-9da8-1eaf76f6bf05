#!/usr/bin/env python3
"""
Comprehensive Playwright Testing Suite for Hospital Pharmacy Management System
Tests all major functionality including authentication, department creation, 
import/export, reports, and full application features.
"""

import asyncio
import time
import os
import csv
import io
from playwright.async_api import async_playwright, <PERSON>, <PERSON><PERSON>er, <PERSON><PERSON><PERSON><PERSON>ontext
from datetime import datetime

class PharmacyTestSuite:
    def __init__(self):
        self.base_url = "http://127.0.0.1:5000"
        self.admin_credentials = {"username": "admin", "password": "@Xx123456789xX@"}
        self.test_results = []
        self.browser = None
        self.context = None
        self.page = None
        
    async def setup_browser(self):
        """Setup Playwright browser and context"""
        print("🚀 Setting up Playwright browser...")
        playwright = await async_playwright().start()
        
        # Launch browser with options for better testing
        self.browser = await playwright.chromium.launch(
            headless=False,  # Set to True for headless testing
            slow_mo=500,     # Slow down operations for visibility
            args=['--start-maximized']
        )
        
        # Create context with viewport
        self.context = await self.browser.new_context(
            viewport={'width': 1920, 'height': 1080}
        )
        
        # Create page
        self.page = await self.context.new_page()
        
        # Set default timeout
        self.page.set_default_timeout(30000)  # 30 seconds
        
        print("✅ Browser setup complete")
        return True
        
    async def login_as_admin(self):
        """Login as admin user"""
        print("🔐 Logging in as admin...")
        
        try:
            # Navigate to login page
            await self.page.goto(self.base_url)
            await self.page.wait_for_load_state('networkidle')
            
            # Fill login form
            await self.page.fill('input[name="username"]', self.admin_credentials["username"])
            await self.page.fill('input[name="password"]', self.admin_credentials["password"])
            
            # Submit form
            await self.page.click('button[type="submit"]')
            await self.page.wait_for_load_state('networkidle')
            
            # Verify login success by checking for dashboard
            if "/dashboard" in self.page.url:
                print("✅ Admin login successful")
                return True
            else:
                print("❌ Admin login failed")
                return False
                
        except Exception as e:
            print(f"❌ Login error: {str(e)}")
            return False
    
    async def test_authentication(self):
        """Test authentication functionality"""
        print("\n🔐 TESTING AUTHENTICATION")
        print("=" * 50)
        
        test_result = {
            "test_name": "Authentication",
            "status": "PASS",
            "details": []
        }
        
        try:
            # Test admin login
            login_success = await self.login_as_admin()
            if login_success:
                test_result["details"].append("✅ Admin login successful")
            else:
                test_result["status"] = "FAIL"
                test_result["details"].append("❌ Admin login failed")
            
            # Test logout
            await self.page.click('a[href*="logout"]')
            await self.page.wait_for_load_state('networkidle')
            
            if "/auth/login" in self.page.url:
                test_result["details"].append("✅ Logout successful")
            else:
                test_result["status"] = "FAIL"
                test_result["details"].append("❌ Logout failed")
                
            # Login again for subsequent tests
            await self.login_as_admin()
            
        except Exception as e:
            test_result["status"] = "FAIL"
            test_result["details"].append(f"❌ Authentication test error: {str(e)}")
        
        self.test_results.append(test_result)
        self.print_test_result(test_result)
        
    async def test_department_creation(self):
        """Test department creation and automatic store/user creation"""
        print("\n🏢 TESTING DEPARTMENT CREATION")
        print("=" * 50)
        
        test_result = {
            "test_name": "Department Creation",
            "status": "PASS",
            "details": []
        }
        
        try:
            # Navigate to departments
            await self.page.goto(f"{self.base_url}/departments")
            await self.page.wait_for_load_state('networkidle')
            
            # Click Add Department
            await self.page.click('a[href*="/departments/add"]')
            await self.page.wait_for_load_state('networkidle')
            
            # Fill department form
            test_dept_name = f"Test Department {datetime.now().strftime('%H%M%S')}"
            await self.page.fill('input[name="name"]', test_dept_name)
            await self.page.fill('input[name="responsible_person"]', "Dr. Test Person")
            await self.page.fill('input[name="telephone"]', "******-TEST")
            await self.page.fill('textarea[name="notes"]', "Test department for automation")
            
            # Submit form
            await self.page.click('button[type="submit"]')
            await self.page.wait_for_load_state('networkidle')
            
            # Check for success message
            success_messages = await self.page.query_selector_all('.alert-success')
            if success_messages:
                test_result["details"].append("✅ Department created successfully")
                
                # Check if user credentials are displayed
                info_messages = await self.page.query_selector_all('.alert-info')
                if info_messages:
                    test_result["details"].append("✅ Department user created automatically")
                else:
                    test_result["details"].append("⚠️ Department user creation not confirmed")
            else:
                test_result["status"] = "FAIL"
                test_result["details"].append("❌ Department creation failed")
            
            # Verify department appears in list
            await self.page.goto(f"{self.base_url}/departments")
            await self.page.wait_for_load_state('networkidle')
            
            department_found = await self.page.query_selector(f'text="{test_dept_name}"')
            if department_found:
                test_result["details"].append("✅ Department appears in departments list")
            else:
                test_result["status"] = "FAIL"
                test_result["details"].append("❌ Department not found in list")
            
            # Check if corresponding store was created
            await self.page.goto(f"{self.base_url}/stores")
            await self.page.wait_for_load_state('networkidle')
            
            store_found = await self.page.query_selector(f'text="{test_dept_name} Store"')
            if store_found:
                test_result["details"].append("✅ Corresponding store created automatically")
            else:
                test_result["status"] = "FAIL"
                test_result["details"].append("❌ Corresponding store not found")
                
        except Exception as e:
            test_result["status"] = "FAIL"
            test_result["details"].append(f"❌ Department creation test error: {str(e)}")
        
        self.test_results.append(test_result)
        self.print_test_result(test_result)
        
    async def test_import_export_functions(self):
        """Test import/export functionality across all modules"""
        print("\n📁 TESTING IMPORT/EXPORT FUNCTIONS")
        print("=" * 50)
        
        test_result = {
            "test_name": "Import/Export Functions",
            "status": "PASS",
            "details": []
        }
        
        try:
            # Test CSV template downloads
            modules_to_test = [
                ("medicines", "Medicines"),
                ("patients", "Patients"),
                ("suppliers", "Suppliers"),
                ("departments", "Departments")
            ]
            
            for module, display_name in modules_to_test:
                try:
                    # Navigate to module
                    await self.page.goto(f"{self.base_url}/{module}")
                    await self.page.wait_for_load_state('networkidle')
                    
                    # Look for template download link
                    template_link = await self.page.query_selector('a[href*="template/download"]')
                    if template_link:
                        test_result["details"].append(f"✅ {display_name} template download available")
                    else:
                        test_result["details"].append(f"⚠️ {display_name} template download not found")
                    
                    # Look for import functionality
                    import_form = await self.page.query_selector('input[type="file"][name="csv_file"]')
                    if import_form:
                        test_result["details"].append(f"✅ {display_name} import functionality available")
                    else:
                        test_result["details"].append(f"⚠️ {display_name} import functionality not found")
                        
                except Exception as e:
                    test_result["details"].append(f"❌ {display_name} import/export test error: {str(e)}")
            
            # Test inventory export
            try:
                await self.page.goto(f"{self.base_url}/stores")
                await self.page.wait_for_load_state('networkidle')
                
                export_link = await self.page.query_selector('a[href*="/stores/export"]')
                if export_link:
                    test_result["details"].append("✅ Inventory export functionality available")
                else:
                    test_result["details"].append("⚠️ Inventory export functionality not found")
                    
            except Exception as e:
                test_result["details"].append(f"❌ Inventory export test error: {str(e)}")
                
        except Exception as e:
            test_result["status"] = "FAIL"
            test_result["details"].append(f"❌ Import/Export test error: {str(e)}")
        
        self.test_results.append(test_result)
        self.print_test_result(test_result)

    async def test_reports_section(self):
        """Test all report types and functionality"""
        print("\n📊 TESTING REPORTS SECTION")
        print("=" * 50)

        test_result = {
            "test_name": "Reports Section",
            "status": "PASS",
            "details": []
        }

        try:
            # Navigate to reports
            await self.page.goto(f"{self.base_url}/reports")
            await self.page.wait_for_load_state('networkidle')

            # Test different report types
            report_types = [
                ("consumption", "Consumption Report"),
                ("supplier", "Supplier Report"),
                ("purchase", "Purchase Report"),
                ("inventory", "Inventory Report"),
                ("low-stock", "Low Stock Report"),
                ("suggested-purchase", "Suggested Purchase Report")
            ]

            for report_url, report_name in report_types:
                try:
                    await self.page.goto(f"{self.base_url}/reports/{report_url}")
                    await self.page.wait_for_load_state('networkidle')

                    # Check if page loads without error
                    error_element = await self.page.query_selector('.alert-danger')
                    if not error_element:
                        test_result["details"].append(f"✅ {report_name} loads successfully")

                        # Check for data tables or charts
                        table = await self.page.query_selector('table')
                        chart = await self.page.query_selector('canvas')
                        if table or chart:
                            test_result["details"].append(f"✅ {report_name} displays data")
                        else:
                            test_result["details"].append(f"⚠️ {report_name} no data displayed")
                    else:
                        test_result["status"] = "FAIL"
                        test_result["details"].append(f"❌ {report_name} shows error")

                except Exception as e:
                    test_result["details"].append(f"❌ {report_name} test error: {str(e)}")

            # Test report filtering (if available)
            await self.page.goto(f"{self.base_url}/reports/consumption")
            await self.page.wait_for_load_state('networkidle')

            filter_form = await self.page.query_selector('form')
            if filter_form:
                test_result["details"].append("✅ Report filtering functionality available")
            else:
                test_result["details"].append("⚠️ Report filtering not found")

        except Exception as e:
            test_result["status"] = "FAIL"
            test_result["details"].append(f"❌ Reports section test error: {str(e)}")

        self.test_results.append(test_result)
        self.print_test_result(test_result)

    async def test_full_application_features(self):
        """Test all major application features"""
        print("\n🏥 TESTING FULL APPLICATION FEATURES")
        print("=" * 50)

        test_result = {
            "test_name": "Full Application Features",
            "status": "PASS",
            "details": []
        }

        try:
            # Test navigation to all major sections
            sections = [
                ("dashboard", "Dashboard"),
                ("medicines", "Medicines Management"),
                ("patients", "Patients Management"),
                ("suppliers", "Suppliers Management"),
                ("departments", "Departments Management"),
                ("stores", "Stores/Inventory Management"),
                ("purchases", "Purchases Management"),
                ("consumption", "Consumption Tracking"),
                ("transfers", "Transfers Management"),
                ("settings", "Settings"),
                ("chatbot", "AI Chatbot")
            ]

            for section_url, section_name in sections:
                try:
                    await self.page.goto(f"{self.base_url}/{section_url}")
                    await self.page.wait_for_load_state('networkidle')

                    # Check if page loads without error
                    error_element = await self.page.query_selector('.alert-danger')
                    if not error_element:
                        test_result["details"].append(f"✅ {section_name} accessible")

                        # Check for main content
                        content = await self.page.query_selector('main, .container, .content')
                        if content:
                            test_result["details"].append(f"✅ {section_name} displays content")
                        else:
                            test_result["details"].append(f"⚠️ {section_name} content not found")
                    else:
                        test_result["status"] = "FAIL"
                        test_result["details"].append(f"❌ {section_name} shows error")

                except Exception as e:
                    test_result["details"].append(f"❌ {section_name} test error: {str(e)}")

            # Test form submissions (add new medicine)
            try:
                await self.page.goto(f"{self.base_url}/medicines/add")
                await self.page.wait_for_load_state('networkidle')

                form = await self.page.query_selector('form')
                if form:
                    test_result["details"].append("✅ Add medicine form available")
                else:
                    test_result["details"].append("⚠️ Add medicine form not found")

            except Exception as e:
                test_result["details"].append(f"❌ Medicine form test error: {str(e)}")

        except Exception as e:
            test_result["status"] = "FAIL"
            test_result["details"].append(f"❌ Full application test error: {str(e)}")

        self.test_results.append(test_result)
        self.print_test_result(test_result)

    async def test_error_handling(self):
        """Test error scenarios and edge cases"""
        print("\n⚠️ TESTING ERROR HANDLING")
        print("=" * 50)

        test_result = {
            "test_name": "Error Handling",
            "status": "PASS",
            "details": []
        }

        try:
            # Test invalid URLs
            invalid_urls = [
                "/nonexistent",
                "/medicines/invalid-id",
                "/departments/999999"
            ]

            for url in invalid_urls:
                try:
                    await self.page.goto(f"{self.base_url}{url}")
                    await self.page.wait_for_load_state('networkidle')

                    # Check for 404 page or error handling
                    error_page = await self.page.query_selector('h1:has-text("404")')
                    error_message = await self.page.query_selector('.alert-danger')

                    if error_page or error_message:
                        test_result["details"].append(f"✅ Error handling for {url}")
                    else:
                        test_result["details"].append(f"⚠️ No error handling for {url}")

                except Exception as e:
                    test_result["details"].append(f"❌ Error test for {url}: {str(e)}")

            # Test form validation (empty required fields)
            try:
                await self.page.goto(f"{self.base_url}/medicines/add")
                await self.page.wait_for_load_state('networkidle')

                # Submit empty form
                await self.page.click('button[type="submit"]')
                await self.page.wait_for_load_state('networkidle')

                # Check for validation messages
                validation_error = await self.page.query_selector('.alert-danger, .is-invalid')
                if validation_error:
                    test_result["details"].append("✅ Form validation working")
                else:
                    test_result["details"].append("⚠️ Form validation not detected")

            except Exception as e:
                test_result["details"].append(f"❌ Form validation test error: {str(e)}")

        except Exception as e:
            test_result["status"] = "FAIL"
            test_result["details"].append(f"❌ Error handling test error: {str(e)}")

        self.test_results.append(test_result)
        self.print_test_result(test_result)

    def print_test_result(self, result):
        """Print formatted test result"""
        status_icon = "✅" if result["status"] == "PASS" else "❌"
        print(f"\n{status_icon} {result['test_name']}: {result['status']}")
        for detail in result["details"]:
            print(f"   {detail}")
            
    async def cleanup(self):
        """Cleanup browser resources"""
        if self.context:
            await self.context.close()
        if self.browser:
            await self.browser.close()
        print("\n🧹 Browser cleanup complete")

async def main():
    """Main test execution function"""
    print("🎯 COMPREHENSIVE HOSPITAL PHARMACY TESTING SUITE")
    print("=" * 80)
    print(f"Testing application at: http://127.0.0.1:5000")
    print(f"Test started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 80)
    
    suite = PharmacyTestSuite()
    
    try:
        # Setup browser
        await suite.setup_browser()
        
        # Run tests
        await suite.test_authentication()
        await suite.test_department_creation()
        await suite.test_import_export_functions()
        await suite.test_reports_section()
        await suite.test_full_application_features()
        await suite.test_error_handling()
        
        # Print summary
        print("\n" + "=" * 80)
        print("📊 TEST SUMMARY")
        print("=" * 80)
        
        total_tests = len(suite.test_results)
        passed_tests = len([r for r in suite.test_results if r["status"] == "PASS"])
        failed_tests = total_tests - passed_tests
        
        print(f"Total Tests: {total_tests}")
        print(f"Passed: {passed_tests}")
        print(f"Failed: {failed_tests}")
        print(f"Success Rate: {(passed_tests/total_tests)*100:.1f}%")
        
        if failed_tests > 0:
            print("\n❌ FAILED TESTS:")
            for result in suite.test_results:
                if result["status"] == "FAIL":
                    print(f"   - {result['test_name']}")
        
    except Exception as e:
        print(f"❌ Test suite error: {str(e)}")
    
    finally:
        await suite.cleanup()

if __name__ == "__main__":
    asyncio.run(main())
