{% extends "base.html" %}

{% block title %}Create Purchase - Hospital Pharmacy Management System{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1><i class="bi bi-plus-circle"></i> Create Purchase</h1>
    <a href="{{ url_for('purchases.index') }}" class="btn btn-outline-secondary">
        <i class="bi bi-arrow-left"></i> Back to Purchases
    </a>
</div>

<div class="row justify-content-center">
    <div class="col-md-10">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">Purchase Information</h5>
            </div>
            <div class="card-body">
                <form method="POST" novalidate>
                    <!-- Basic Information -->
                    <div class="row">
                        <div class="col-md-3">
                            <div class="mb-3">
                                <label for="date" class="form-label">Date <span class="text-danger">*</span></label>
                                <input type="date" class="form-control" id="date" name="date" required>
                                <div class="invalid-feedback">
                                    Please provide a date.
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="mb-3">
                                <label for="invoice_number" class="form-label">Invoice Number <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="invoice_number" name="invoice_number" required>
                                <div class="invalid-feedback">
                                    Please provide an invoice number.
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="mb-3">
                                <label for="supplier_id" class="form-label">Supplier <span class="text-danger">*</span></label>
                                <select class="form-select" id="supplier_id" name="supplier_id" required>
                                    <option value="">Select Supplier</option>
                                    {% for supplier in suppliers %}
                                    <option value="{{ supplier.id }}">{{ supplier.name }}</option>
                                    {% endfor %}
                                </select>
                                <div class="invalid-feedback">
                                    Please select a supplier.
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="mb-3">
                                <label for="purchaser_name" class="form-label">Purchaser Name <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="purchaser_name" name="purchaser_name" required>
                                <div class="invalid-feedback">
                                    Please provide purchaser name.
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Status and Delivery Information -->
                    <div class="row">
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="status" class="form-label">Status <span class="text-danger">*</span></label>
                                <select class="form-select" id="status" name="status" required onchange="toggleDeliveryFields()">
                                    <option value="">Select Status</option>
                                    <option value="complete">Complete</option>
                                    <option value="pending">Pending</option>
                                    <option value="delivered">Delivered</option>
                                </select>
                                <div class="invalid-feedback">
                                    Please select a status.
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4" id="deliveryDateField" style="display: none;">
                            <div class="mb-3">
                                <label for="delivery_date" class="form-label">Delivery Date <span class="text-danger">*</span></label>
                                <input type="date" class="form-control" id="delivery_date" name="delivery_date">
                                <div class="invalid-feedback">
                                    Please provide a delivery date.
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4" id="receivedByField" style="display: none;">
                            <div class="mb-3">
                                <label for="received_by" class="form-label">Received By <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="received_by" name="received_by" placeholder="Name of person who received">
                                <div class="invalid-feedback">
                                    Please provide the name of person who received.
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Medicines Section -->
                    <div class="mb-3">
                        <div class="d-flex justify-content-between align-items-center mb-2">
                            <label class="form-label">Medicines <span class="text-danger">*</span></label>
                            <button type="button" class="btn btn-sm btn-outline-primary" onclick="addMedicineRow()">
                                <i class="bi bi-plus"></i> Add Medicine
                            </button>
                        </div>
                        
                        <div id="medicinesContainer">
                            <div class="row mb-2 medicine-row">
                                <div class="col-md-6">
                                    <select class="form-select" name="medicine_id[]" required>
                                        <option value="">Select Medicine</option>
                                        {% for medicine in medicines %}
                                        <option value="{{ medicine.id }}">{{ medicine.name }} - {{ medicine.form_dosage }}</option>
                                        {% endfor %}
                                    </select>
                                </div>
                                <div class="col-md-4">
                                    <input type="number" class="form-control" name="quantity[]" placeholder="Quantity" min="1" required>
                                </div>
                                <div class="col-md-2">
                                    <button type="button" class="btn btn-outline-danger btn-sm" onclick="removeMedicineRow(this)" disabled>
                                        <i class="bi bi-trash"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Notes -->
                    <div class="mb-3">
                        <label for="notes" class="form-label">Notes</label>
                        <textarea class="form-control" id="notes" name="notes" rows="3" 
                                  placeholder="Additional information about the purchase..."></textarea>
                    </div>
                    
                    <div class="d-flex justify-content-end gap-2">
                        <a href="{{ url_for('purchases.index') }}" class="btn btn-secondary">
                            <i class="bi bi-x-circle"></i> Cancel
                        </a>
                        <button type="submit" class="btn btn-primary">
                            <i class="bi bi-check-circle"></i> Create Purchase
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// Set today's date as default
document.addEventListener('DOMContentLoaded', function() {
    const today = new Date().toISOString().split('T')[0];
    document.getElementById('date').value = today;
});

// Toggle delivery fields based on status selection
function toggleDeliveryFields() {
    const status = document.getElementById('status').value;
    const deliveryDateField = document.getElementById('deliveryDateField');
    const receivedByField = document.getElementById('receivedByField');
    const deliveryDateInput = document.getElementById('delivery_date');
    const receivedByInput = document.getElementById('received_by');

    if (status === 'delivered') {
        deliveryDateField.style.display = 'block';
        receivedByField.style.display = 'block';
        deliveryDateInput.required = true;
        receivedByInput.required = true;

        // Set delivery date to today if not already set
        if (!deliveryDateInput.value) {
            const today = new Date().toISOString().split('T')[0];
            deliveryDateInput.value = today;
        }
    } else {
        deliveryDateField.style.display = 'none';
        receivedByField.style.display = 'none';
        deliveryDateInput.required = false;
        receivedByInput.required = false;
        deliveryDateInput.value = '';
        receivedByInput.value = '';
    }
}

// Add medicine row
function addMedicineRow() {
    const container = document.getElementById('medicinesContainer');
    const newRow = document.createElement('div');
    newRow.className = 'row mb-2 medicine-row';
    newRow.innerHTML = `
        <div class="col-md-6">
            <select class="form-select" name="medicine_id[]" required>
                <option value="">Select Medicine</option>
                {% for medicine in medicines %}
                <option value="{{ medicine.id }}">{{ medicine.name }} - {{ medicine.form_dosage }}</option>
                {% endfor %}
            </select>
        </div>
        <div class="col-md-4">
            <input type="number" class="form-control" name="quantity[]" placeholder="Quantity" min="1" required>
        </div>
        <div class="col-md-2">
            <button type="button" class="btn btn-outline-danger btn-sm" onclick="removeMedicineRow(this)">
                <i class="bi bi-trash"></i>
            </button>
        </div>
    `;
    
    container.appendChild(newRow);
    updateRemoveButtons();
}

// Remove medicine row
function removeMedicineRow(button) {
    const row = button.closest('.medicine-row');
    row.remove();
    updateRemoveButtons();
}

// Update remove buttons (disable if only one row)
function updateRemoveButtons() {
    const rows = document.querySelectorAll('.medicine-row');
    const removeButtons = document.querySelectorAll('.medicine-row button[onclick*="removeMedicineRow"]');
    
    removeButtons.forEach(button => {
        button.disabled = rows.length <= 1;
    });
}

// Form validation
(function() {
    'use strict';
    window.addEventListener('load', function() {
        var forms = document.getElementsByTagName('form');
        var validation = Array.prototype.filter.call(forms, function(form) {
            form.addEventListener('submit', function(event) {
                if (form.checkValidity() === false) {
                    event.preventDefault();
                    event.stopPropagation();
                }
                form.classList.add('was-validated');
            }, false);
        });
    }, false);
})();
</script>
{% endblock %}
