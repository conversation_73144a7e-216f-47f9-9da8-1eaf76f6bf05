# Hospital Pharmacy Management System

A comprehensive web-based pharmacy management system built with Flask, Bootstrap, and JSON database. This system provides complete inventory management, patient tracking, supplier management, and advanced reporting capabilities for hospital pharmacies.

## 🏥 Features

### Core Functionality
- **User Authentication**: Role-based access control (Admin/Department User)
- **Medicine Management**: Complete CRUD operations with inventory tracking
- **Patient Management**: Patient records and consumption tracking
- **Supplier Management**: Supplier information and performance tracking
- **Department Management**: Multi-department support with role-based access
- **Inventory Management**: Real-time stock tracking with color-coded alerts

### Advanced Features
- **Purchase Management**: Multi-medicine purchase orders with automatic inventory updates
- **Consumption Tracking**: Point-of-sale style medicine dispensing
- **Comprehensive Reporting**: Consumption, inventory, supplier, and purchase reports
- **AI-Powered Insights**: Suggested purchase recommendations based on consumption patterns
- **Low Stock Alerts**: Automated alerts for medicines below threshold levels
- **Export Functionality**: CSV export for all reports and data

### User Interface
- **Responsive Design**: Mobile-friendly Bootstrap 5 interface
- **Dark/Light Theme**: User-selectable theme preferences
- **Interactive Charts**: Visual analytics with Chart.js
- **Search & Filtering**: Advanced filtering across all modules
- **Role-Based Navigation**: Customized interface based on user permissions

## 🚀 Quick Start

### Prerequisites
- Python 3.7 or higher
- pip (Python package installer)

### Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd JAHRA-ALORF-STORE
   ```

2. **Install dependencies**
   ```bash
   pip install -r requirements.txt
   ```

3. **Run the application**
   ```bash
   python app.py
   ```

4. **Access the system**
   - Open your browser and navigate to `http://127.0.0.1:5000`
   - Log in with your assigned credentials

## 📁 Project Structure

```
JAHRA-ALORF-STORE/
├── app.py                 # Main Flask application
├── requirements.txt       # Python dependencies
├── blueprints/           # Flask blueprints
│   ├── auth.py           # Authentication routes
│   ├── dashboard.py      # Dashboard routes
│   ├── medicines.py      # Medicine management
│   ├── patients.py       # Patient management
│   ├── suppliers.py      # Supplier management
│   ├── departments.py    # Department management
│   ├── stores.py         # Store/inventory management
│   ├── purchases.py      # Purchase management
│   ├── consumption.py    # Consumption tracking
│   ├── reports.py        # Reporting system
│   ├── chatbot.py        # AI chatbot interface
│   └── settings.py       # Settings and user management
├── templates/            # Jinja2 templates
│   ├── base.html         # Base template
│   ├── auth/             # Authentication templates
│   ├── dashboard/        # Dashboard templates
│   ├── medicines/        # Medicine management templates
│   ├── patients/         # Patient management templates
│   ├── suppliers/        # Supplier management templates
│   ├── departments/      # Department management templates
│   ├── stores/           # Store management templates
│   ├── purchases/        # Purchase management templates
│   ├── consumption/      # Consumption tracking templates
│   ├── reports/          # Reporting templates
│   ├── chatbot/          # AI chatbot templates
│   ├── settings/         # Settings templates
│   └── errors/           # Error page templates
├── static/               # Static files
│   ├── css/
│   │   └── style.css     # Custom styles
│   └── js/
│       └── app.js        # Custom JavaScript
├── utils/                # Utility modules
│   ├── database.py       # Database operations
│   └── helpers.py        # Helper functions
└── data/                 # JSON database files (auto-created)
    ├── users.json
    ├── medicines.json
    ├── patients.json
    ├── suppliers.json
    ├── departments.json
    ├── stores.json
    ├── purchases.json
    ├── consumption.json
    └── history.json
```

## 👥 User Roles

### Administrator
- Full system access
- User management
- All CRUD operations
- Purchase management
- System settings
- AI chatbot access
- All reports and analytics

### Department User
- Department-specific access
- Medicine and patient management
- Consumption tracking
- Limited reporting (department-specific)
- Profile management

## 🔧 Configuration

### User Management
The system supports role-based access control with different user types and permissions.

### Database Configuration
The system uses JSON files for data storage, located in the `data/` directory. The database is automatically initialized on first run with:
- Default user accounts
- Sample department (Main Pharmacy)
- Empty data structures for all modules

## 📊 Usage Guide

### Getting Started
1. **Login**: Use your assigned credentials
2. **Dashboard**: View system overview and quick stats
3. **Add Suppliers**: Start by adding medicine suppliers
4. **Add Medicines**: Create your medicine inventory
5. **Record Purchases**: Add stock through purchase orders
6. **Track Consumption**: Record medicine dispensing to patients

### Key Workflows

#### Medicine Management
1. Navigate to Medicines → Add Medicine
2. Fill in medicine details and select supplier
3. Set low stock limits for automatic alerts
4. View inventory levels with color-coded status

#### Purchase Management (Admin Only)
1. Navigate to Purchases → Create Purchase
2. Select supplier and add multiple medicines
3. System automatically updates inventory
4. Track purchase history and invoices

#### Consumption Tracking
1. Navigate to Consumption → Record Consumption
2. Select patient and medicines to dispense
3. System validates against available stock
4. Automatic inventory deduction

#### Reporting
1. Navigate to Reports for various analytics
2. Use filters for specific date ranges or departments
3. Export data as CSV for external analysis
4. View AI-powered purchase suggestions

## 🤖 AI Features

### Suggested Purchase Report
The system includes AI-powered purchase recommendations based on:
- 30-day consumption patterns
- Current stock levels
- Low stock limits
- Historical usage trends

### Smart Alerts
- Automatic low stock notifications
- Color-coded inventory status
- Consumption pattern analysis

## 🎨 Customization

### Themes
Users can switch between light and dark themes:
- Click the theme toggle in the user dropdown
- Preference is saved locally

### Responsive Design
The interface adapts to different screen sizes:
- Desktop: Full feature set
- Tablet: Optimized layout
- Mobile: Touch-friendly interface

## 🔒 Security Features

- Session-based authentication
- Role-based access control
- CSRF protection (recommended for production)
- Input validation and sanitization
- Secure password handling (upgrade to hashing recommended)

## 📈 Performance

### Optimization Features
- Efficient JSON database operations
- Client-side filtering and search
- Lazy loading for large datasets
- Optimized Bootstrap components

### Browser Support
- Chrome 90+
- Firefox 88+
- Safari 14+
- Edge 90+

## 🚀 Deployment

### Development
```bash
python app.py
```
Access at `http://127.0.0.1:5000`

### Production Considerations
1. **Security**: Implement password hashing
2. **Database**: Consider migrating to PostgreSQL/MySQL
3. **Web Server**: Use Gunicorn + Nginx
4. **SSL**: Enable HTTPS
5. **Backup**: Implement regular data backups

## 🤝 Contributing

### Development Setup
1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

### Code Style
- Follow PEP 8 for Python code
- Use meaningful variable names
- Add comments for complex logic
- Maintain consistent indentation

## 📝 License

This project is developed for internal hospital use. All rights reserved.

## 👨‍💻 Credits

- **Developer**: Waleed Mohamed (Lead Developer & System Architect)
- **Hospital**: ALORF HOSPITAL
- **Technology Stack**: Flask, Bootstrap 5, Chart.js, JSON Database

## 📞 Support

For technical support or feature requests:
- **Development Team**: <EMAIL>
- **Hospital Administration**: <EMAIL>

## 🔄 Version History

- **v1.0.0** (July 2025): Initial release with full feature set

----

**Hospital Pharmacy Management System** - Streamlining pharmacy operations with modern web technology.
# ALORFMEDZ - Auto-deployment test Fri Jul 25 22:15:04 UTC 2025
