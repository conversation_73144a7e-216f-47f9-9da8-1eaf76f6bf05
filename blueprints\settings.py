"""
Settings Blueprint
"""

from flask import Blueprint, render_template, request, redirect, url_for, flash, send_file, jsonify
from utils.helpers import login_required, admin_required
from utils.database import (
    get_medicines, get_patients, get_suppliers, get_departments, get_history,
    get_users, get_user_activity_summary, save_user, update_user, delete_user,
    get_user_by_id, generate_secure_password, create_department_user
)
import os
import zipfile
import json
from datetime import datetime
from werkzeug.utils import secure_filename

settings_bp = Blueprint('settings', __name__)

@settings_bp.route('/')
@login_required
def index():
    """Settings dashboard"""
    return render_template('settings/index.html')



@settings_bp.route('/about')
@login_required
def about():
    """About page"""
    # Get system statistics
    medicines = get_medicines()
    patients = get_patients()
    suppliers = get_suppliers()
    departments = get_departments()

    stats = {
        'total_medicines': len(medicines),
        'total_patients': len(patients),
        'total_suppliers': len(suppliers),
        'total_departments': len(departments)
    }

    return render_template('settings/about.html', **stats)

@settings_bp.route('/history')
@login_required
def history():
    """Activity history page"""
    from flask import request, session

    # Get filter parameters
    user_id = request.args.get('user_id')
    action = request.args.get('action')
    entity_type = request.args.get('entity_type')

    # For non-admin users, only show their own history
    if session.get('role') != 'admin':
        user_id = session.get('user_id')

    # Get filtered history
    history_data = get_history(limit=200, user_id=user_id, entity_type=entity_type)

    # Apply action filter
    if action:
        history_data = [h for h in history_data if h.get('action') == action]

    # Get users for admin filter
    users = get_users() if session.get('role') == 'admin' else []

    return render_template('settings/history.html',
                         history=history_data,
                         users=users)

@settings_bp.route('/users')
@admin_required
def users():
    """User management page (admin only)"""
    users = get_users()
    departments = get_departments()

    # Get activity summary for each user
    user_activities = {}
    for user in users:
        user_activities[user['id']] = get_user_activity_summary(user['id'])

    return render_template('settings/users.html',
                         users=users,
                         departments=departments,
                         user_activities=user_activities)

@settings_bp.route('/users/add', methods=['POST'])
@admin_required
def add_user():
    """Add new user (admin only)"""
    try:
        # Get form data
        username = request.form.get('username', '').strip()
        password = request.form.get('password', '').strip()
        role = request.form.get('role', 'department_user')
        name = request.form.get('name', '').strip()
        email = request.form.get('email', '').strip()
        department_id = request.form.get('department_id', '').strip()

        # Validation
        if not username:
            flash('Username is required!', 'error')
            return redirect(url_for('settings.users'))

        if not password:
            flash('Password is required!', 'error')
            return redirect(url_for('settings.users'))

        if role == 'department_user' and not department_id:
            flash('Department is required for department users!', 'error')
            return redirect(url_for('settings.users'))

        # Prepare user data
        user_data = {
            'username': username,
            'password': password,
            'role': role,
            'name': name or username.title(),
            'email': email or f"{username}@hospital.com"
        }

        # Add department_id for department users
        if role == 'department_user' and department_id:
            user_data['department_id'] = department_id

        # Save user
        user_id = save_user(user_data)
        flash(f'User "{username}" created successfully!', 'success')

    except ValueError as e:
        flash(str(e), 'error')
    except Exception as e:
        flash(f'Error creating user: {str(e)}', 'error')

    return redirect(url_for('settings.users'))

@settings_bp.route('/users/edit/<user_id>', methods=['GET', 'POST'])
@admin_required
def edit_user(user_id):
    """Edit user (admin only)"""
    user = get_user_by_id(user_id)
    if not user:
        flash('User not found!', 'error')
        return redirect(url_for('settings.users'))

    if request.method == 'POST':
        try:
            # Get form data
            username = request.form.get('username', '').strip()
            password = request.form.get('password', '').strip()
            role = request.form.get('role', user.get('role', 'department_user'))
            name = request.form.get('name', '').strip()
            email = request.form.get('email', '').strip()
            department_id = request.form.get('department_id', '').strip()

            # Validation
            if not username:
                flash('Username is required!', 'error')
                return redirect(url_for('settings.edit_user', user_id=user_id))

            if role == 'department_user' and not department_id:
                flash('Department is required for department users!', 'error')
                return redirect(url_for('settings.edit_user', user_id=user_id))

            # Prepare update data
            update_data = {
                'username': username,
                'role': role,
                'name': name or username.title(),
                'email': email or f"{username}@hospital.com"
            }

            # Add password if provided
            if password:
                update_data['password'] = password

            # Add department_id for department users
            if role == 'department_user' and department_id:
                update_data['department_id'] = department_id
            elif role == 'admin':
                update_data['department_id'] = None

            # Update user
            update_user(user_id, update_data)
            flash(f'User "{username}" updated successfully!', 'success')
            return redirect(url_for('settings.users'))

        except ValueError as e:
            flash(str(e), 'error')
        except Exception as e:
            flash(f'Error updating user: {str(e)}', 'error')

    # GET request - show edit form
    departments = get_departments()
    return render_template('settings/edit_user.html', user=user, departments=departments)

@settings_bp.route('/users/delete/<user_id>', methods=['POST'])
@admin_required
def delete_user_route(user_id):
    """Delete user (admin only)"""
    try:
        user = get_user_by_id(user_id)
        if not user:
            flash('User not found!', 'error')
            return redirect(url_for('settings.users'))

        username = user.get('username', 'Unknown')
        delete_user(user_id)
        flash(f'User "{username}" deleted successfully!', 'success')

    except ValueError as e:
        flash(str(e), 'error')
    except Exception as e:
        flash(f'Error deleting user: {str(e)}', 'error')

    return redirect(url_for('settings.users'))

@settings_bp.route('/users/reset_password/<user_id>', methods=['POST'])
@admin_required
def reset_user_password(user_id):
    """Reset user password (admin only)"""
    try:
        user = get_user_by_id(user_id)
        if not user:
            flash('User not found!', 'error')
            return redirect(url_for('settings.users'))

        # Generate new password
        new_password = generate_secure_password()

        # Update user password
        update_user(user_id, {'password': new_password})

        username = user.get('username', 'Unknown')
        flash(f'Password reset for "{username}". New password: {new_password}', 'success')
        flash('Please save this password and share it with the user.', 'warning')

    except Exception as e:
        flash(f'Error resetting password: {str(e)}', 'error')

    return redirect(url_for('settings.users'))

@settings_bp.route('/users/create_department_user/<department_id>', methods=['POST'])
@admin_required
def create_dept_user(department_id):
    """Create additional department user for existing department"""
    try:
        departments = get_departments()
        department = next((d for d in departments if d['id'] == department_id), None)

        if not department:
            flash('Department not found!', 'error')
            return redirect(url_for('settings.users'))

        # Create department user
        user_info = create_department_user(department_id, department['name'])

        flash(f'Department user created successfully!', 'success')
        flash(f'Username: {user_info["username"]}, Password: {user_info["password"]}', 'info')
        flash('Please save these credentials and share them with the department staff.', 'warning')

    except Exception as e:
        flash(f'Error creating department user: {str(e)}', 'error')

    return redirect(url_for('settings.users'))

@settings_bp.route('/backup/full')
@admin_required
def backup_full():
    """Create and download full system backup as ZIP"""
    try:
        from utils.database import DATA_DIR, DB_FILES

        # Create backup filename with timestamp
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        backup_filename = f'pharmacy_backup_{timestamp}.zip'
        backup_path = os.path.join(DATA_DIR, backup_filename)

        # Create ZIP file with all data
        with zipfile.ZipFile(backup_path, 'w', zipfile.ZIP_DEFLATED) as zipf:
            for file_type, file_path in DB_FILES.items():
                if os.path.exists(file_path):
                    zipf.write(file_path, f'{file_type}.json')

            # Add backup metadata
            metadata = {
                'backup_date': datetime.now().isoformat(),
                'backup_type': 'full_system',
                'files_included': list(DB_FILES.keys()),
                'version': '1.0.0'
            }
            zipf.writestr('backup_metadata.json', json.dumps(metadata, indent=2))

        return send_file(backup_path, as_attachment=True, download_name=backup_filename)

    except Exception as e:
        flash(f'Backup failed: {str(e)}', 'error')
        return redirect(url_for('settings.index'))

@settings_bp.route('/backup/file/<file_type>')
@admin_required
def backup_file(file_type):
    """Download individual data file"""
    try:
        from utils.database import DB_FILES

        if file_type not in DB_FILES:
            flash('Invalid file type.', 'error')
            return redirect(url_for('settings.index'))

        file_path = DB_FILES[file_type]
        if not os.path.exists(file_path):
            flash('File not found.', 'error')
            return redirect(url_for('settings.index'))

        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        download_name = f'{file_type}_{timestamp}.json'

        return send_file(file_path, as_attachment=True, download_name=download_name)

    except Exception as e:
        flash(f'File download failed: {str(e)}', 'error')
        return redirect(url_for('settings.index'))

@settings_bp.route('/restore', methods=['GET', 'POST'])
@admin_required
def restore():
    """Restore system from backup"""
    if request.method == 'POST':
        try:
            if 'backup_file' not in request.files:
                flash('No backup file selected.', 'error')
                return redirect(url_for('settings.restore'))

            file = request.files['backup_file']
            if file.filename == '':
                flash('No backup file selected.', 'error')
                return redirect(url_for('settings.restore'))

            if not file.filename.endswith('.zip'):
                flash('Please upload a ZIP backup file.', 'error')
                return redirect(url_for('settings.restore'))

            # Save uploaded file
            from utils.database import DATA_DIR
            filename = secure_filename(file.filename)
            upload_path = os.path.join(DATA_DIR, f'restore_{filename}')
            file.save(upload_path)

            # Extract and validate backup
            extracted_files = []
            with zipfile.ZipFile(upload_path, 'r') as zipf:
                # Check for metadata
                if 'backup_metadata.json' in zipf.namelist():
                    metadata_content = zipf.read('backup_metadata.json')
                    metadata = json.loads(metadata_content)
                    flash(f'Backup created on: {metadata.get("backup_date", "Unknown")}', 'info')

                # Extract data files
                from utils.database import DB_FILES
                for file_type in DB_FILES.keys():
                    json_filename = f'{file_type}.json'
                    if json_filename in zipf.namelist():
                        # Validate JSON content
                        content = zipf.read(json_filename)
                        try:
                            json.loads(content)  # Validate JSON
                            # Extract to data directory
                            zipf.extract(json_filename, DATA_DIR)
                            # Rename to proper filename
                            extracted_path = os.path.join(DATA_DIR, json_filename)
                            target_path = DB_FILES[file_type]
                            if os.path.exists(extracted_path):
                                os.replace(extracted_path, target_path)
                                extracted_files.append(file_type)
                        except json.JSONDecodeError:
                            flash(f'Invalid JSON in {json_filename}', 'warning')

            # Clean up
            os.remove(upload_path)

            if extracted_files:
                flash(f'Successfully restored {len(extracted_files)} data files: {", ".join(extracted_files)}', 'success')
            else:
                flash('No valid data files found in backup.', 'warning')

            return redirect(url_for('settings.index'))

        except Exception as e:
            flash(f'Restore failed: {str(e)}', 'error')
            return redirect(url_for('settings.restore'))

    return render_template('settings/restore.html')

@settings_bp.route('/generate-sample-data')
@admin_required
def generate_sample_data():
    """Generate comprehensive sample data"""
    try:
        from utils.sample_data import save_sample_data
        save_sample_data()
        flash('Sample data generated successfully! The system now contains comprehensive test data.', 'success')
    except Exception as e:
        flash(f'Sample data generation failed: {str(e)}', 'error')

    return redirect(url_for('settings.index'))
