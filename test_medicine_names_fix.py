#!/usr/bin/env python3
"""
Test the fixed medicine names query handling
"""

import requests
import json

def test_medicine_names_query():
    """Test the fixed medicine names query"""
    base_url = "http://127.0.0.1:5000"
    session = requests.Session()
    
    print("🧪 Testing Fixed Medicine Names Query\n")
    
    # Login
    login_data = {'username': 'admin', 'password': '@Xx123456789xX@'}
    login_response = session.post(f"{base_url}/auth/login", data=login_data)
    print(f"Login: {'✅' if 'dashboard' in login_response.url else '❌'}")
    
    # Set configuration to use enhanced local agent (not force LLM)
    config_data = {
        'provider': 'openrouter',
        'openrouter_api_key': '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',
        'model': 'deepseek/deepseek-r1:nitro',
        'max_tokens': '1000',
        'temperature': '0.7',
        'enabled': 'on',
        # force_llm not set (defaults to False) - use enhanced local agent
    }
    
    config_response = session.post(f"{base_url}/chatbot/config", data=config_data)
    print(f"Config set to Enhanced Agent Mode: {'✅' if config_response.status_code == 200 else '❌'}")
    
    # Test the specific query that was problematic
    test_queries = [
        "give me names of all the medicines in the database",
        "show me all medicine names",
        "list all medicines in database",
        "what are all the medicines",
        "show complete list of medicines"
    ]
    
    for i, query in enumerate(test_queries, 1):
        print(f"\n{i}. Testing Query: '{query}'")
        query_data = {'query': query}
        response = session.post(f"{base_url}/chatbot/query", json=query_data, headers={'Content-Type': 'application/json'})
        
        if response.status_code == 200:
            data = response.json()
            agent_handled = data.get('agent_handled', False)
            response_text = data.get('response', '')
            
            print(f"   Status: {'✅ Success' if agent_handled else '❌ Failed'}")
            print(f"   Agent Handled: {agent_handled}")
            print(f"   Response Length: {len(response_text)} characters")
            
            # Check if response contains medicine names
            if 'ALL MEDICINES IN DATABASE' in response_text:
                print("   ✅ Correct response format detected")
                
                # Count medicine entries in response
                medicine_count = response_text.count('. **')
                print(f"   Medicine Count in Response: {medicine_count}")
                
                # Check if it contains unwanted information
                unwanted_info = ['Patients', 'Suppliers', 'Departments', 'Stores']
                has_unwanted = any(info in response_text for info in unwanted_info)
                print(f"   Contains Unwanted Info: {'❌ Yes' if has_unwanted else '✅ No'}")
                
                # Show first few lines of response
                lines = response_text.split('\n')[:10]
                print("   Response Preview:")
                for line in lines:
                    if line.strip():
                        print(f"     {line}")
                        
            else:
                print("   ❌ Incorrect response format")
                print(f"   Response Preview: {response_text[:200]}...")
        else:
            print(f"   ❌ HTTP Error: {response.status_code}")
    
    print("\n🎯 Medicine Names Query Testing Complete!")

if __name__ == "__main__":
    test_medicine_names_query()
