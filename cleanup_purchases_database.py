#!/usr/bin/env python3
"""
Database cleanup script for purchases.json
This script will:
1. Remove the "payment_method" field from all records
2. Standardize the "status" field to use only "complete", "pending", "delivered"
3. Ensure delivery_date and received_by are only present for "delivered" status
4. Clean up any inconsistencies
"""

import json
import os
from datetime import datetime

def cleanup_purchases_database():
    """Clean up the purchases database"""
    
    # Load current purchases data
    purchases_file = 'data/purchases.json'
    
    if not os.path.exists(purchases_file):
        print(f"Error: {purchases_file} not found!")
        return
    
    # Create backup
    backup_file = f'data/purchases_backup_{datetime.now().strftime("%Y%m%d_%H%M%S")}.json'
    
    with open(purchases_file, 'r') as f:
        purchases = json.load(f)
    
    # Create backup
    with open(backup_file, 'w') as f:
        json.dump(purchases, f, indent=2)
    print(f"Backup created: {backup_file}")
    
    # Clean up each purchase record
    cleaned_count = 0
    status_mapping = {
        'completed': 'complete',
        'complete': 'complete',
        'pending': 'pending',
        'delivered': 'delivered',
        'cancelled': 'pending',  # Map cancelled to pending
        'canceled': 'pending'    # Handle typos
    }
    
    for purchase in purchases:
        original_purchase = purchase.copy()
        
        # 1. Remove payment_method field
        if 'payment_method' in purchase:
            del purchase['payment_method']
            cleaned_count += 1
        
        # 2. Standardize status field
        current_status = purchase.get('status', 'complete').lower()
        purchase['status'] = status_mapping.get(current_status, 'complete')
        
        # 3. Handle delivery_date and received_by based on status
        if purchase['status'] != 'delivered':
            # Remove delivery_date and received_by for non-delivered items
            if 'delivery_date' in purchase:
                del purchase['delivery_date']
            if 'received_by' in purchase:
                del purchase['received_by']
        else:
            # Ensure delivered items have delivery_date and received_by
            if 'delivery_date' not in purchase or not purchase['delivery_date']:
                # Use purchase date as fallback
                purchase['delivery_date'] = purchase.get('date') or purchase.get('purchase_date', '')
            if 'received_by' not in purchase or not purchase['received_by']:
                purchase['received_by'] = 'Unknown'
        
        # 4. Ensure notes field exists (rename from notes if needed)
        if 'notes' not in purchase:
            purchase['notes'] = ''
        
        # 5. Standardize date field (prefer 'date' over 'purchase_date')
        if 'purchase_date' in purchase and 'date' not in purchase:
            purchase['date'] = purchase['purchase_date']
        if 'purchase_date' in purchase and 'date' in purchase:
            # Remove purchase_date if date exists
            del purchase['purchase_date']
        
        # 6. Ensure required fields exist
        if 'purchaser_name' not in purchase:
            purchase['purchaser_name'] = 'Unknown'
        
        # 7. Update timestamp
        purchase['updated_at'] = datetime.now().isoformat()
        
        if purchase != original_purchase:
            cleaned_count += 1
    
    # Save cleaned data
    with open(purchases_file, 'w') as f:
        json.dump(purchases, f, indent=2)
    
    print(f"Database cleanup completed!")
    print(f"Total records processed: {len(purchases)}")
    print(f"Records modified: {cleaned_count}")
    
    # Print status distribution
    status_counts = {}
    for purchase in purchases:
        status = purchase.get('status', 'unknown')
        status_counts[status] = status_counts.get(status, 0) + 1
    
    print("\nStatus distribution after cleanup:")
    for status, count in status_counts.items():
        print(f"  {status}: {count}")

if __name__ == "__main__":
    cleanup_purchases_database()
