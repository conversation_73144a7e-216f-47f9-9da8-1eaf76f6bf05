{% extends "base.html" %}

{% block title %}Suppliers Management - Hospital Pharmacy Management System{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1><i class="bi bi-truck"></i> Suppliers Management</h1>
    <div>
        <a href="{{ url_for('suppliers.add') }}" class="btn btn-primary">
            <i class="bi bi-plus-circle"></i> Add Supplier
        </a>
        <div class="btn-group" role="group">
            <button type="button" class="btn btn-outline-secondary dropdown-toggle" data-bs-toggle="dropdown">
                <i class="bi bi-download"></i> Export
            </button>
            <ul class="dropdown-menu">
                <li><a class="dropdown-item" href="#" onclick="exportTableToCSV('suppliersTable', 'suppliers')">
                    <i class="bi bi-file-earmark-spreadsheet"></i> Export CSV
                </a></li>
                <li><a class="dropdown-item" href="{{ url_for('suppliers.download_template') }}">
                    <i class="bi bi-file-earmark-arrow-down"></i> Download Template
                </a></li>
            </ul>
        </div>
        <button type="button" class="btn btn-outline-info" data-bs-toggle="modal" data-bs-target="#importModal">
            <i class="bi bi-upload"></i> Import
        </button>
    </div>
</div>

<!-- Search and Filter -->
<div class="row mb-3">
    <div class="col-md-6">
        <div class="input-group">
            <span class="input-group-text"><i class="bi bi-search"></i></span>
            <input type="text" class="form-control" id="searchInput" placeholder="Search suppliers...">
        </div>
    </div>
    <div class="col-md-3">
        <select class="form-select" id="typeFilter">
            <option value="">All Types</option>
            <option value="Medicine">Medicine</option>
            <option value="Disposable">Disposable</option>
            <option value="Equipment">Equipment</option>
            <option value="Mixed">Mixed</option>
        </select>
    </div>
    <div class="col-md-3">
        <div class="d-flex gap-2">
            <button class="btn btn-outline-secondary" onclick="toggleView('card')" id="cardViewBtn">
                <i class="bi bi-grid"></i> Cards
            </button>
            <button class="btn btn-outline-secondary" onclick="toggleView('table')" id="tableViewBtn">
                <i class="bi bi-list"></i> Table
            </button>
        </div>
    </div>
</div>

<!-- Card View -->
<div id="cardView">
    <div class="row" id="suppliersCards">
        {% for supplier in suppliers %}
        <div class="col-md-4 mb-4 supplier-card" data-type="{{ supplier.type }}" data-name="{{ supplier.name|lower }}">
            <div class="card h-100">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h6 class="card-title mb-0">
                        <i class="bi bi-truck"></i> ID: {{ supplier.id }}
                    </h6>
                    <span class="badge bg-primary">Supplier</span>
                </div>
                <div class="card-body">
                    <h5 class="card-title">{{ supplier.name }}</h5>
                    <p class="card-text">
                        <i class="bi bi-telephone"></i> {{ supplier.phone }}<br>
                        <i class="bi bi-person"></i> {{ supplier.contact_person }}<br>
                        <small class="text-muted">{{ supplier.email }}</small>
                    </p>
                </div>
                <div class="card-footer">
                    <div class="d-flex justify-content-end gap-2">
                        <a href="{{ url_for('suppliers.edit', supplier_id=supplier.id) }}" 
                           class="btn btn-sm btn-outline-primary">
                            <i class="bi bi-pencil"></i> Edit
                        </a>
                        <a href="{{ url_for('suppliers.delete', supplier_id=supplier.id) }}" 
                           class="btn btn-sm btn-outline-danger"
                           onclick="return confirmDelete('Are you sure you want to delete this supplier?')">
                            <i class="bi bi-trash"></i> Delete
                        </a>
                    </div>
                </div>
            </div>
        </div>
        {% else %}
        <div class="col-12">
            <div class="text-center text-muted py-5">
                <i class="bi bi-inbox display-4"></i>
                <h4>No suppliers found</h4>
                <p>Get started by <a href="{{ url_for('suppliers.add') }}">adding your first supplier</a>.</p>
            </div>
        </div>
        {% endfor %}
    </div>
</div>

<!-- Table View (hidden by default) -->
<div id="tableView" style="display: none;">
    <div class="card">
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-hover" id="suppliersTable">
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>Supplier Name</th>
                            <th>Type</th>
                            <th>Contact Person</th>
                            <th>Phone</th>
                            <th>Email</th>
                            <th>Address</th>
                            <th>Notes</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for supplier in suppliers %}
                        <tr>
                            <td>{{ supplier.id }}</td>
                            <td><strong>{{ supplier.name }}</strong></td>
                            <td>{{ supplier.type or 'Medicine' }}</td>
                            <td>{{ supplier.contact_person }}</td>
                            <td>{{ supplier.phone }}</td>
                            <td>{{ supplier.email }}</td>
                            <td>{{ supplier.address or '' }}</td>
                            <td>{{ (supplier.notes or '')[:50] }}{% if (supplier.notes or '')|length > 50 %}...{% endif %}</td>
                            <td class="action-buttons">
                                <a href="{{ url_for('suppliers.edit', supplier_id=supplier.id) }}" 
                                   class="btn btn-sm btn-outline-primary" title="Edit">
                                    <i class="bi bi-pencil"></i>
                                </a>
                                <a href="{{ url_for('suppliers.delete', supplier_id=supplier.id) }}" 
                                   class="btn btn-sm btn-outline-danger" title="Delete"
                                   onclick="return confirmDelete('Are you sure you want to delete this supplier?')">
                                    <i class="bi bi-trash"></i>
                                </a>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<!-- Import Modal -->
<div class="modal fade" id="importModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Import Suppliers</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="importForm" method="POST" enctype="multipart/form-data" action="{{ url_for('suppliers.index') }}">
                    <div class="mb-3">
                        <label for="csvFile" class="form-label">Select CSV File</label>
                        <input type="file" class="form-control" id="csvFile" name="csv_file" accept=".csv" required>
                        <div class="form-text">
                            Please use the template format. <a href="{{ url_for('suppliers.download_template') }}">Download template</a>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="submit" form="importForm" class="btn btn-primary">Import</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// View toggle functionality
function toggleView(view) {
    const cardView = document.getElementById('cardView');
    const tableView = document.getElementById('tableView');
    const cardBtn = document.getElementById('cardViewBtn');
    const tableBtn = document.getElementById('tableViewBtn');
    
    if (view === 'card') {
        cardView.style.display = 'block';
        tableView.style.display = 'none';
        cardBtn.classList.add('active');
        tableBtn.classList.remove('active');
    } else {
        cardView.style.display = 'none';
        tableView.style.display = 'block';
        cardBtn.classList.remove('active');
        tableBtn.classList.add('active');
    }
}

// Search functionality for cards
document.getElementById('searchInput').addEventListener('keyup', function() {
    const filter = this.value.toLowerCase();
    const cards = document.querySelectorAll('.supplier-card');
    
    cards.forEach(function(card) {
        const name = card.getAttribute('data-name');
        if (name.includes(filter)) {
            card.style.display = 'block';
        } else {
            card.style.display = 'none';
        }
    });
});

// Type filter
document.getElementById('typeFilter').addEventListener('change', function() {
    const filter = this.value;
    const cards = document.querySelectorAll('.supplier-card');
    
    cards.forEach(function(card) {
        const type = card.getAttribute('data-type');
        if (filter === '' || type === filter) {
            card.style.display = 'block';
        } else {
            card.style.display = 'none';
        }
    });
});

// Set default view
document.addEventListener('DOMContentLoaded', function() {
    toggleView('card');
});
</script>
{% endblock %}
