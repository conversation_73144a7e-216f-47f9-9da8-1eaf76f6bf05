#!/usr/bin/env python3
"""
Test the force LLM functionality
"""

import requests
import json

def test_force_llm():
    """Test force LLM functionality"""
    base_url = "http://127.0.0.1:5000"
    session = requests.Session()
    
    print("🤖 Testing Force LLM Functionality\n")
    
    # Login
    login_data = {'username': 'admin', 'password': '@Xx123456789xX@'}
    login_response = session.post(f"{base_url}/auth/login", data=login_data)
    print(f"Login: {'✅' if 'dashboard' in login_response.url else '❌'}")
    
    # Test 1: Normal mode (agent first)
    print("\n1. Testing Normal Mode (Agent First)...")
    config_data = {
        'provider': 'openrouter',
        'openrouter_api_key': '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',
        'model': 'deepseek/deepseek-r1:nitro',
        'max_tokens': '1000',
        'temperature': '0.7',
        'enabled': 'on',
        # force_llm not set (defaults to False)
    }
    
    config_response = session.post(f"{base_url}/chatbot/config", data=config_data)
    print(f"   Config saved: {'✅' if config_response.status_code == 200 else '❌'}")
    
    query_data = {'query': 'Show me medicine inventory overview'}
    query_response = session.post(f"{base_url}/chatbot/query", json=query_data, headers={'Content-Type': 'application/json'})
    
    if query_response.status_code == 200:
        data = query_response.json()
        print(f"   Agent Handled: {data.get('agent_handled', False)}")
        print(f"   LLM Handled: {data.get('llm_handled', False)}")
        print(f"   Response Type: {'Agent' if data.get('agent_handled') else 'LLM' if data.get('llm_handled') else 'Fallback'}")
    
    # Test 2: Force LLM mode
    print("\n2. Testing Force LLM Mode...")
    config_data_force = {
        'provider': 'openrouter',
        'openrouter_api_key': '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',
        'model': 'deepseek/deepseek-r1:nitro',
        'max_tokens': '1000',
        'temperature': '0.7',
        'enabled': 'on',
        'force_llm': 'on'  # Force LLM mode
    }
    
    config_response = session.post(f"{base_url}/chatbot/config", data=config_data_force)
    print(f"   Config saved: {'✅' if config_response.status_code == 200 else '❌'}")
    
    query_data = {'query': 'What is the current status of our pharmacy inventory?'}
    query_response = session.post(f"{base_url}/chatbot/query", json=query_data, headers={'Content-Type': 'application/json'})
    
    if query_response.status_code == 200:
        data = query_response.json()
        print(f"   Agent Handled: {data.get('agent_handled', False)}")
        print(f"   LLM Handled: {data.get('llm_handled', False)}")
        print(f"   Response Type: {'Agent' if data.get('agent_handled') else 'LLM' if data.get('llm_handled') else 'Fallback'}")
        response_preview = data.get('response', '')[:200]
        print(f"   Response Preview: {response_preview}...")
    else:
        print(f"   ❌ Query failed: {query_response.status_code}")
        print(f"   Error: {query_response.text[:200]}...")
    
    print("\n🎯 Force LLM Testing Complete!")

if __name__ == "__main__":
    test_force_llm()
