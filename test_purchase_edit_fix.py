#!/usr/bin/env python3
"""
Test Purchase Edit Fix
Tests the fix for purchase record #02 editing functionality
"""

import asyncio
from playwright.async_api import async_playwright

async def test_purchase_edit_fix():
    """Test the purchase edit fix"""
    
    async with async_playwright() as p:
        browser = await p.chromium.launch(headless=False, slow_mo=1000)
        context = await browser.new_context(viewport={'width': 1920, 'height': 1080})
        page = await context.new_page()
        
        try:
            print("🔧 Testing Purchase Edit Fix...")
            
            # Step 1: Navigate to local application
            print("\n📡 Step 1: Navigating to local application...")
            await page.goto("http://localhost:5000", wait_until='networkidle')
            
            # Step 2: Login
            print("\n🔐 Step 2: Logging in...")
            await page.fill('input[name="username"]', 'admin')
            await page.fill('input[name="password"]', '@Xx123456789xX@')
            await page.click('button[type="submit"]')
            await page.wait_for_timeout(2000)
            
            # Verify login success
            if 'dashboard' in page.url:
                print("✅ Login successful")
            else:
                print("❌ Login failed")
                return
            
            # Step 3: Navigate to purchases page
            print("\n🛒 Step 3: Navigating to purchases page...")
            await page.click('a[href*="purchase"]')
            await page.wait_for_timeout(2000)
            
            # Step 4: Try to edit purchase record #02
            print("\n✏️ Step 4: Testing edit of purchase record #02...")
            
            # Look for edit button for purchase #02
            edit_button = await page.query_selector('a[href*="/purchases/edit/02"]')
            if edit_button:
                print("✅ Found edit button for purchase #02")
                await edit_button.click()
                await page.wait_for_timeout(3000)
                
                # Check if we reached the edit page without error
                current_url = page.url
                if 'edit/02' in current_url:
                    print("✅ Successfully reached edit page for purchase #02")
                    
                    # Take screenshot of edit page
                    await page.screenshot(path="purchase_02_edit_page.png")
                    print("📸 Screenshot saved: purchase_02_edit_page.png")
                    
                    # Step 5: Try to submit the form (test the actual fix)
                    print("\n💾 Step 5: Testing form submission...")
                    
                    # Make a small change to test the update
                    notes_field = await page.query_selector('textarea[name="notes"]')
                    if notes_field:
                        await notes_field.fill("Test edit - fix verification")
                    
                    # Submit the form
                    submit_button = await page.query_selector('button[type="submit"]')
                    if submit_button:
                        await submit_button.click()
                        await page.wait_for_timeout(5000)
                        
                        # Check if we were redirected back to purchases list (success)
                        if 'purchases' in page.url and 'edit' not in page.url:
                            print("✅ Purchase edit successful - redirected to purchases list")
                            
                            # Look for success message
                            success_message = await page.query_selector('.alert-success')
                            if success_message:
                                message_text = await success_message.text_content()
                                print(f"✅ Success message: {message_text}")
                            else:
                                print("⚠️ No success message found, but redirect occurred")
                                
                        else:
                            print("❌ Form submission failed - still on edit page")
                            
                            # Check for error messages
                            error_message = await page.query_selector('.alert-danger')
                            if error_message:
                                error_text = await error_message.text_content()
                                print(f"❌ Error message: {error_text}")
                            
                            # Take screenshot of error
                            await page.screenshot(path="purchase_02_edit_error.png")
                            print("📸 Error screenshot saved: purchase_02_edit_error.png")
                    else:
                        print("❌ Submit button not found")
                else:
                    print("❌ Failed to reach edit page - possible server error")
                    
                    # Check for error page
                    page_content = await page.content()
                    if '500' in page_content or 'Internal Server Error' in page_content:
                        print("❌ 500 Internal Server Error detected")
                    
                    await page.screenshot(path="purchase_02_edit_error.png")
                    print("📸 Error screenshot saved: purchase_02_edit_error.png")
            else:
                print("❌ Edit button for purchase #02 not found")
                
                # Take screenshot of purchases page
                await page.screenshot(path="purchases_page.png")
                print("📸 Screenshot saved: purchases_page.png")
            
            # Step 6: Test other purchase records
            print("\n🔄 Step 6: Testing other purchase records...")
            
            # Go back to purchases page
            await page.goto("http://localhost:5000/purchases/", wait_until='networkidle')
            
            # Test editing purchase #01
            edit_button_01 = await page.query_selector('a[href*="/purchases/edit/01"]')
            if edit_button_01:
                print("✅ Testing purchase #01 edit...")
                await edit_button_01.click()
                await page.wait_for_timeout(2000)
                
                if 'edit/01' in page.url:
                    print("✅ Purchase #01 edit page accessible")
                    
                    # Try quick submit without changes
                    submit_button = await page.query_selector('button[type="submit"]')
                    if submit_button:
                        await submit_button.click()
                        await page.wait_for_timeout(3000)
                        
                        if 'purchases' in page.url and 'edit' not in page.url:
                            print("✅ Purchase #01 edit/submit successful")
                        else:
                            print("❌ Purchase #01 edit/submit failed")
                else:
                    print("❌ Purchase #01 edit page not accessible")
            
            print("\n🎯 Purchase edit fix testing completed!")
            
        except Exception as e:
            print(f"❌ Critical error during testing: {str(e)}")
            await page.screenshot(path="critical_error.png")
            
        finally:
            await browser.close()

if __name__ == "__main__":
    asyncio.run(test_purchase_edit_fix())
