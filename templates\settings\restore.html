{% extends "base.html" %}

{% block title %}Restore Data - Hospital Pharmacy Management System{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1><i class="bi bi-upload"></i> Restore System Data</h1>
    <a href="{{ url_for('settings.index') }}" class="btn btn-outline-secondary">
        <i class="bi bi-arrow-left"></i> Back to Settings
    </a>
</div>

<div class="row">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0"><i class="bi bi-upload"></i> Upload Backup File</h5>
            </div>
            <div class="card-body">
                <form method="POST" enctype="multipart/form-data" id="restoreForm">
                    <div class="mb-4">
                        <label for="backup_file" class="form-label">Select Backup File <span class="text-danger">*</span></label>
                        <input type="file" class="form-control" id="backup_file" name="backup_file" 
                               accept=".zip" required>
                        <div class="form-text">
                            Please select a ZIP backup file created by this system.
                        </div>
                    </div>
                    
                    <div class="alert alert-warning">
                        <h6><i class="bi bi-exclamation-triangle"></i> Important Warning</h6>
                        <ul class="mb-0">
                            <li><strong>This will overwrite all existing data</strong></li>
                            <li>Make sure you have a current backup before proceeding</li>
                            <li>The restore process cannot be undone</li>
                            <li>All users will need to log in again after restore</li>
                        </ul>
                    </div>
                    
                    <div class="form-check mb-3">
                        <input class="form-check-input" type="checkbox" id="confirmRestore" required>
                        <label class="form-check-label" for="confirmRestore">
                            I understand that this will overwrite all existing data and cannot be undone
                        </label>
                    </div>
                    
                    <div class="d-flex justify-content-end gap-2">
                        <a href="{{ url_for('settings.index') }}" class="btn btn-secondary">Cancel</a>
                        <button type="submit" class="btn btn-danger" id="restoreButton" disabled>
                            <i class="bi bi-upload"></i> Restore Data
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <div class="col-md-4">
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="card-title mb-0"><i class="bi bi-info-circle"></i> Restore Process</h5>
            </div>
            <div class="card-body">
                <div class="step mb-3">
                    <div class="d-flex align-items-center mb-2">
                        <div class="bg-primary text-white rounded-circle d-flex align-items-center justify-content-center me-2" style="width: 24px; height: 24px; font-size: 12px;">1</div>
                        <strong>Upload Backup</strong>
                    </div>
                    <p class="text-muted small mb-0">Select and upload your ZIP backup file</p>
                </div>
                
                <div class="step mb-3">
                    <div class="d-flex align-items-center mb-2">
                        <div class="bg-primary text-white rounded-circle d-flex align-items-center justify-content-center me-2" style="width: 24px; height: 24px; font-size: 12px;">2</div>
                        <strong>Validation</strong>
                    </div>
                    <p class="text-muted small mb-0">System validates backup file integrity</p>
                </div>
                
                <div class="step mb-3">
                    <div class="d-flex align-items-center mb-2">
                        <div class="bg-primary text-white rounded-circle d-flex align-items-center justify-content-center me-2" style="width: 24px; height: 24px; font-size: 12px;">3</div>
                        <strong>Data Replacement</strong>
                    </div>
                    <p class="text-muted small mb-0">Existing data is replaced with backup data</p>
                </div>
                
                <div class="step">
                    <div class="d-flex align-items-center mb-2">
                        <div class="bg-success text-white rounded-circle d-flex align-items-center justify-content-center me-2" style="width: 24px; height: 24px; font-size: 12px;">4</div>
                        <strong>Complete</strong>
                    </div>
                    <p class="text-muted small mb-0">System is restored and ready to use</p>
                </div>
            </div>
        </div>
        
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0"><i class="bi bi-shield-check"></i> Backup Requirements</h5>
            </div>
            <div class="card-body">
                <h6>Valid Backup Files Must:</h6>
                <ul class="small">
                    <li>Be in ZIP format</li>
                    <li>Contain valid JSON data files</li>
                    <li>Include backup metadata</li>
                    <li>Be created by this system</li>
                </ul>
                
                <h6 class="mt-3">Supported Data Types:</h6>
                <ul class="small">
                    <li>Medicines and inventory</li>
                    <li>Patients and consumption</li>
                    <li>Suppliers and purchases</li>
                    <li>Departments and stores</li>
                    <li>Inventory transfers</li>
                    <li>User accounts and history</li>
                </ul>
            </div>
        </div>
    </div>
</div>

<!-- Progress Modal -->
<div class="modal fade" id="progressModal" tabindex="-1" data-bs-backdrop="static" data-bs-keyboard="false">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title"><i class="bi bi-upload"></i> Restoring Data</h5>
            </div>
            <div class="modal-body text-center">
                <div class="spinner-border text-primary mb-3" role="status">
                    <span class="visually-hidden">Loading...</span>
                </div>
                <p>Please wait while your data is being restored...</p>
                <div class="alert alert-warning">
                    <strong>Do not close this window or navigate away!</strong>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    const confirmCheckbox = document.getElementById('confirmRestore');
    const restoreButton = document.getElementById('restoreButton');
    const restoreForm = document.getElementById('restoreForm');
    const fileInput = document.getElementById('backup_file');
    
    // Enable/disable restore button based on checkbox
    confirmCheckbox.addEventListener('change', function() {
        restoreButton.disabled = !this.checked;
    });
    
    // File validation
    fileInput.addEventListener('change', function() {
        const file = this.files[0];
        if (file) {
            if (!file.name.endsWith('.zip')) {
                alert('Please select a ZIP file.');
                this.value = '';
                return;
            }
            
            if (file.size > 50 * 1024 * 1024) { // 50MB limit
                alert('File size too large. Maximum size is 50MB.');
                this.value = '';
                return;
            }
        }
    });
    
    // Show progress modal on form submit
    restoreForm.addEventListener('submit', function(e) {
        if (!confirmCheckbox.checked) {
            e.preventDefault();
            alert('Please confirm that you understand the restore process.');
            return;
        }
        
        if (!fileInput.files[0]) {
            e.preventDefault();
            alert('Please select a backup file.');
            return;
        }
        
        // Show progress modal
        const progressModal = new bootstrap.Modal(document.getElementById('progressModal'));
        progressModal.show();
        
        // Disable form elements
        const formElements = restoreForm.querySelectorAll('input, button');
        formElements.forEach(element => {
            element.disabled = true;
        });
    });
});
</script>
{% endblock %}
