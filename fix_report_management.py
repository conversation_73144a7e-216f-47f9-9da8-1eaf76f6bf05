#!/usr/bin/env python3
"""
Script to fix Report Management issues:
1. Add ID numbering system (1,2,3,...) to all report table views and update database
2. Fix 'Current Stock' column in Low Stock Reports to match medicine management data
3. Make table header column names black font color for: Supplier Details and Purchase Details
4. Fix Consumption Report CSV Export: When filtering by department/date/medicine, export only the filtered records, not all records
"""

import json
import os
from datetime import datetime

def add_id_numbering_to_databases():
    """Add sequential ID numbering to all database files that need it"""
    
    # Files that need ID numbering
    database_files = [
        'data/consumption.json',
        'data/purchases.json', 
        'data/medicines.json',
        'data/patients.json',
        'data/suppliers.json',
        'data/departments.json',
        'data/stores.json',
        'data/transfers.json'
    ]
    
    for file_path in database_files:
        if os.path.exists(file_path):
            print(f"\n🔧 Processing {file_path}...")
            
            with open(file_path, 'r') as f:
                data = json.load(f)
            
            # Check if data needs ID renumbering
            needs_update = False
            current_ids = [item.get('id') for item in data if 'id' in item]
            
            # Check for non-sequential or non-numeric IDs
            try:
                numeric_ids = [int(id_val) for id_val in current_ids if str(id_val).isdigit()]
                if len(numeric_ids) != len(current_ids) or numeric_ids != list(range(1, len(numeric_ids) + 1)):
                    needs_update = True
            except:
                needs_update = True
            
            if needs_update:
                print(f"  ⚠️  Found non-sequential IDs, updating...")
                
                # Renumber all records sequentially
                for i, item in enumerate(data, 1):
                    old_id = item.get('id', 'Unknown')
                    item['id'] = str(i).zfill(2)  # Zero-padded 2-digit IDs
                    print(f"    Updated ID: {old_id} → {item['id']}")
                
                # Save updated data
                with open(file_path, 'w') as f:
                    json.dump(data, f, indent=2)
                
                print(f"  ✅ Updated {len(data)} records with sequential IDs")
            else:
                print(f"  ✅ IDs already sequential, no changes needed")

def fix_low_stock_current_stock():
    """Fix Current Stock column in Low Stock Reports to match medicine management data"""
    
    print(f"\n🔧 Checking Low Stock Report Current Stock calculation...")
    
    # Load required data
    with open('data/medicines.json', 'r') as f:
        medicines = json.load(f)
    
    with open('data/stores.json', 'r') as f:
        stores = json.load(f)
    
    # Calculate total stock for each medicine across all stores
    medicine_stock = {}
    for medicine in medicines:
        medicine_id = medicine['id']
        total_stock = 0
        
        # Sum stock from all stores
        for store in stores:
            inventory = store.get('inventory', {})
            stock = inventory.get(medicine_id, 0)
            total_stock += stock
        
        medicine_stock[medicine_id] = total_stock
        
        # Update medicine record with calculated total stock
        medicine['total_stock'] = total_stock
    
    # Save updated medicines data
    with open('data/medicines.json', 'w') as f:
        json.dump(medicines, f, indent=2)
    
    print(f"  ✅ Updated total stock calculations for {len(medicines)} medicines")
    
    # Verify low stock items
    low_stock_count = 0
    for medicine in medicines:
        total_stock = medicine.get('total_stock', 0)
        low_limit = medicine.get('low_stock_limit', 0)
        if total_stock <= low_limit:
            low_stock_count += 1
    
    print(f"  📊 Found {low_stock_count} medicines with low stock")

def main():
    """Main function to run all report management fixes"""
    
    print("🚀 Starting Report Management Fixes...")
    print("=" * 60)
    
    # Task 1: Add ID numbering system
    print("\n📋 TASK 1: Adding ID numbering system to all databases")
    add_id_numbering_to_databases()
    
    # Task 2: Fix Current Stock column in Low Stock Reports
    print("\n📊 TASK 2: Fixing Current Stock calculations")
    fix_low_stock_current_stock()
    
    print("\n" + "=" * 60)
    print("✅ All Report Management fixes completed successfully!")
    print("\n📝 SUMMARY:")
    print("  ✅ Added sequential ID numbering to all database tables")
    print("  ✅ Fixed Current Stock calculations in Low Stock Reports")
    print("  ⚠️  Table header styling and CSV export filtering require template updates")
    print("\n🔄 Next: Update templates for header styling and CSV export filtering")

if __name__ == "__main__":
    main()
