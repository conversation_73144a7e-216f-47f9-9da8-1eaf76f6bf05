{% extends "base.html" %}

{% block title %}Departments Management - Hospital Pharmacy Management System{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1><i class="bi bi-building"></i> Departments Management</h1>
    <div>
        <a href="{{ url_for('departments.add') }}" class="btn btn-primary">
            <i class="bi bi-plus-circle"></i> Add Department
        </a>
        <div class="btn-group" role="group">
            <button type="button" class="btn btn-outline-secondary dropdown-toggle" data-bs-toggle="dropdown">
                <i class="bi bi-download"></i> Export
            </button>
            <ul class="dropdown-menu">
                <li><a class="dropdown-item" href="#" onclick="exportTableToCSV('departmentsTable', 'departments')">
                    <i class="bi bi-file-earmark-spreadsheet"></i> Export CSV
                </a></li>
                <li><a class="dropdown-item" href="{{ url_for('departments.download_template') }}">
                    <i class="bi bi-file-earmark-arrow-down"></i> Download Template
                </a></li>
            </ul>
        </div>
        <button type="button" class="btn btn-outline-info" data-bs-toggle="modal" data-bs-target="#importModal">
            <i class="bi bi-upload"></i> Import
        </button>
    </div>
</div>

<!-- Search -->
<div class="row mb-3">
    <div class="col-md-6">
        <div class="input-group">
            <span class="input-group-text"><i class="bi bi-search"></i></span>
            <input type="text" class="form-control" id="searchInput" placeholder="Search departments...">
        </div>
    </div>
</div>

<!-- Departments Table -->
<div class="card">
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-hover" id="departmentsTable">
                <thead>
                    <tr>
                        <th>ID</th>
                        <th>Department Name</th>
                        <th>Responsible Person</th>
                        <th>Telephone</th>
                        <th>Store Status</th>
                        <th>Notes</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    {% for department in departments %}
                    <tr>
                        <td>{{ department.id }}</td>
                        <td><strong>{{ department.name }}</strong></td>
                        <td>{{ department.responsible_person or 'Not assigned' }}</td>
                        <td>
                            {% if department.telephone %}
                            <a href="tel:{{ department.telephone }}" class="text-decoration-none">
                                <i class="bi bi-telephone"></i> {{ department.telephone }}
                            </a>
                            {% else %}
                            <span class="text-muted">Not provided</span>
                            {% endif %}
                        </td>
                        <td>
                            <span class="badge bg-success">
                                <i class="bi bi-check-circle"></i> Store Created
                            </span>
                        </td>
                        <td>
                            {% set notes_text = department.notes or '' %}
                            {% if notes_text %}
                                {{ notes_text[:50] }}{% if notes_text|length > 50 %}...{% endif %}
                            {% else %}
                                <span class="text-muted">No notes</span>
                            {% endif %}
                        </td>
                        <td class="action-buttons">
                            <a href="{{ url_for('departments.edit', department_id=department.id) }}" 
                               class="btn btn-sm btn-outline-primary" title="Edit">
                                <i class="bi bi-pencil"></i>
                            </a>
                            <a href="{{ url_for('stores.index') }}?department={{ department.id }}" 
                               class="btn btn-sm btn-outline-info" title="View Store">
                                <i class="bi bi-box"></i>
                            </a>
                            <a href="{{ url_for('departments.delete', department_id=department.id) }}"
                               class="btn btn-sm btn-outline-danger" title="Delete"
                               {% if department.id == '01' %}disabled title="Cannot delete main department"{% endif %}
                               onclick="return confirmDelete('Are you sure you want to delete this department? This will also delete its associated store.')">
                                <i class="bi bi-trash"></i>
                            </a>
                        </td>
                    </tr>
                    {% else %}
                    <tr>
                        <td colspan="7" class="text-center text-muted">
                            <i class="bi bi-inbox"></i> No departments found. 
                            <a href="{{ url_for('departments.add') }}">Add the first department</a>.
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>
</div>

<!-- Import Modal -->
<div class="modal fade" id="importModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Import Departments</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="importForm" method="POST" enctype="multipart/form-data" action="{{ url_for('departments.index') }}">
                    <div class="mb-3">
                        <label for="csvFile" class="form-label">Select CSV File</label>
                        <input type="file" class="form-control" id="csvFile" name="csv_file" accept=".csv" required>
                        <div class="form-text">
                            Please use the template format. <a href="{{ url_for('departments.download_template') }}">Download template</a>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="submit" form="importForm" class="btn btn-primary">Import</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// Initialize search functionality
document.addEventListener('DOMContentLoaded', function() {
    searchTable('searchInput', 'departmentsTable');
});
</script>
{% endblock %}
