[{"id": "01", "source_store_id": "01", "destination_store_id": "04", "medicines": [{"medicine_id": "04", "quantity": 5}, {"medicine_id": "15", "quantity": 20}], "notes": "Transfer to Cardiology Store", "status": "completed", "created_at": "2025-07-11T20:35:53.517184", "transfer_date": "2025-07-25", "requested_by": "Pharmacist <PERSON>", "approved_by": "Medical Director", "updated_at": "2025-07-26T16:08:01.922790"}, {"id": "02", "source_store_id": "01", "destination_store_id": "05", "medicines": [{"medicine_id": "11", "quantity": 2}, {"medicine_id": "05", "quantity": 17}, {"medicine_id": "18", "quantity": 11}], "notes": "Transfer to Oncology Store", "status": "completed", "created_at": "2025-07-06T20:35:53.517199", "transfer_date": "2025-07-15", "requested_by": "Nurse <PERSON>", "approved_by": "Pharmacy Manager", "updated_at": "2025-07-26T16:08:01.922814"}, {"id": "03", "source_store_id": "01", "destination_store_id": "10", "medicines": [{"medicine_id": "33", "quantity": 1}, {"medicine_id": "19", "quantity": 17}], "notes": "Transfer to Psychiatry Store", "status": "completed", "created_at": "2025-07-23T20:35:53.517212", "transfer_date": "2025-07-03", "requested_by": "Dr. <PERSON>", "approved_by": "Medical Director", "updated_at": "2025-07-26T16:08:01.922824"}, {"id": "04", "source_store_id": "01", "destination_store_id": "10", "medicines": [{"medicine_id": "23", "quantity": 11}], "notes": "Transfer to Psychiatry Store", "status": "completed", "created_at": "2025-07-09T20:35:53.517223", "transfer_date": "2025-07-19", "requested_by": "Pharmacist <PERSON>", "approved_by": "Pharmacy Manager", "updated_at": "2025-07-26T16:08:01.922831"}, {"id": "05", "source_store_id": "01", "destination_store_id": "10", "medicines": [{"medicine_id": "48", "quantity": 18}], "notes": "Transfer to Psychiatry Store", "status": "completed", "created_at": "2025-07-13T20:35:53.517244", "transfer_date": "2025-07-11", "requested_by": "Nurse <PERSON>", "approved_by": "Chief Pharmacist", "updated_at": "2025-07-26T16:08:01.922845"}, {"id": "06", "source_store_id": "01", "destination_store_id": "05", "medicines": [{"medicine_id": "11", "quantity": 9}, {"medicine_id": "01", "quantity": 5}, {"medicine_id": "18", "quantity": 20}], "notes": "Transfer to Oncology Store", "status": "completed", "created_at": "2025-07-17T20:35:53.517256", "transfer_date": "2025-07-24", "requested_by": "Dr. <PERSON>", "approved_by": "Department Head", "updated_at": "2025-07-26T16:08:01.922856"}, {"id": "07", "source_store_id": "01", "destination_store_id": "07", "medicines": [{"medicine_id": "26", "quantity": 1}, {"medicine_id": "15", "quantity": 8}, {"medicine_id": "03", "quantity": 12}], "notes": "Transfer to Dermatology Store", "status": "completed", "created_at": "2025-07-16T20:35:53.517270", "transfer_date": "2025-07-02", "requested_by": "Pharmacist <PERSON>", "approved_by": "Department Head", "updated_at": "2025-07-26T16:10:33.097848"}, {"id": "08", "source_store_id": "01", "destination_store_id": "08", "medicines": [{"medicine_id": "23", "quantity": 9}, {"medicine_id": "01", "quantity": 6}], "notes": "Transfer to Neurology Store", "status": "completed", "created_at": "2025-07-12T20:35:53.517281", "transfer_date": "2025-07-02", "requested_by": "Nurse <PERSON>", "approved_by": "Department Head", "updated_at": "2025-07-26T16:08:01.922880"}, {"id": "09", "source_store_id": "01", "destination_store_id": "07", "medicines": [{"medicine_id": "48", "quantity": 6}, {"medicine_id": "49", "quantity": 3}, {"medicine_id": "41", "quantity": 10}], "notes": "Transfer to Surgery Store", "status": "completed", "created_at": "2025-07-13T20:35:53.517294", "transfer_date": "2025-07-03", "requested_by": "Dr. <PERSON>", "approved_by": "Chief Pharmacist", "updated_at": "2025-07-26T16:08:01.922890"}, {"source_store_id": "01", "destination_store_id": "10", "medicines": [{"medicine_id": "01", "quantity": 19}, {"medicine_id": "02", "quantity": 1}], "notes": "Automatic transfer due to store deletion: Emergency Department Store", "status": "completed", "created_at": "2025-07-25T19:23:57.817532", "id": "10", "transfer_date": "2025-07-08", "requested_by": "Dr. <PERSON>", "approved_by": "Pharmacy Manager", "updated_at": "2025-07-26T16:10:33.097989"}, {"source_store_id": "09", "destination_store_id": "01", "medicines": [{"medicine_id": "06", "quantity": 11}, {"medicine_id": "04", "quantity": 2}, {"medicine_id": "01", "quantity": 50}, {"medicine_id": "16", "quantity": 50}, {"medicine_id": "13", "quantity": 100}], "notes": "Automatic transfer due to store deletion: Intensive Care Unit Store", "status": "completed", "created_at": "2025-07-25T21:04:31.377502", "id": "11", "transfer_date": "2025-07-12", "requested_by": "<PERSON>", "approved_by": "Department Head", "updated_at": "2025-07-26T16:10:33.098056"}, {"source_store_id": "06", "destination_store_id": "01", "medicines": [{"medicine_id": "26", "quantity": 1}, {"medicine_id": "15", "quantity": 8}, {"medicine_id": "03", "quantity": 12}], "notes": "Automatic transfer due to store deletion: Dermatology Store", "status": "completed", "created_at": "2025-07-26T05:10:30.054013", "id": "12", "transfer_date": "2025-07-23", "requested_by": "<PERSON>", "approved_by": "Pharmacy Manager", "updated_at": "2025-07-26T16:10:33.098121"}, {"id": "13", "source_store_id": "05", "destination_store_id": "08", "medicines": [{"medicine_id": "08", "quantity": 12}, {"medicine_id": "10", "quantity": 21}, {"medicine_id": "20", "quantity": 13}], "transfer_date": "2025-06-09", "notes": "Transfer from Oncology Store to Neurology Store", "status": "in_transit", "requested_by": "Nurse <PERSON>", "approved_by": "Medical Director", "created_at": "2025-06-09T16:09:01.369767", "updated_at": "2025-07-26T16:09:01.369810"}, {"id": "14", "source_store_id": "08", "destination_store_id": "07", "medicines": [{"medicine_id": "22", "quantity": 13}, {"medicine_id": "27", "quantity": 29}, {"medicine_id": "49", "quantity": 43}], "transfer_date": "2025-06-28", "notes": "Transfer from Neurology Store to Surgery Store", "status": "in_transit", "requested_by": "Dr. <PERSON>", "approved_by": "Pharmacy Manager", "created_at": "2025-06-28T16:09:01.369841", "updated_at": "2025-07-26T16:09:01.369852"}, {"id": "15", "source_store_id": "06", "destination_store_id": "10", "medicines": [{"medicine_id": "28", "quantity": 6}, {"medicine_id": "39", "quantity": 50}, {"medicine_id": "26", "quantity": 9}], "transfer_date": "2025-06-18", "notes": "Transfer from Pediatrics Store to Psychiatry Store", "status": "pending", "requested_by": "<PERSON>", "approved_by": "Department Head", "created_at": "2025-06-18T16:09:01.369863", "updated_at": "2025-07-26T16:09:01.369872"}, {"id": "16", "source_store_id": "07", "destination_store_id": "05", "medicines": [{"medicine_id": "20", "quantity": 15}], "transfer_date": "2025-07-08", "notes": "Transfer from Surgery Store to Oncology Store", "status": "pending", "requested_by": "Dr. <PERSON>", "approved_by": "Medical Director", "created_at": "2025-07-08T16:09:01.369879", "updated_at": "2025-07-26T16:09:01.369887"}, {"id": "17", "source_store_id": "01", "destination_store_id": "09", "medicines": [{"medicine_id": "16", "quantity": 44}, {"medicine_id": "18", "quantity": 16}], "transfer_date": "2025-07-03", "notes": "Transfer from Main Pharmacy Store to Orthopedics Store", "status": "pending", "requested_by": "Dr. <PERSON>", "approved_by": "Chief Pharmacist", "created_at": "2025-07-03T16:09:01.369895", "updated_at": "2025-07-26T16:09:01.369902"}, {"id": "18", "source_store_id": "09", "destination_store_id": "01", "medicines": [{"medicine_id": "29", "quantity": 12}, {"medicine_id": "38", "quantity": 16}, {"medicine_id": "06", "quantity": 5}], "transfer_date": "2025-07-25", "notes": "Transfer from Orthopedics Store to Main Pharmacy Store", "status": "in_transit", "requested_by": "Dr. <PERSON>", "approved_by": "Medical Director", "created_at": "2025-07-25T16:09:01.369910", "updated_at": "2025-07-26T16:09:01.369917"}, {"id": "19", "source_store_id": "04", "destination_store_id": "07", "medicines": [{"medicine_id": "13", "quantity": 37}, {"medicine_id": "25", "quantity": 35}], "transfer_date": "2025-06-12", "notes": "Transfer from Cardiology Store to Surgery Store", "status": "completed", "requested_by": "Nurse <PERSON>", "approved_by": "Pharmacy Manager", "created_at": "2025-06-12T16:09:01.369924", "updated_at": "2025-07-26T16:09:01.369930"}, {"id": "20", "source_store_id": "10", "destination_store_id": "01", "medicines": [{"medicine_id": "06", "quantity": 12}, {"medicine_id": "36", "quantity": 8}], "transfer_date": "2025-06-14", "notes": "Transfer from Psychiatry Store to Main Pharmacy Store", "status": "in_transit", "requested_by": "Dr. <PERSON>", "approved_by": "Chief Pharmacist", "created_at": "2025-06-14T16:09:01.369937", "updated_at": "2025-07-26T16:09:01.369943"}, {"id": "21", "source_store_id": "01", "destination_store_id": "10", "medicines": [{"medicine_id": "16", "quantity": 29}], "transfer_date": "2025-06-09", "notes": "Transfer from Main Pharmacy Store to Psychiatry Store", "status": "in_transit", "requested_by": "Pharmacist <PERSON>", "approved_by": "Pharmacy Manager", "created_at": "2025-06-09T16:09:01.369949", "updated_at": "2025-07-26T16:09:01.369959"}, {"id": "22", "source_store_id": "09", "destination_store_id": "01", "medicines": [{"medicine_id": "46", "quantity": 50}, {"medicine_id": "37", "quantity": 48}], "transfer_date": "2025-06-11", "notes": "Transfer from Orthopedics Store to Main Pharmacy Store", "status": "in_transit", "requested_by": "<PERSON>", "approved_by": "Department Head", "created_at": "2025-06-11T16:09:01.369966", "updated_at": "2025-07-26T16:09:01.369974"}, {"id": "23", "source_store_id": "01", "destination_store_id": "09", "medicines": [{"medicine_id": "08", "quantity": 68}, {"medicine_id": "42", "quantity": 37}, {"medicine_id": "71", "quantity": 89}], "notes": "Transfer to Orthopedics Store", "status": "in_transit", "created_at": "2025-07-27T01:16:58.831578", "transfer_date": "2025-07-21", "requested_by": "Pharmacist <PERSON>", "approved_by": "Pharmacy Director"}, {"id": "24", "source_store_id": "01", "destination_store_id": "04", "medicines": [{"medicine_id": "37", "quantity": 19}, {"medicine_id": "99", "quantity": 27}], "notes": "Transfer to Cardiology Store", "status": "pending", "created_at": "2025-07-27T01:16:58.831952", "transfer_date": "2025-06-30", "requested_by": "Pharmacist <PERSON>", "approved_by": "Operations Manager"}, {"id": "25", "source_store_id": "01", "destination_store_id": "11", "medicines": [{"medicine_id": "10", "quantity": 38}, {"medicine_id": "75", "quantity": 37}, {"medicine_id": "74", "quantity": 72}], "notes": "Transfer to Dermatology Store", "status": "completed", "created_at": "2025-07-27T01:16:58.832283", "transfer_date": "2025-07-10", "requested_by": "Pharmacist <PERSON>", "approved_by": "Pharmacy Director"}, {"id": "26", "source_store_id": "01", "destination_store_id": "09", "medicines": [{"medicine_id": "58", "quantity": 85}, {"medicine_id": "96", "quantity": 43}, {"medicine_id": "02", "quantity": 24}], "notes": "Transfer to Orthopedics Store", "status": "in_transit", "created_at": "2025-07-27T01:16:58.832674", "transfer_date": "2025-06-04", "requested_by": "Pharmacist <PERSON>", "approved_by": "Operations Manager"}, {"id": "27", "source_store_id": "01", "destination_store_id": "11", "medicines": [{"medicine_id": "39", "quantity": 52}], "notes": "Transfer to Dermatology Store", "status": "pending", "created_at": "2025-07-27T01:16:58.833014", "transfer_date": "2025-06-30", "requested_by": "Pharmacist <PERSON>", "approved_by": "Department Head"}, {"id": "28", "source_store_id": "01", "destination_store_id": "06", "medicines": [{"medicine_id": "84", "quantity": 81}, {"medicine_id": "76", "quantity": 56}, {"medicine_id": "98", "quantity": 50}], "notes": "Transfer to Pediatrics Store", "status": "completed", "created_at": "2025-07-27T01:16:58.833331", "transfer_date": "2025-06-28", "requested_by": "Pharmacist <PERSON>", "approved_by": "Clinical Supervisor"}, {"id": "29", "source_store_id": "01", "destination_store_id": "14", "medicines": [{"medicine_id": "26", "quantity": 96}, {"medicine_id": "66", "quantity": 84}, {"medicine_id": "35", "quantity": 24}, {"medicine_id": "28", "quantity": 69}], "notes": "Transfer to Ophthalmology Store", "status": "completed", "created_at": "2025-07-27T01:16:58.833653", "transfer_date": "2025-06-16", "requested_by": "Pharmacist <PERSON>", "approved_by": "Department Head"}, {"id": "30", "source_store_id": "01", "destination_store_id": "10", "medicines": [{"medicine_id": "59", "quantity": 80}, {"medicine_id": "15", "quantity": 20}], "notes": "Transfer to Psychiatry Store", "status": "in_transit", "created_at": "2025-07-27T01:16:58.833967", "transfer_date": "2025-06-23", "requested_by": "Pharmacist <PERSON>", "approved_by": "Department Head"}, {"id": "31", "source_store_id": "01", "destination_store_id": "15", "medicines": [{"medicine_id": "40", "quantity": 27}, {"medicine_id": "65", "quantity": 48}, {"medicine_id": "100", "quantity": 53}, {"medicine_id": "91", "quantity": 36}], "notes": "Transfer to Endocrinology Store", "status": "in_transit", "created_at": "2025-07-27T01:16:58.834291", "transfer_date": "2025-07-23", "requested_by": "Pharmacist <PERSON>", "approved_by": "Operations Manager"}, {"id": "32", "source_store_id": "01", "destination_store_id": "06", "medicines": [{"medicine_id": "46", "quantity": 61}], "notes": "Transfer to Pediatrics Store", "status": "completed", "created_at": "2025-07-27T01:16:58.834615", "transfer_date": "2025-07-17", "requested_by": "Pharmacist <PERSON>", "approved_by": "Operations Manager"}, {"id": "33", "source_store_id": "01", "destination_store_id": "12", "medicines": [{"medicine_id": "36", "quantity": 92}, {"medicine_id": "23", "quantity": 40}, {"medicine_id": "89", "quantity": 61}, {"medicine_id": "45", "quantity": 96}], "notes": "Transfer to Orthopedics Store", "status": "in_transit", "created_at": "2025-07-27T01:16:58.834935", "transfer_date": "2025-07-01", "requested_by": "Pharmacist <PERSON>", "approved_by": "Department Head"}, {"id": "34", "source_store_id": "01", "destination_store_id": "11", "medicines": [{"medicine_id": "48", "quantity": 100}, {"medicine_id": "49", "quantity": 92}], "notes": "Transfer to Ophthalmology Store", "status": "in_transit", "created_at": "2025-07-27T01:16:58.835258", "transfer_date": "2025-05-29", "requested_by": "Pharmacist <PERSON>", "approved_by": "Operations Manager"}, {"id": "35", "source_store_id": "01", "destination_store_id": "10", "medicines": [{"medicine_id": "36", "quantity": 37}, {"medicine_id": "83", "quantity": 88}, {"medicine_id": "46", "quantity": 17}, {"medicine_id": "14", "quantity": 53}], "notes": "Transfer to Psychiatry Store", "status": "in_transit", "created_at": "2025-07-27T01:16:58.835599", "transfer_date": "2025-06-03", "requested_by": "Pharmacist <PERSON>", "approved_by": "Department Head"}, {"id": "36", "source_store_id": "01", "destination_store_id": "11", "medicines": [{"medicine_id": "34", "quantity": 11}, {"medicine_id": "62", "quantity": 94}, {"medicine_id": "102", "quantity": 67}], "notes": "Transfer to Dermatology Store", "status": "in_transit", "created_at": "2025-07-27T01:16:58.835997", "transfer_date": "2025-06-18", "requested_by": "Pharmacist <PERSON>", "approved_by": "Clinical Supervisor"}, {"id": "37", "source_store_id": "01", "destination_store_id": "09", "medicines": [{"medicine_id": "30", "quantity": 13}], "notes": "Transfer to Orthopedics Store", "status": "in_transit", "created_at": "2025-07-27T01:16:58.836371", "transfer_date": "2025-07-10", "requested_by": "Pharmacist <PERSON>", "approved_by": "Medical Director"}, {"id": "38", "source_store_id": "01", "destination_store_id": "14", "medicines": [{"medicine_id": "13", "quantity": 86}, {"medicine_id": "09", "quantity": 67}, {"medicine_id": "84", "quantity": 36}, {"medicine_id": "23", "quantity": 17}], "notes": "Transfer to Ophthalmology Store", "status": "in_transit", "created_at": "2025-07-27T01:17:37.037280", "transfer_date": "2025-06-22", "requested_by": "Pharmacist <PERSON>", "approved_by": "Operations Manager"}, {"id": "39", "source_store_id": "01", "destination_store_id": "15", "medicines": [{"medicine_id": "100", "quantity": 47}, {"medicine_id": "88", "quantity": 17}], "notes": "Transfer to Endocrinology Store", "status": "in_transit", "created_at": "2025-07-27T01:17:37.037447", "transfer_date": "2025-06-30", "requested_by": "Pharmacist <PERSON>", "approved_by": "Chief Pharmacist"}, {"id": "40", "source_store_id": "01", "destination_store_id": "05", "medicines": [{"medicine_id": "84", "quantity": 97}, {"medicine_id": "49", "quantity": 13}, {"medicine_id": "61", "quantity": 67}, {"medicine_id": "19", "quantity": 11}], "notes": "Transfer to Oncology Store", "status": "in_transit", "created_at": "2025-07-27T01:17:37.037626", "transfer_date": "2025-07-24", "requested_by": "Pharmacist <PERSON>", "approved_by": "Chief Pharmacist"}, {"id": "41", "source_store_id": "01", "destination_store_id": "04", "medicines": [{"medicine_id": "15", "quantity": 64}], "notes": "Transfer to Cardiology Store", "status": "pending", "created_at": "2025-07-27T01:17:37.037855", "transfer_date": "2025-06-01", "requested_by": "Pharmacist <PERSON>", "approved_by": "Department Head"}, {"id": "42", "source_store_id": "01", "destination_store_id": "07", "medicines": [{"medicine_id": "60", "quantity": 25}], "notes": "Transfer to Surgery Store", "status": "in_transit", "created_at": "2025-07-27T01:17:37.037985", "transfer_date": "2025-07-26", "requested_by": "Pharmacist <PERSON>", "approved_by": "Medical Director"}, {"id": "43", "source_store_id": "01", "destination_store_id": "13", "medicines": [{"medicine_id": "24", "quantity": 69}, {"medicine_id": "94", "quantity": 78}, {"medicine_id": "70", "quantity": 21}, {"medicine_id": "90", "quantity": 38}], "notes": "Transfer to Dermatology Store", "status": "pending", "created_at": "2025-07-27T01:17:37.038086", "transfer_date": "2025-07-01", "requested_by": "Pharmacist <PERSON>", "approved_by": "Medical Director"}, {"id": "44", "source_store_id": "01", "destination_store_id": "11", "medicines": [{"medicine_id": "99", "quantity": 100}, {"medicine_id": "29", "quantity": 38}, {"medicine_id": "59", "quantity": 34}, {"medicine_id": "84", "quantity": 62}], "notes": "Transfer to Gastroenterology Store", "status": "completed", "created_at": "2025-07-27T01:17:37.038213", "transfer_date": "2025-06-09", "requested_by": "Pharmacist <PERSON>", "approved_by": "Clinical Supervisor"}, {"id": "45", "source_store_id": "01", "destination_store_id": "10", "medicines": [{"medicine_id": "59", "quantity": 67}], "notes": "Transfer to Psychiatry Store", "status": "pending", "created_at": "2025-07-27T01:17:37.038294", "transfer_date": "2025-06-20", "requested_by": "Pharmacist <PERSON>", "approved_by": "Operations Manager"}, {"id": "46", "source_store_id": "01", "destination_store_id": "11", "medicines": [{"medicine_id": "99", "quantity": 62}, {"medicine_id": "74", "quantity": 21}], "notes": "Transfer to Endocrinology Store", "status": "completed", "created_at": "2025-07-27T01:17:37.038372", "transfer_date": "2025-07-06", "requested_by": "Pharmacist <PERSON>", "approved_by": "Clinical Supervisor"}, {"id": "47", "source_store_id": "01", "destination_store_id": "05", "medicines": [{"medicine_id": "10", "quantity": 85}, {"medicine_id": "86", "quantity": 93}, {"medicine_id": "03", "quantity": 75}], "notes": "Transfer to Oncology Store", "status": "pending", "created_at": "2025-07-27T01:17:37.038460", "transfer_date": "2025-07-25", "requested_by": "Pharmacist <PERSON>", "approved_by": "Operations Manager"}, {"id": "48", "source_store_id": "01", "destination_store_id": "11", "medicines": [{"medicine_id": "48", "quantity": 41}, {"medicine_id": "51", "quantity": 82}, {"medicine_id": "101", "quantity": 82}, {"medicine_id": "53", "quantity": 13}], "notes": "Transfer to Gastroenterology Store", "status": "pending", "created_at": "2025-07-27T01:17:37.038599", "transfer_date": "2025-06-04", "requested_by": "Pharmacist <PERSON>", "approved_by": "Operations Manager"}, {"id": "49", "source_store_id": "01", "destination_store_id": "14", "medicines": [{"medicine_id": "89", "quantity": 95}, {"medicine_id": "88", "quantity": 26}], "notes": "Transfer to Ophthalmology Store", "status": "pending", "created_at": "2025-07-27T01:17:37.038716", "transfer_date": "2025-07-21", "requested_by": "Pharmacist <PERSON>", "approved_by": "Medical Director"}, {"id": "50", "source_store_id": "01", "destination_store_id": "07", "medicines": [{"medicine_id": "58", "quantity": 63}, {"medicine_id": "24", "quantity": 13}, {"medicine_id": "44", "quantity": 56}], "notes": "Transfer to Surgery Store", "status": "pending", "created_at": "2025-07-27T01:17:37.038872", "transfer_date": "2025-06-25", "requested_by": "Pharmacist <PERSON>", "approved_by": "Medical Director"}, {"id": "51", "source_store_id": "01", "destination_store_id": "12", "medicines": [{"medicine_id": "91", "quantity": 62}, {"medicine_id": "31", "quantity": 42}, {"medicine_id": "78", "quantity": 81}, {"medicine_id": "14", "quantity": 87}], "notes": "Transfer to Orthopedics Store", "status": "in_transit", "created_at": "2025-07-27T01:17:37.039009", "transfer_date": "2025-06-21", "requested_by": "Pharmacist <PERSON>", "approved_by": "Pharmacy Director"}, {"id": "52", "source_store_id": "01", "destination_store_id": "09", "medicines": [{"medicine_id": "01", "quantity": 27}, {"medicine_id": "35", "quantity": 25}, {"medicine_id": "82", "quantity": 64}, {"medicine_id": "62", "quantity": 58}], "notes": "Transfer to Orthopedics Store", "status": "completed", "created_at": "2025-07-27T01:17:37.039115", "transfer_date": "2025-05-31", "requested_by": "Pharmacist <PERSON>", "approved_by": "Operations Manager"}, {"id": "53", "source_store_id": "01", "destination_store_id": "04", "medicines": [{"medicine_id": "22", "quantity": 22}, {"medicine_id": "09", "quantity": 13}], "transfer_date": "2025-06-18", "notes": "Transfer to Cardiology Store", "status": "pending", "requested_by": "Dr. <PERSON>", "approved_by": "Medical Director", "created_at": "2025-07-27T03:15:46.479328", "updated_at": "2025-07-27T03:15:46.479333"}, {"id": "54", "source_store_id": "01", "destination_store_id": "04", "medicines": [{"medicine_id": "26", "quantity": 29}, {"medicine_id": "11", "quantity": 17}, {"medicine_id": "35", "quantity": 23}], "transfer_date": "2025-07-18", "notes": "Transfer to Cardiology Store", "status": "pending", "requested_by": "Dr. <PERSON>", "approved_by": "Department Head", "created_at": "2025-07-27T03:15:46.479430", "updated_at": "2025-07-27T03:15:46.479432"}, {"id": "55", "source_store_id": "01", "destination_store_id": "04", "medicines": [{"medicine_id": "06", "quantity": 30}, {"medicine_id": "26", "quantity": 36}, {"medicine_id": "31", "quantity": 38}, {"medicine_id": "42", "quantity": 30}], "transfer_date": "2025-05-04", "notes": "Transfer to Cardiology Store", "status": "pending", "requested_by": "Pharmacist <PERSON>", "approved_by": "Operations Manager", "created_at": "2025-07-27T03:15:46.479537", "updated_at": "2025-07-27T03:15:46.479539"}, {"id": "56", "source_store_id": "01", "destination_store_id": "05", "medicines": [{"medicine_id": "16", "quantity": 13}, {"medicine_id": "22", "quantity": 50}], "transfer_date": "2025-06-10", "notes": "Transfer to Oncology Store", "status": "in_transit", "requested_by": "Pharmacist <PERSON>", "approved_by": "Pharmacy Manager", "created_at": "2025-07-27T03:15:46.479627", "updated_at": "2025-07-27T03:15:46.479629"}, {"id": "57", "source_store_id": "01", "destination_store_id": "05", "medicines": [{"medicine_id": "24", "quantity": 7}, {"medicine_id": "34", "quantity": 35}, {"medicine_id": "45", "quantity": 11}, {"medicine_id": "30", "quantity": 15}], "transfer_date": "2025-05-21", "notes": "Transfer to Oncology Store", "status": "completed", "requested_by": "Dr. <PERSON>", "approved_by": "Operations Manager", "created_at": "2025-07-27T03:15:46.479724", "updated_at": "2025-07-27T03:15:46.479726"}, {"id": "58", "source_store_id": "01", "destination_store_id": "05", "medicines": [{"medicine_id": "37", "quantity": 7}, {"medicine_id": "12", "quantity": 13}, {"medicine_id": "09", "quantity": 37}, {"medicine_id": "26", "quantity": 30}], "transfer_date": "2025-07-08", "notes": "Transfer to Oncology Store", "status": "in_transit", "requested_by": "<PERSON>", "approved_by": "Operations Manager", "created_at": "2025-07-27T03:15:46.479814", "updated_at": "2025-07-27T03:15:46.479816"}, {"id": "59", "source_store_id": "01", "destination_store_id": "06", "medicines": [{"medicine_id": "15", "quantity": 6}], "transfer_date": "2025-06-23", "notes": "Transfer to Pediatrics Store", "status": "completed", "requested_by": "Nurse <PERSON>", "approved_by": "Department Head", "created_at": "2025-07-27T03:15:46.479904", "updated_at": "2025-07-27T03:15:46.479907"}, {"id": "60", "source_store_id": "01", "destination_store_id": "06", "medicines": [{"medicine_id": "47", "quantity": 12}, {"medicine_id": "17", "quantity": 24}, {"medicine_id": "35", "quantity": 24}, {"medicine_id": "28", "quantity": 40}], "transfer_date": "2025-06-07", "notes": "Transfer to Pediatrics Store", "status": "in_transit", "requested_by": "Dr. <PERSON>", "approved_by": "Medical Director", "created_at": "2025-07-27T03:15:46.479999", "updated_at": "2025-07-27T03:15:46.480001"}, {"id": "61", "source_store_id": "01", "destination_store_id": "07", "medicines": [{"medicine_id": "32", "quantity": 22}, {"medicine_id": "09", "quantity": 50}], "transfer_date": "2025-06-01", "notes": "Transfer to Surgery Store", "status": "pending", "requested_by": "Pharmacist <PERSON>", "approved_by": "Chief Pharmacist", "created_at": "2025-07-27T03:15:46.480086", "updated_at": "2025-07-27T03:15:46.480088"}, {"id": "62", "source_store_id": "01", "destination_store_id": "07", "medicines": [{"medicine_id": "38", "quantity": 34}, {"medicine_id": "26", "quantity": 42}, {"medicine_id": "07", "quantity": 15}], "transfer_date": "2025-05-04", "notes": "Transfer to Surgery Store", "status": "pending", "requested_by": "Dr. <PERSON>", "approved_by": "Department Head", "created_at": "2025-07-27T03:15:46.480169", "updated_at": "2025-07-27T03:15:46.480172"}, {"id": "63", "source_store_id": "01", "destination_store_id": "07", "medicines": [{"medicine_id": "30", "quantity": 14}, {"medicine_id": "37", "quantity": 18}, {"medicine_id": "06", "quantity": 37}], "transfer_date": "2025-07-04", "notes": "Transfer to Surgery Store", "status": "in_transit", "requested_by": "Dr. <PERSON>", "approved_by": "Department Head", "created_at": "2025-07-27T03:15:46.480257", "updated_at": "2025-07-27T03:15:46.480259"}, {"id": "64", "source_store_id": "01", "destination_store_id": "08", "medicines": [{"medicine_id": "24", "quantity": 34}, {"medicine_id": "26", "quantity": 19}, {"medicine_id": "35", "quantity": 22}, {"medicine_id": "23", "quantity": 42}], "transfer_date": "2025-06-01", "notes": "Transfer to Neurology Store", "status": "pending", "requested_by": "Dr. <PERSON>", "approved_by": "Chief Pharmacist", "created_at": "2025-07-27T03:15:46.480343", "updated_at": "2025-07-27T03:15:46.480345"}, {"id": "65", "source_store_id": "01", "destination_store_id": "08", "medicines": [{"medicine_id": "04", "quantity": 45}, {"medicine_id": "12", "quantity": 32}, {"medicine_id": "45", "quantity": 12}], "transfer_date": "2025-05-27", "notes": "Transfer to Neurology Store", "status": "pending", "requested_by": "Dr. <PERSON>", "approved_by": "Department Head", "created_at": "2025-07-27T03:15:46.480426", "updated_at": "2025-07-27T03:15:46.480428"}, {"id": "66", "source_store_id": "01", "destination_store_id": "10", "medicines": [{"medicine_id": "23", "quantity": 32}, {"medicine_id": "05", "quantity": 18}, {"medicine_id": "47", "quantity": 14}], "transfer_date": "2025-05-15", "notes": "Transfer to Psychiatry Store", "status": "pending", "requested_by": "Dr. <PERSON>", "approved_by": "Pharmacy Manager", "created_at": "2025-07-27T03:15:46.480508", "updated_at": "2025-07-27T03:15:46.480510"}, {"id": "67", "source_store_id": "01", "destination_store_id": "10", "medicines": [{"medicine_id": "03", "quantity": 24}, {"medicine_id": "47", "quantity": 25}, {"medicine_id": "18", "quantity": 8}], "transfer_date": "2025-05-26", "notes": "Transfer to Psychiatry Store", "status": "completed", "requested_by": "Nurse <PERSON>", "approved_by": "Operations Manager", "created_at": "2025-07-27T03:15:46.480590", "updated_at": "2025-07-27T03:15:46.480596"}, {"source_store_id": "01", "destination_store_id": "02", "medicines": [{"medicine_id": "01", "quantity": 50}], "notes": "Test transfer with new requested by and approved by fields", "status": "completed", "requested_by": "Dr. <PERSON>", "approved_by": "Pharmacy Director", "id": "68", "created_at": "2025-07-27T03:53:20.042890"}]